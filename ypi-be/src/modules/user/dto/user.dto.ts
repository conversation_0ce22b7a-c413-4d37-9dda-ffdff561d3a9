import { ROLE } from '@core/enums/role.enum'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, TransformFnParams } from 'class-transformer'
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  MinLength,
  ValidateIf,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class GetUsersDTO extends PaginatedQueryParams {
  @ApiProperty({ type: ROLE })
  @IsEnum(ROLE)
  @IsNotEmpty()
  roleType: ROLE
}

export class CreateUserDTO {
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @IsNotEmpty()
  @ValidateIf((object, value) => object.roleType !== ROLE.INSPECTOR)
  password?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fullName: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @ValidateIf((object, value) => object.roleType !== ROLE.INSPECTOR)
  phoneCode?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @ValidateIf((object, value) => object.roleType !== ROLE.INSPECTOR)
  phoneNumber?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @ValidateIf((object, value) => object.roleType !== ROLE.INSPECTOR)
  company?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsUrl({ require_tld: false, require_protocol: true })
  @ValidateIf((object, value) => object.roleType === ROLE.ADMIN)
  callbackURL?: string

  @ApiProperty({ type: ROLE })
  @IsEnum(ROLE)
  @IsNotEmpty()
  roleType: ROLE

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  avatarUrl?: string
}

export class UpdateUserDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fullName: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  phoneCode?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  phoneNumber?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  company?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  avatarUrl?: string
}

export class ForgotPasswordDTO {
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsUrl({ require_tld: false, require_protocol: true })
  callbackURL: string
}

export class VerifyResetPasswordDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  token: string
}

export class NewPasswordDTO {
  @ApiProperty()
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @IsNotEmpty()
  password: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  token: string
}
