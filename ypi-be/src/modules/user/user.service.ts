import { ROLE } from '@core/enums/role.enum'
import { EMAIL_TEMPLATE, FE_URL } from '@core/enums/system.enum'
import { S3Service } from '@modules/aws/s3.service'
import { MailerService } from '@modules/mailer/mailer.service'
import { RoleService } from '@modules/role/role.service'
import {
  CreateUserDTO,
  ForgotPasswordDTO,
  GetUsersDTO,
  NewPasswordDTO,
  UpdateUserDTO,
  VerifyResetPasswordDTO,
} from '@modules/user/dto/user.dto'
import { User } from '@modules/user/user.entity'
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { randomBytes, scryptSync } from 'node:crypto'
import { FindManyOptions, ILike, IsNull, Repository } from 'typeorm'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { decodeToken, encodeToken } from '../../utils/functions/encrypt'
import { getLinkAsset } from '../../utils/functions/image'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
    private readonly roleService: RoleService,
    private readonly mailerService: MailerService,
    private readonly s3Service: S3Service,
  ) {}

  async createUser(
    payload: CreateUserDTO,
    avatar: Express.Multer.File,
  ): Promise<User> {
    const {
      password,
      roleType,
      email,
      fullName,
      phoneCode,
      phoneNumber,
      company,
      callbackURL,
      avatarUrl,
    } = payload

    const findEmail = await this.userRepo.findOne({
      where: { email, isDeleted: false, deletedAt: IsNull() },
    })

    if (findEmail) {
      throw new BadRequestException(MSG.USER_ALREADY_EXISTS)
    }

    const findRole = await this.roleService.getRoleValue(roleType.toUpperCase())

    if (!findRole) {
      throw new NotFoundException(MSG.ROLE_NOT_FOUND)
    }

    const nameParts = fullName.split(' ')
    const lastName = nameParts.pop()
    const firstName = nameParts.join(' ')
    let generatedPw = {
      passwordHash: 'none',
      passwordSalt: 'none',
    }
    if (password) {
      generatedPw = this.generatePassword(password)
    }

    const dtUser = await this.userRepo.save({
      ...generatedPw,
      company,
      email,
      firstName,
      lastName,
      fullName,
      phoneCode,
      phoneNumber,
      avatar: avatarUrl,
      role: findRole,
    })

    if (avatar) {
      const avatarLink = await this.uploadAvatar(avatar, { user: dtUser })
      console.log('avatarLink', avatarLink)
    }

    if (roleType !== ROLE.SUPER_ADMIN) {
      let loginInspector
      if (roleType === ROLE.INSPECTOR) {
        const tokenDate = new Date()
        const tokenTimestamp = Math.floor(tokenDate.getTime() / 1000)
        const token = encodeToken(
          `?userId=${dtUser.id}&timestamp=${tokenTimestamp}`,
        )
        loginInspector = `${FE_URL}/auth/sign-up?token=${token}&email=${email}`
        await this.userRepo.save({ id: dtUser.id, passwordChangeToken: token })
      }

      const hostUrl =
        process.env.NODE_ENV === 'production'
          ? 'https://api.carelaplus.com'
          : 'https://ypi-dev.thepineapple.io'

      await this.mailerService
        .sendMail(
          {
            to: email,
            subject: 'YPI - New user is registered',
            html: EMAIL_TEMPLATE.CREATE_USER,
          },
          {
            fullName: dtUser.fullName || nameParts,
            email,
            password: loginInspector ? undefined : password,
            loginLink: loginInspector ? loginInspector : callbackURL,
            hostUrl,
          },
        )
        .catch(console.error)
    }

    return dtUser
  }

  async updateUser(
    payload: UpdateUserDTO,
    { user }: LoggedUserDTO,
    userId: User['id'],
  ): Promise<User> {
    const { fullName, phoneCode, phoneNumber, company, avatarUrl } = payload

    const findUser = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['role'],
    })

    if (!findUser) {
      throw new NotFoundException(MSG.USER_NOT_FOUND)
    }

    if (
      user.role.value === ROLE.SUPER_ADMIN &&
      findUser.role.value === ROLE.SUPER_ADMIN &&
      user.id !== findUser.id
    ) {
      throw new BadRequestException(MSG.ROLE_NOT_ACCEPTED)
    }

    const nameParts = fullName.split(' ')
    const lastName = nameParts.pop()
    const firstName = nameParts.join(' ')
    return this.userRepo.save({
      id: userId,
      company,
      firstName,
      lastName,
      fullName,
      phoneCode,
      phoneNumber,
      avatar: avatarUrl,
    })
  }

  async deleteUser({ user }: LoggedUserDTO, userId: User['id']) {
    const findUser = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['role'],
    })

    if (!findUser) {
      throw new NotFoundException(MSG.USER_NOT_FOUND)
    }

    if (
      user.role.value === ROLE.SUPER_ADMIN &&
      findUser.role.value === ROLE.SUPER_ADMIN &&
      user.id !== findUser.id
    ) {
      throw new BadRequestException(MSG.ROLE_NOT_ACCEPTED)
    }

    return this.userRepo.update(userId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }

  async getUsers({ keyword, pageIndex, pageSize, roleType }: GetUsersDTO) {
    const [data, total] = await this.userRepo.findAndCount({
      ...pagingHandler(pageIndex, pageSize),
      select: {
        id: true,
        email: true,
        avatar: true,
        fullName: true,
        firstName: true,
        lastName: true,
        company: true,
        phoneCode: true,
        phoneNumber: true,
        isActive: true,
        createdAt: true,
      },
      relations: ['role'],
      where: {
        email: keyword ? ILike(`%${keyword}%`) : undefined,
        role: {
          value: roleType.toUpperCase(),
        },
        staff: IsNull(),
      },
      order: { createdAt: 'DESC' },
    } as FindManyOptions<User>)

    data.map(async (obj) => {
      obj.avatar = !obj.avatar?.startsWith('http')
        ? getLinkAsset(obj.avatar)
        : obj.avatar
      return obj
    })

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  async forgotPassword({ email, callbackURL }: ForgotPasswordDTO) {
    const findEmail = await this.userRepo.findOne({
      where: { email },
    })

    if (!findEmail) {
      throw new BadRequestException(MSG.USER_EMAIL_NOT_EXIST)
    }

    const tokenDate = new Date()
    const tokenTimestamp = Math.floor(tokenDate.getTime() / 1000)
    const token = encodeToken(
      `?userId=${findEmail.id}&timestamp=${tokenTimestamp}`,
    )
    await this.mailerService
      .sendMail(
        {
          to: email,
          subject: 'YPI - Request to reset password',
          html: EMAIL_TEMPLATE.FORGOT_PASSWORD,
        },
        {
          fullName: findEmail.fullName,
          resetPassLink: `${callbackURL}?token=${token}`,
        },
      )
      .catch(console.error)
    return this.userRepo.save({ id: findEmail.id, passwordChangeToken: token })
  }

  async verifyResetPassword({ token }: VerifyResetPasswordDTO) {
    const getToken = decodeToken(token)
    if (!getToken) {
      throw new BadRequestException(MSG.TOKEN_NOT_ACCEPTED)
    }
    const params = new URLSearchParams(getToken)
    const findToken = await this.userRepo.findOne({
      where: {
        id: params.get('userId'),
        passwordChangeToken: token,
      },
    })
    if (!findToken) {
      throw new BadRequestException(MSG.TOKEN_NOT_ACCEPTED)
    }
    return true
  }

  async newPassword({ token, password }: NewPasswordDTO) {
    const getToken = decodeToken(token)
    if (!getToken) {
      throw new BadRequestException(MSG.TOKEN_NOT_ACCEPTED)
    }
    const params = new URLSearchParams(getToken)
    const findToken = await this.userRepo.findOne({
      where: {
        id: params.get('userId'),
        passwordChangeToken: token,
      },
    })
    if (!findToken) {
      throw new BadRequestException(MSG.TOKEN_NOT_ACCEPTED)
    }
    const generatedPw = this.generatePassword(password)
    return this.userRepo.save({
      id: findToken.id,
      passwordChangeToken: null,
      ...generatedPw,
    })
  }

  async uploadAvatar(avatar: Express.Multer.File, { user }: LoggedUserDTO) {
    const prefix = `user/${user.id}/avatar`
    const dtUpload = await this.s3Service.processImageUpload(prefix, avatar)
    if (!dtUpload) {
      throw new BadRequestException(MSG.COULD_NOT_UPLOAD)
    }
    await this.userRepo.save({ id: user.id, avatar: dtUpload.link })
    let signedUrl
    if (dtUpload) {
      signedUrl = getLinkAsset(dtUpload.link)
    }
    return signedUrl
  }

  generatePassword = (password: string): TPasswordGenerateOutput => {
    const passwordSalt = randomBytes(16).toString('hex')
    const passwordHash = scryptSync(password, passwordSalt, 32).toString('hex')

    return {
      passwordHash,
      passwordSalt,
    }
  }

  comparePassword = ({
    password,
    passwordSalt,
    passwordHash,
  }: TPasswordCompareInput): boolean => {
    return (
      scryptSync(password, passwordSalt, 32).toString('hex') === passwordHash
    )
  }
}
