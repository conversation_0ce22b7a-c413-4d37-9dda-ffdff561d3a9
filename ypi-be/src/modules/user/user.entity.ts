import { BaseEntity } from '@core/databases/entities/base.entity'
import { Role } from '@modules/role/role.entity'
import { Staff } from '@modules/staff/staff.entity'
import { Token } from '@modules/token/token.entity'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm'

@Entity('user')
export class User extends BaseEntity {
  @Column()
  @Column({ nullable: true })
  username: string

  @Column({ nullable: true })
  email: string

  @Column({ nullable: true })
  company: string

  @Column({ nullable: true })
  phoneCode: string

  @Column({ nullable: true })
  phoneNumber: string

  @Column({ nullable: true })
  fullName: string

  @Column({ nullable: true })
  firstName: string

  @Column({ nullable: true })
  lastName: string

  @Column({ nullable: true })
  avatar: string

  @Column({ nullable: true })
  passwordSalt: string

  @Column({ nullable: true })
  passwordHash: string

  @Column({ nullable: true })
  passwordChangeToken: string

  @Column({ default: true })
  isActive: boolean

  @ManyToOne(() => Role, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'roleId' })
  role: Role

  @OneToMany(() => Token, (token) => token.user, { onDelete: 'SET NULL' })
  @JoinColumn()
  token: Token

  @OneToOne(() => Staff, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffId' })
  staff: Staff
}
