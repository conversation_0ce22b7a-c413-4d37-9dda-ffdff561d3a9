import { LoggedUser } from '@core/decorators/auth.decorator'
import { SuperAdminGuard } from '@modules/auth/guards/role.guard'
import {
  CreateUserDTO,
  GetUsersDTO,
  UpdateUserDTO,
} from '@modules/user/dto/user.dto'
import { User } from '@modules/user/user.entity'
import { UserService } from '@modules/user/user.service'
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiOperation } from '@nestjs/swagger'
import { LoggedUserDTO } from '../../commons/dto/logged'

@Controller('user')
export class UserController {
  constructor(private userService: UserService) {}

  @Post('/')
  @UseInterceptors(FileInterceptor('avatar'))
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: 'Create user' })
  async createUser(
    @Body() body: CreateUserDTO,
    @UploadedFile() avatar: Express.Multer.File,
  ): Promise<any> {
    await this.userService.createUser(body, avatar)
    return {
      data: true,
    }
  }

  @Post('/upload/avatar')
  @UseInterceptors(FileInterceptor('avatar'))
  async uploadAvatar(
    @UploadedFile() avatar: Express.Multer.File,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtUser = await this.userService.uploadAvatar(avatar, user)
    return {
      data: dtUser,
    }
  }

  @Get('/')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: 'Get users' })
  async getUsers(@Query() body: GetUsersDTO): Promise<any> {
    const dtUser = await this.userService.getUsers(body)
    return {
      data: dtUser,
    }
  }

  @Put('/:userId')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: 'Update user' })
  async updateUser(
    @Param('userId') userId: User['id'],
    @Body() body: UpdateUserDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtUser = await this.userService.updateUser(body, user, userId)
    return {
      data: dtUser,
    }
  }

  @Delete('/:userId')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: 'Delete user' })
  async deleteUser(
    @Param('userId') userId: User['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.userService.deleteUser(user, userId)
    return {
      data: true,
    }
  }
}
