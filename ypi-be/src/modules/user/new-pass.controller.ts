import { Body, Controller, Post } from '@nestjs/common'
import { Public } from '@core/decorators/public.decorator'
import { NewPasswordDTO } from '@modules/user/dto/user.dto'
import { UserService } from '@modules/user/user.service'
import { ApiOperation } from '@nestjs/swagger'

@Controller('new-password')
export class NewPassController {
  constructor(private userService: UserService) {
  }

  @Post('/')
  @ApiOperation({ summary: 'Create new password' })
  @Public()
  async newPassword(
    @Body() body: NewPasswordDTO,
  ): Promise<any> {
    await this.userService.newPassword(body)
    return {
      data: true,
    }
  }
}
