import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { RoleModule } from '@modules/role/role.module'
import { Role } from '@modules/role/role.entity'
import { UserService } from '@modules/user/user.service'
import { User } from '@modules/user/user.entity'
import { UserController } from '@modules/user/user.controller'
import { ForgotPassController } from '@modules/user/forgot-pass.controller'
import { NewPassController } from '@modules/user/new-pass.controller'
import { MailerModule } from '@modules/mailer/mailer.module'
import { AwsModule } from '@modules/aws/aws.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role]),
    RoleModule,
    MailerModule,
    AwsModule,
  ],
  controllers: [UserController, ForgotPassController, NewPassController],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {
}
