import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { Public } from '@core/decorators/public.decorator'
import { ForgotPasswordDTO, VerifyResetPasswordDTO } from '@modules/user/dto/user.dto'
import { UserService } from '@modules/user/user.service'
import { ApiOperation } from '@nestjs/swagger'

@Controller('forgot-password')
export class ForgotPassController {
  constructor(private userService: UserService) {
  }

  @Post('/')
  @ApiOperation({ summary: 'Forgot password' })
  @Public()
  async forgotPassword(
    @Body() body: ForgotPasswordDTO,
  ): Promise<any> {
    await this.userService.forgotPassword(body)
    return {
      data: true,
    }
  }

  @Get('/verify')
  @ApiOperation({ summary: 'Verify reset password' })
  @Public()
  async verifyResetPassword(
    @Query() body: VerifyResetPasswordDTO,
  ): Promise<any> {
    await this.userService.verifyResetPassword(body)
    return {
      data: true,
    }
  }
}
