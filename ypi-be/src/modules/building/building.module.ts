import { BuildingController } from '@modules/building/building.controller'
import { Building } from '@modules/building/building.entity'
import { BuildingService } from '@modules/building/building.service'
import { CustomerModule } from '@modules/customer/customer.module'
import { forwardRef, Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

@Module({
  imports: [
    TypeOrmModule.forFeature([Building]),
    forwardRef(() => CustomerModule),
  ],
  providers: [BuildingService],
  controllers: [BuildingController],
  exports: [BuildingService],
})
export class BuildingModule {}
