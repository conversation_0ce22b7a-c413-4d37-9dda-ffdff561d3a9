import { ROLE } from '@core/enums/role.enum'
import { Building } from '@modules/building/building.entity'
import {
  CreateBuildingDTO,
  UpdateBuildingDTO,
} from '@modules/building/dto/building.dto'
import { Customer } from '@modules/customer/customer.entity'
import { CustomerService } from '@modules/customer/customer.service'
import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { LoggedUserDTO } from 'src/commons/dto/logged'
import { In, Repository } from 'typeorm'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class BuildingService {
  constructor(
    @InjectRepository(Building)
    public buildingRepo: Repository<Building>,
    private readonly customerService: CustomerService,
  ) {}

  async createBuilding(payload: CreateBuildingDTO): Promise<Building> {
    const { name } = payload
    return this.buildingRepo.save({ name })
  }

  async getAllowBuildings(loggedUser: LoggedUserDTO) {
    const { user } = loggedUser
    if ([ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(user.role.value as ROLE)) {
      return this.buildingRepo.find()
    }

    const customers = await this.customerService.customerRepo.find({
      where: {
        managedBy: {
          id: user.id,
        },
      },
      relations: ['building'],
    })

    const buildingIds = [
      ...new Set(
        customers
          .map((customer: Customer) => customer.building?.id)
          .filter((id) => id),
      ),
    ]

    if (!buildingIds.length) {
      return []
    }

    return this.buildingRepo.findBy({
      id: In(buildingIds),
    })
  }

  async updateBuilding(
    payload: UpdateBuildingDTO,
    buildingId: Building['id'],
  ): Promise<Building> {
    const { name } = payload

    const findBuilding = await this.buildingRepo.findOne({
      where: { id: buildingId },
    })

    if (!findBuilding) {
      throw new NotFoundException(MSG.BUILDING_NOT_FOUND)
    }
    return this.buildingRepo.save({
      id: buildingId,
      name,
    })
  }

  async deleteBuilding(buildingId: Building['id']) {
    const findBuilding = await this.buildingRepo.findOne({
      where: { id: buildingId },
    })

    if (!findBuilding) {
      throw new NotFoundException(MSG.BUILDING_NOT_FOUND)
    }

    return this.buildingRepo.update(buildingId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
