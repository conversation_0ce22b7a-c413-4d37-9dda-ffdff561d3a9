import { LoggedUser } from '@core/decorators/auth.decorator'
import { JwtAuthGuard } from '@modules/auth/guards/jwt.guard'
import { PermitAdminGuard } from '@modules/auth/guards/role.guard'
import { Building } from '@modules/building/building.entity'
import { BuildingService } from '@modules/building/building.service'
import {
  CreateBuildingDTO,
  UpdateBuildingDTO,
} from '@modules/building/dto/building.dto'
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { LoggedUserDTO } from 'src/commons/dto/logged'

@Controller('building')
export class BuildingController {
  constructor(private buildingService: BuildingService) {}

  @Post('/')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Create building' })
  async createBuilding(@Body() body: CreateBuildingDTO): Promise<any> {
    await this.buildingService.createBuilding(body)
    return {
      data: true,
    }
  }

  @Get('/allow')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get allow buildings' })
  async getAllowBuildings(@LoggedUser() user: LoggedUserDTO): Promise<any> {
    const dtBuilding = await this.buildingService.getAllowBuildings(user)
    return {
      data: dtBuilding,
    }
  }

  @Put('/:buildingId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Update building' })
  async updateBuilding(
    @Param('buildingId') buildingId: Building['id'],
    @Body() body: UpdateBuildingDTO,
  ): Promise<any> {
    await this.buildingService.updateBuilding(body, buildingId)
    return {
      data: true,
    }
  }

  @Delete('/:buildingId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Delete building' })
  async deleteBuilding(
    @Param('buildingId') buildingId: Building['id'],
  ): Promise<any> {
    await this.buildingService.deleteBuilding(buildingId)
    return {
      data: true,
    }
  }
}
