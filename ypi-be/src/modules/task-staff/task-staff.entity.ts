import { BaseEntity } from '@core/databases/entities/base.entity'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'
import { Task } from '@modules/task/task.entity'
import { Staff } from '@modules/staff/staff.entity'

@Entity('task-staff')
export class TaskStaff extends BaseEntity {
  @ManyToOne(() => Task, (task) => task.staffs)
  @JoinColumn({ name: 'taskId' })
  task: Task

  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffId' })
  staff: Staff
}
