import { ASSIGN_TASK_STATUS } from '@core/enums/assign-task.enum'
import { DEFECT_TYPE } from '@core/enums/defect.enum'
import { TASK_STATUS } from '@core/enums/task.enum'
import { Defect } from '@modules/defect/defect.entity'
import { Staff } from '@modules/staff/staff.entity'
import { TaskStaff } from '@modules/task-staff/task-staff.entity'
import { Task } from '@modules/task/task.entity'
import { TaskService } from '@modules/task/task.service'
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import {
  Between,
  Equal,
  FindManyOptions,
  FindOneOptions,
  FindOptionsWhere,
  In,
  LessThanOrEqual,
  Not,
  Repository,
} from 'typeorm'
import { MSG } from '../../utils/messages/common'
import { AssignStaffService } from './assign-staff/assign-staff.service'
import { AssignTask } from './assign-task.entity'
import {
  AssignStaffDTO,
  AssignTaskDTO,
  ConfirmCorrectedTaskDTO,
  SetLabSampleTakenDateDTO,
  UpdateAssignStaffDTO,
  UpdateAssignTaskDTO,
} from './dto/assign-task.dto'

@Injectable()
export class AssignTaskService {
  constructor(
    @InjectRepository(AssignTask)
    public assignTaskRepo: Repository<AssignTask>,
    @InjectRepository(Task)
    private taskRepo: Repository<Task>,
    @InjectRepository(Defect)
    private defectRepo: Repository<Defect>,
    @InjectRepository(TaskStaff)
    private taskStaffRepo: Repository<TaskStaff>,
    @InjectRepository(Staff)
    private staffRepo: Repository<Staff>,
    private readonly assignStaffService: AssignStaffService,
    private readonly taskService: TaskService,
  ) {}

  async assignTask(body: AssignTaskDTO): Promise<boolean> {
    const assignTask = await this.assignTaskRepo.findOne({
      where: {
        startAt: Between(
          moment(body?.startAt).toDate(),
          moment(body?.endAt).toDate(),
        ),
        status: ASSIGN_TASK_STATUS.PRE_INSPECTION,
      },
    })
    if (assignTask) {
      throw new BadRequestException(MSG.TASK_ALREADY_EXISTS)
    }

    // const code = await this.generateUniqueCode()

    const task = await this.assignTaskRepo.save({
      tankId: body?.tankId,
      startAt: moment(body?.startAt).toDate(),
      endAt: moment(body?.endAt).toDate(),
      phase: '1',
      status: ASSIGN_TASK_STATUS.PRE_INSPECTION,
    })

    const dtDefect = await this.defectRepo.find({
      where: { type: DEFECT_TYPE.DEFAULT },
    })

    if (dtDefect && dtDefect.length) {
      for (const defect of dtDefect) {
        const dtTask = await this.taskRepo.save({
          defect: {
            id: defect.id,
          },
          assignTask: {
            id: task?.id,
          },
          extra: !!defect.qty,
        })
        await this.addStaffsToTask(body?.staffs, dtTask)
      }
    }

    await this.assignStaffService.createAssignStaff({
      assignTaskId: task?.id,
      staffs: body?.staffs,
    })

    return true
  }

  async updateAssignTask(
    taskId: AssignTask['id'],
    body: UpdateAssignTaskDTO,
  ): Promise<boolean> {
    let assignTask = await this.assignTaskRepo.findOne({
      where: { id: taskId },
    })
    if (!assignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    assignTask = await this.assignTaskRepo.findOne({
      where: {
        startAt: Between(
          moment(body?.startAt).toDate(),
          moment(body?.endAt).toDate(),
        ),
        status: ASSIGN_TASK_STATUS.PRE_INSPECTION,
        id: Not(Equal(taskId)),
      },
    })
    if (assignTask) {
      throw new BadRequestException(MSG.TASK_ALREADY_EXISTS)
    }

    await this.assignTaskRepo.update(taskId, {
      startAt: moment(body?.startAt).toDate(),
      endAt: moment(body?.endAt).toDate(),
    })

    await this.assignStaffService.createAssignStaff({
      assignTaskId: taskId,
      staffs: body?.staffs,
    })

    // Update task
    await this.taskService.updateTaskAfterAssign(taskId, body?.staffs)

    return true
  }

  async addStaffsToTask(staffs: string[], task: Task) {
    const dtStaff = await this.staffRepo.find()
    const staffIds = dtStaff.map((item) => item?.id)
    const addStaffIds = []
    for (const staffId of staffs) {
      if (staffIds.includes(staffId)) {
        addStaffIds.push(staffId)
      }
    }
    if (addStaffIds.length) {
      for (const staffId of addStaffIds) {
        await this.taskStaffRepo.save({
          staff: {
            id: staffId,
          },
          task: {
            id: task.id,
          },
        })
      }
    }
  }

  // Assign task phase: 2
  async assignTaskRectify(body: AssignStaffDTO): Promise<boolean> {
    if (!body || !body.startAt || !body.endAt) {
      throw new BadRequestException(MSG.DATE_NOT_VALID)
    }
    if (!moment(body.startAt).isSameOrAfter(body.endAt)) {
      throw new BadRequestException(MSG.DATE_NOT_VALID)
    }

    let assignTask = await this.assignTaskRepo.findOne({
      where: {
        parent: { id: body?.taskId },
        status: ASSIGN_TASK_STATUS.PREPARE,
        startAt: Between(
          moment(body.startAt).toDate(),
          moment(body.endAt).toDate(),
        ),
      },
    } as FindOneOptions<AssignTask>)
    if (assignTask) {
      throw new BadRequestException(MSG.TASK_ALREADY_EXISTS)
    }

    assignTask = await this.assignTaskRepo.findOne({
      where: { id: body?.taskId },
      relations: ['children'],
    })
    if (!assignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }
    if (assignTask?.children?.length) {
      throw new BadRequestException(MSG.TASK_ALREADY_EXISTS)
    }

    // --- NEW LOGIC: Always save all defects in the payload ---
    if (!assignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }
    const task = await this.assignTaskRepo.save({
      tankId: assignTask.tankId,
      parent: { id: assignTask.id },
      startAt: moment(body.startAt).toDate(),
      endAt: moment(body.endAt).toDate(),
      code: assignTask.code,
      phase: '2',
      status: ASSIGN_TASK_STATUS.PREPARE,
      estimatedValue:
        typeof body.estimatedValue === 'number' ? body.estimatedValue : 0,
      acceptRectify: body.acceptRectify,
    })

    if (Array.isArray(body.defects) && body.defects.length) {
      // Add staff
      let staffs = []
      for (const defect of body.defects) {
        if (Array.isArray(defect.staffs)) {
          staffs = [...staffs, ...defect.staffs]
        }
      }
      staffs = [...new Set(staffs)]
      await this.assignStaffService.createAssignStaff({
        assignTaskId: task.id,
        staffs: staffs,
      })

      // Update assign_task phase: 1
      const assignTaskNew = await this.assignTaskRepo.findOneBy({
        id: assignTask.id,
      })
      if (assignTaskNew) {
        await this.assignTaskRepo.save({
          ...assignTaskNew,
          children: [{ id: task.id }],
        })
      }

      // --- NEW LOGIC: For each defect, create a new Task in phase 2 if not found in phase 1 ---
      for (const defect of body.defects) {
        // Defensive: check assignTask and task existence
        if (!assignTask || !task) {
          throw new NotFoundException(MSG.TASK_NOT_FOUND)
        }
        // Try to find the task in phase 1
        const phase1Task = await this.taskRepo.findOne({
          where: {
            assignTask: { id: assignTask.id },
            defect: { id: defect.defectId },
          },
        })
        // If not found, just create a new Task for this defect in phase 2
        const newTask = await this.taskRepo.save({
          defect: { id: defect.defectId },
          assignTask: { id: task.id },
          status: TASK_STATUS.RECTIFY,
          qty: phase1Task && phase1Task.qty ? phase1Task.qty : undefined,
          size: phase1Task && phase1Task.size ? phase1Task.size : undefined,
          extra:
            phase1Task && typeof phase1Task.extra === 'boolean'
              ? phase1Task.extra
              : false,
        })

        // Only add staff if task was created successfully
        if (newTask && newTask.id) {
          try {
            const staffsToAssign = Array.isArray(defect.staffs)
              ? defect.staffs
              : []
            await this.taskService.addStaffsToTask(staffsToAssign, newTask.id)
          } catch (error) {
            console.error(
              `Failed to assign staff to task ${newTask.id}:`,
              error,
            )
          }
        }
      }
    }

    return true
  }

  // Update assign task phase: 2
  async updateAssignTaskRectify(
    taskId: AssignTask['id'],
    body: UpdateAssignStaffDTO,
  ): Promise<boolean> {
    if (!body || !body.startAt || !body.endAt) {
      throw new BadRequestException(MSG.DATE_NOT_VALID)
    }
    if (!moment(body.startAt).isSameOrAfter(body.endAt)) {
      throw new BadRequestException(MSG.DATE_NOT_VALID)
    }

    let assignTask = await this.assignTaskRepo.findOne({
      where: {
        id: taskId,
        status: In([ASSIGN_TASK_STATUS.PREPARE, ASSIGN_TASK_STATUS.UNRESOLVED]),
      },
    })
    if (!assignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    assignTask = await this.assignTaskRepo.findOne({
      where: {
        startAt: Between(
          moment(body.startAt).toDate(),
          moment(body.endAt).toDate(),
        ),
        status: In([ASSIGN_TASK_STATUS.PREPARE, ASSIGN_TASK_STATUS.UNRESOLVED]),
        id: Not(Equal(taskId)),
      },
    })
    if (assignTask) {
      throw new BadRequestException(MSG.DUPPLICATE_TASK_TIME_IN_TANK)
    }

    // Check defectIds
    let defectIds = []
    if (body?.defects?.length) {
      defectIds = body?.defects.map((item) => item?.defectId)
    }
    const tasks = await this.taskService.getTaskByDefectIds(
      assignTask?.id,
      defectIds,
    )
    if (!tasks?.length) {
      throw new NotFoundException(MSG.DEFECT_NOT_FOUND)
    }

    await this.assignTaskRepo.update(taskId, {
      startAt: moment(body.startAt).toDate(),
      endAt: moment(body.endAt).toDate(),
      estimatedValue:
        typeof body.estimatedValue === 'number' ? body.estimatedValue : 0,
      acceptRectify: body.acceptRectify,
      status: ASSIGN_TASK_STATUS.PREPARE,
    })

    if (body?.defects?.length) {
      // Add staff
      let staffs = []
      for (const defect of body?.defects) {
        staffs = [...staffs, ...defect?.staffs]
      }
      staffs = [...new Set(staffs)]
      await this.assignStaffService.createAssignStaff({
        assignTaskId: taskId,
        staffs: staffs,
      })

      // Update task
      await this.taskService.updateTaskAfterAssignStaff(taskId, body?.defects)
    }

    return true
  }

  async setLabSampleTakenDate(
    taskId: AssignTask['id'],
    { datetime }: SetLabSampleTakenDateDTO,
  ): Promise<boolean> {
    await this.assignTaskRepo.update(taskId, {
      sampleTakenDate: datetime,
    })

    return true
  }

  async confirmCorrectedTask(payload: ConfirmCorrectedTaskDTO): Promise<any> {
    if (!payload || !payload.assignTaskId) {
      throw new BadRequestException(MSG.TASK_NOT_FOUND)
    }
    const { assignTaskId } = payload
    let assignTask = await this.assignTaskRepo.findOne({
      where: {
        id: assignTaskId,
        isConfirmed: true,
        phase: '2',
      },
    } as FindOneOptions<AssignTask>)

    if (assignTask) {
      throw new BadRequestException(MSG.TASK_HAS_BEEN_CONFIRMED)
    }

    assignTask = await this.assignTaskRepo.findOne({
      where: {
        id: assignTaskId,
        status: ASSIGN_TASK_STATUS.COMPLETED,
        phase: '2',
      },
    } as FindOneOptions<AssignTask>)

    if (!assignTask) {
      throw new BadRequestException(MSG.TASK_HAS_NOT_BEEN_COMPLETED)
    }

    // Defensive: check assignTask.id before using
    if (!assignTask.id) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }
    // Create new phase 3
    await this.assignTaskRepo.save({
      parent: {
        id: assignTask.id,
      },
      tank: {
        id: assignTask.tankId,
      },
      code: assignTask.code,
      status: ASSIGN_TASK_STATUS.CONFIRMED,
      phase: '3',
      startAt: new Date(),
      endAt: new Date(),
    })

    // Update phase 2
    await this.assignTaskRepo.save({
      id: assignTask.id,
      isConfirmed: true,
    })

    return true
  }

  async deleteAssignTask(taskId: AssignTask['id']) {
    let findAssignTask = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .where('assignTask.id = :assignTaskId', { assignTaskId: taskId })
      .getOne()
    if (!findAssignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    // Check assign task is allowed to be deleted
    findAssignTask = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .where('assignTask.id = :assignTaskId', { assignTaskId: taskId })
      .andWhere('assignTask.status = :assignTaskStatus', {
        assignTaskStatus: ASSIGN_TASK_STATUS.PRE_INSPECTION,
      })
      .andWhere('assignTask.isStaffSubmitted = :isStaffSubmitted', {
        isStaffSubmitted: false,
      })
      .getOne()
    if (!findAssignTask) {
      throw new BadRequestException(MSG.TASK_CANNOT_BE_DELETED)
    }

    return this.assignTaskRepo.update(taskId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }

  // Length 8
  async generateUniqueCode() {
    let code = 0
    do {
      code = Math.floor(10000000 + Math.random() * 90000000)
    } while (
      await this.assignTaskRepo.findOne({
        where: { code: `${code}` },
        select: ['id'],
      })
    )
    return code
  }

  async scheduleCheckTaskUnresolved() {
    const assignTasks = await this.assignTaskRepo.find({
      where: {
        status: In([ASSIGN_TASK_STATUS.UNRESOLVED]),
        tasks: {
          status: In([TASK_STATUS.UN_COMPLETED, TASK_STATUS.RECTIFY]),
        },
        endAt: LessThanOrEqual(new Date()),
      },
      relations: ['tasks'],
    } as FindManyOptions<AssignTask>)
    if (assignTasks.length) {
      let result
      for (const assignTask of assignTasks) {
        result = assignTask.tasks.map((task) => task.id)
      }
      if (result.length) {
        await this.taskRepo.update(
          {
            id: In(result),
          } as FindOptionsWhere<Task>,
          {
            status: TASK_STATUS.UN_COMPLETED,
          },
        )
      }
    }
    return true
  }
}
