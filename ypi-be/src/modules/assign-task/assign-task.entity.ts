import { BaseEntity } from '@core/databases/entities/base.entity'
import { ASSIGN_TASK_STATUS } from '@core/enums/assign-task.enum'
import { AssignStaff } from '@modules/assign-task/assign-staff/assign-staff.entity'
import { File } from '@modules/file/file.entity'
import { Staff } from '@modules/staff/staff.entity'
import { Tank } from '@modules/tank/tank.entity'
import { Task } from '@modules/task/task.entity'
import { User } from '@modules/user/user.entity'
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm'

@Entity('assign_task')
export class AssignTask extends BaseEntity {
  @ManyToOne(() => Tank, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'tankId' })
  tank: Tank
  @Column({ nullable: true })
  tankId?: string

  @Column({ type: 'timestamptz', nullable: true })
  startAt: Date

  @Column({ type: 'timestamptz', nullable: true })
  endAt: Date

  @OneToMany(() => AssignStaff, (assignStaff) => assignStaff.assignTask)
  @JoinColumn({ name: 'staffs' })
  staffs: AssignStaff[]

  @Column({ nullable: true })
  code: string

  @Column({ nullable: true })
  phase: string

  @Column({
    type: 'enum',
    enum: ASSIGN_TASK_STATUS,
    default: null,
  })
  status: ASSIGN_TASK_STATUS

  @Column({ type: 'boolean', default: true })
  acceptRectify: boolean

  @Column({ type: 'real', default: 0 })
  estimatedValue: number

  @ManyToOne(() => AssignTask, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'parentId' })
  parent: AssignTask

  @OneToMany(() => AssignTask, (assignTask) => assignTask.parent)
  @JoinColumn()
  children: AssignTask[]

  @OneToMany(() => Task, (task) => task.assignTask)
  @JoinColumn({ name: 'tasks' })
  tasks: Task[]

  @Column({ type: 'boolean', default: false })
  isStaffSubmitted: boolean

  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffSubmittedBy' })
  staffSubmittedBy: Staff

  @Column({ type: 'boolean', default: false })
  isInspectorSubmitted: boolean

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'inspectorSubmittedBy' })
  inspectorSubmittedBy: User

  @Column({ type: 'timestamptz', nullable: true })
  inspectorSubmittedDate: Date

  @Column({ nullable: true })
  customerSignature: string

  @Column({ type: 'timestamptz', nullable: true })
  customerSignatureDate: Date

  @Column({ type: 'timestamptz', nullable: true })
  completedDate: Date

  @Column({ type: 'timestamptz', nullable: true })
  sampleTakenDate: Date

  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'receiveSignatureBy' })
  receiveSignatureBy: Staff

  @Column({ type: 'boolean', default: false })
  isConfirmed: boolean

  @OneToMany(() => File, (file) => file.assignTask, { onDelete: 'SET NULL' })
  @JoinColumn()
  labReports: File[]

  @Column({ type: 'timestamptz', nullable: true })
  nextDueDate: Date
}
