import { AdminGuard } from '@modules/auth/guards/role.guard'
import {
  Body,
  Controller,
  Delete,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common'
import { <PERSON>ron, CronExpression } from '@nestjs/schedule'
import { ApiOperation } from '@nestjs/swagger'
import { AssignTask } from './assign-task.entity'
import { AssignTaskService } from './assign-task.service'
import {
  AssignStaffDTO,
  AssignTaskDTO,
  ConfirmCorrectedTaskDTO,
  SetLabSampleTakenDateDTO,
  UpdateAssignStaffDTO,
  UpdateAssignTaskDTO,
} from './dto/assign-task.dto'

@Controller('assign-task')
export class AssignTaskController {
  constructor(private readonly assignTaskService: AssignTaskService) {}

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Assign task' })
  async assignTask(
    @Body() body: AssignTaskDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.assignTaskService.assignTask(body)
    return { data: true }
  }

  @Put('/:taskId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update task' })
  async updateAssignTask(
    @Param('taskId') taskId: AssignTask['id'],
    @Body() body: UpdateAssignTaskDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.assignTaskService.updateAssignTask(taskId, body)
    return { data: true }
  }

  @Post('/rectify')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Assign task rectify' })
  async assignTaskRectify(
    @Body() body: AssignStaffDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.assignTaskService.assignTaskRectify(body)
    return { data: true }
  }

  @Post('/rectify/confirm')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Confirm the corrected task' })
  async confirmCorrectedTask(
    @Body() body: ConfirmCorrectedTaskDTO,
  ): Promise<any> {
    await this.assignTaskService.confirmCorrectedTask(body)
    return { data: true }
  }

  @Put('/rectify/:taskId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update assign task rectify' })
  async updateAssignTaskRectify(
    @Param('taskId') taskId: AssignTask['id'],
    @Body() body: UpdateAssignStaffDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.assignTaskService.updateAssignTaskRectify(taskId, body)
    return { data: true }
  }

  @Put('/complete/:taskId/lab-sample-taken')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update assign task date of lab sample taken' })
  async setLabSampleTakenDate(
    @Param('taskId') taskId: AssignTask['id'],
    @Body() body: SetLabSampleTakenDateDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.assignTaskService.setLabSampleTakenDate(taskId, body)
    return { data: true }
  }

  @Delete('/:taskId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Delete assign task' })
  async deleteAssignTask(
    @Param('taskId') taskId: AssignTask['id'],
  ): Promise<any> {
    await this.assignTaskService.deleteAssignTask(taskId)
    return {
      data: true,
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async scheduleCheckTaskUnresolved(): Promise<any> {
    console.log('@@@: scheduleCheckTaskUnresolved')
    await this.assignTaskService.scheduleCheckTaskUnresolved()
    return {
      data: true,
    }
  }
}
