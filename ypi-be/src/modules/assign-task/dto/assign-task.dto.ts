import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator'

export class AssignTaskDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  tankId: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  parentId?: string

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  startAt: Date

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  endAt: Date

  @ApiProperty()
  @ArrayNotEmpty()
  @IsArray()
  @IsString({ each: true })
  @IsUUID('4', { each: true })
  staffs: string[]
}

export class UpdateAssignTaskDTO {
  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  startAt: Date

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  endAt: Date

  @ApiProperty()
  @ArrayNotEmpty()
  @IsArray()
  @IsString({ each: true })
  @IsUUID('4', { each: true })
  staffs: string[]
}

export class AssignStaffDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  taskId: string

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  startAt: Date

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  endAt: Date

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  estimatedValue?: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  acceptRectify: boolean

  @ApiProperty()
  @ArrayNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AssignStaffDefectDTO)
  defects: AssignStaffDefectDTO[]
}

export class AssignStaffDefectDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  defectId: string

  @ApiProperty()
  // @ArrayNotEmpty()
  @IsArray()
  @IsString({ each: true })
  @IsUUID('4', { each: true })
  staffs: string[]
}

export class UpdateAssignStaffDTO {
  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  startAt: Date

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  endAt: Date

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  estimatedValue?: number

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  acceptRectify: boolean

  @ApiProperty()
  @ArrayNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AssignStaffDefectDTO)
  defects: AssignStaffDefectDTO[]
}

export class SetLabSampleTakenDateDTO {
  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  datetime: Date
}

export class ConfirmCorrectedTaskDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string
}
