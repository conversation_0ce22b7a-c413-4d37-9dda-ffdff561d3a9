import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm'
import { BaseEntity } from '@core/databases/entities/base.entity'
import { Staff } from '@modules/staff/staff.entity'
import { AssignTask } from '@modules/assign-task/assign-task.entity'

@Entity('assign_staff')
export class AssignStaff extends BaseEntity {
  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffId' })
  staff: Staff
  @Column({ nullable: true })
  staffId?: string

  @ManyToOne(() => AssignTask, (assignTask) => assignTask.staffs)
  @JoinColumn({ name: 'assignTaskId' })
  assignTask: AssignTask
  @Column({ nullable: true })
  assignTaskId?: string
}
