import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateAssignStaffDTO } from '../dto/assign-staff.dto'
import { AssignStaff } from './assign-staff.entity'
import { AssignTask } from '../assign-task.entity'
import { MSG } from '../../../utils/messages/common'

@Injectable()
export class AssignStaffService {
  constructor(
    @InjectRepository(AssignStaff)
    public assignStaffRepo: Repository<AssignStaff>,
  ) {}

  async createAssignStaff(body: CreateAssignStaffDTO): Promise<boolean> {
    const assignStaffs = await this.assignStaffRepo.find({
      where: { assignTaskId: body?.assignTaskId },
    })
    const staffIds = assignStaffs.map((item) => item?.staffId)

    // Add staff
    const addStaffIds = []
    for (const staffId of body?.staffs) {
      if (!staffIds.includes(staffId)) {
        addStaffIds.push(staffId)
      }
    }
    const promiseSaves = []
    for (const staffId of addStaffIds) {
      promiseSaves.push(
        this.assignStaffRepo.save({
          staffId: staffId,
          assignTaskId: body?.assignTaskId,
        }),
      )
    }
    await Promise.all(promiseSaves)

    // Remove staff
    const removeStaffIds = []
    for (const staffId of staffIds) {
      if (!body?.staffs.includes(staffId)) {
        removeStaffIds.push(staffId)
      }
    }
    const promiseRemoves = []
    for (const staffId of removeStaffIds) {
      promiseRemoves.push(this.deleteStaffByIdAndTaskId(staffId, body?.assignTaskId))
    }
    await Promise.all(promiseRemoves)

    return true
  }

  async deleteStaffByIdAndTaskId(staffId: AssignStaff['id'], taskId: AssignTask['id']) {
    const assignStaff = await this.assignStaffRepo.findOne({
      where: { staffId, assignTaskId: taskId },
    })
    if (!assignStaff) {
      throw new NotFoundException(MSG.ASSIGN_STAFF_NOT_FOUND)
    }

    return this.assignStaffRepo.update(assignStaff?.id, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
