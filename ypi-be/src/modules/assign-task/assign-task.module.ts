import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { Defect } from '@modules/defect/defect.entity'
import { Task } from '@modules/task/task.entity'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { TaskStaff } from '@modules/task-staff/task-staff.entity'
import { Staff } from '@modules/staff/staff.entity'
import { AssignTaskService } from './assign-task.service'
import { AssignTaskController } from './assign-task.controller'
import { AssignStaffModule } from './assign-staff/assign-staff.module'
import { TaskModule } from '@modules/task/task.module'

@Module({
  imports: [TypeOrmModule.forFeature([AssignTask, Task, Defect, TaskStaff, Staff]), AssignStaffModule, TaskModule],
  controllers: [AssignTaskController],
  providers: [AssignTaskService],
  exports: [AssignTaskService],
})
export class AssignTaskModule {
}
