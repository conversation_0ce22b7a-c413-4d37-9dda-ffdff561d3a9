import { Agent } from '@modules/agent/agent.entity'
import { AgentModule } from '@modules/agent/agent.module'
import { Building } from '@modules/building/building.entity'
import { BuildingModule } from '@modules/building/building.module'
import { CustomerController } from '@modules/customer/customer.controller'
import { Customer } from '@modules/customer/customer.entity'
import { CustomerService } from '@modules/customer/customer.service'
import { InCharge } from '@modules/in-charge/in-charge.entity'
import { InChargeModule } from '@modules/in-charge/in-charge.module'
import { forwardRef, Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, Building, InCharge, Agent]),
    forwardRef(() => BuildingModule),
    InChargeModule,
    forwardRef(() => AgentModule),
  ],
  controllers: [CustomerController],
  providers: [CustomerService],
  exports: [CustomerService],
})
export class CustomerModule {}
