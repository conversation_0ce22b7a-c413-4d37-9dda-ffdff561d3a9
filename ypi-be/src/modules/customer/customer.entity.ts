import { BaseEntity } from '@core/databases/entities/base.entity'
import { Agent } from '@modules/agent/agent.entity'
import { Building } from '@modules/building/building.entity'
import { User } from '@modules/user/user.entity'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm'

@Entity('customer')
export class Customer extends BaseEntity {
  @Column()
  name: string

  @Column({ nullable: true })
  pubNumber: string

  @Column({ nullable: true })
  pcNumber: string

  @Column({ nullable: true })
  email: string

  @Column({ nullable: true })
  phoneCode: string

  @Column({ nullable: true })
  phoneNumber: string

  @Column({ nullable: true })
  buildingCategory: string

  @Column({ nullable: true })
  nameBuilding: string

  @Column({ nullable: true })
  officePhoneCode: string

  @Column({ nullable: true })
  officePhoneNumber: string

  @ManyToOne(() => Building, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'buildingId' })
  building: Building

  @OneToOne(() => Agent, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'agentId' })
  agent: Agent

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'managedBy' })
  managedBy: User

  @Column({ default: true })
  isActive: boolean

  @OneToMany(() => Agent, (agent) => agent.customer)
  agents: Agent[]
}
