import { ROLE } from '@core/enums/role.enum'
import { AGENT_STATUS } from '@core/enums/system.enum'
import { AgentService } from '@modules/agent/agent.service'
import { BuildingService } from '@modules/building/building.service'
import { Customer } from '@modules/customer/customer.entity'
import {
  CreateCustomerDTO,
  GetCustomersDTO,
  UpdateActiveCustomerDTO,
  UpdateCustomerDTO,
} from '@modules/customer/dto/customer.dto'
import { InChargeService } from '@modules/in-charge/in-charge.service'
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import _ from 'lodash'
import { Brackets, FindOneOptions, Repository } from 'typeorm'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(Customer)
    public customerRepo: Repository<Customer>,
    @Inject(forwardRef(() => BuildingService))
    private readonly buildingService: BuildingService,
    private readonly inChargeService: InChargeService,
    @Inject(forwardRef(() => AgentService))
    private readonly agentService: AgentService,
  ) {}

  async createCustomer(
    payload: CreateCustomerDTO,
    { user }: LoggedUserDTO,
  ): Promise<Customer> {
    const {
      pubNumber,
      phoneCode,
      phoneNumber,
      officePhoneCode,
      officePhoneNumber,
      buildingId,
      agent,
      ...rest
    } = payload

    // Validate input data
    if (!pubNumber?.trim()) {
      throw new BadRequestException('PUB number cannot be empty')
    }
    if (!phoneCode?.trim() || !phoneNumber?.trim()) {
      throw new BadRequestException('Phone code and phone number are required')
    }
    if (!officePhoneCode?.trim() || !officePhoneNumber?.trim()) {
      throw new BadRequestException(
        'Office phone code and phone number are required',
      )
    }

    // Check for existing customer with same pubNumber, phone, or email
    const findCustomer = await this.customerRepo
      .createQueryBuilder('customer')
      .where(
        '(customer.pubNumber = :pubNumber OR CONCAT(customer.phoneCode, customer.phoneNumber) = :phone OR customer.email = :email)',
        {
          pubNumber: pubNumber.trim(),
          phone: `${phoneCode.trim()}${phoneNumber.trim()}`,
          email: rest.email.toLowerCase().trim(),
        },
      )
      .getOne()

    if (findCustomer) {
      if (findCustomer.pubNumber === pubNumber.trim()) {
        throw new BadRequestException(
          'Customer with this PUB number already exists',
        )
      }
      if (
        `${findCustomer.phoneCode}${findCustomer.phoneNumber}` ===
        `${phoneCode.trim()}${phoneNumber.trim()}`
      ) {
        throw new BadRequestException(
          'Customer with this phone number already exists',
        )
      }
      if (findCustomer.email === rest.email.toLowerCase().trim()) {
        throw new BadRequestException('Customer with this email already exists')
      }
      throw new BadRequestException(MSG.CUSTOMER_ALREADY_EXISTS)
    }

    let building = null
    if (buildingId) {
      const findBuilding = await this.buildingService.buildingRepo.findOne({
        where: { id: buildingId },
      })

      if (!findBuilding) {
        throw new NotFoundException(MSG.BUILDING_NOT_FOUND)
      }
      building = { id: buildingId }
    }

    let dtAgent
    if (agent && !_.isEmpty(agent)) {
      const {
        phoneCode,
        phoneNumber,
        postalCode,
        blockNo,
        inChargeId,
        inChargeCategory,
        ...rest
      } = agent

      let inCharge = null
      if (inChargeId) {
        const findInCharge = await this.inChargeService.inChargeRepo.findOne({
          where: { id: inChargeId },
        })

        if (!findInCharge) {
          throw new NotFoundException(MSG.IN_CHARGE_NOT_FOUND)
        }
        inCharge = { id: inChargeId }
      }

      dtAgent = await this.agentService.agentRepo.save({
        ...rest,
        phoneCode,
        phoneNumber,
        postalCode,
        blockNo,
        inChargeCategory,
        inCharge,
      })
    }

    return this.customerRepo.save({
      ...rest,
      pubNumber,
      phoneCode,
      phoneNumber,
      officePhoneCode,
      officePhoneNumber,
      building,
      agent: dtAgent ? { id: dtAgent.id } : null,
      managedBy: {
        id: user.id,
      },
    })
  }

  async getCustomers(
    { keyword, building, agent, pageIndex, pageSize }: GetCustomersDTO,
    loggedUser: LoggedUserDTO,
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const { user } = loggedUser

    const queryBuilder = this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.building', 'building')
      .leftJoinAndSelect('customer.agent', 'agent')
      .leftJoinAndSelect('agent.inCharge', 'inCharge')

    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(user.role.value as ROLE)) {
      queryBuilder
        .leftJoin('customer.managedBy', 'managedBy')
        .where('managedBy.id = :userId', { userId: user.id })
    }

    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.email ILIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('customer.name ILIKE :keyword', {
              keyword: `%${keyword}%`,
            })
            .orWhere('customer.pubNumber ILIKE :keyword', {
              keyword: `%${keyword}%`,
            })
        }),
      )
    }
    if (building && building.length) {
      queryBuilder.andWhere('building.id IN (:...building)', { building })
    }
    if (agent) {
      if (agent === AGENT_STATUS.TRUE) {
        queryBuilder.andWhere('agent.id IS NOT NULL')
      } else {
        queryBuilder.andWhere('agent.id IS NULL')
      }
    }
    queryBuilder.orderBy('customer.name', 'ASC')
    queryBuilder.skip(skip).take(take)
    const [data, total] = await queryBuilder.getManyAndCount()

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  async getCustomer(customerId: Customer['id']): Promise<Customer> {
    return this.customerRepo.findOne({
      where: { id: customerId },
      relations: ['building', 'agent', 'agent.inCharge'],
    })
  }

  async updateCustomer(
    payload: UpdateCustomerDTO,
    customerId: Customer['id'],
    { user }: LoggedUserDTO,
  ): Promise<Customer> {
    const {
      pubNumber,
      phoneCode,
      phoneNumber,
      officePhoneCode,
      officePhoneNumber,
      buildingId,
      agent,
      ...rest
    } = payload

    // Validate input data
    if (!customerId?.trim()) {
      throw new BadRequestException('Customer ID is required')
    }
    if (!pubNumber?.trim()) {
      throw new BadRequestException('PUB number cannot be empty')
    }
    if (!phoneCode?.trim() || !phoneNumber?.trim()) {
      throw new BadRequestException('Phone code and phone number are required')
    }
    if (!officePhoneCode?.trim() || !officePhoneNumber?.trim()) {
      throw new BadRequestException(
        'Office phone code and phone number are required',
      )
    }

    // Check for existing customer with same pubNumber, phone, or email (excluding current customer)
    let findCustomer = await this.customerRepo
      .createQueryBuilder('customer')
      .where(
        '(customer.pubNumber = :pubNumber OR CONCAT(customer.phoneCode, customer.phoneNumber) = :phone OR customer.email = :email)',
        {
          pubNumber: pubNumber.trim(),
          phone: `${phoneCode.trim()}${phoneNumber.trim()}`,
          email: rest.email.toLowerCase().trim(),
        },
      )
      .andWhere('customer.id != :customerId', { customerId })
      .leftJoinAndSelect('customer.agent', 'agent')
      .getOne()
    if (findCustomer) {
      if (findCustomer.pubNumber === pubNumber.trim()) {
        throw new BadRequestException(
          'Another customer with this PUB number already exists',
        )
      }
      if (
        `${findCustomer.phoneCode}${findCustomer.phoneNumber}` ===
        `${phoneCode.trim()}${phoneNumber.trim()}`
      ) {
        throw new BadRequestException(
          'Another customer with this phone number already exists',
        )
      }
      if (findCustomer.email === rest.email.toLowerCase().trim()) {
        throw new BadRequestException(
          'Another customer with this email already exists',
        )
      }
      throw new BadRequestException(MSG.CUSTOMER_ALREADY_EXISTS)
    }

    findCustomer = await this.customerRepo.findOne({
      where: {
        id: customerId,
        managedBy: {
          id: user.id,
        },
      },
      relations: ['agent'],
    } as FindOneOptions<Customer>)
    if (!findCustomer) {
      throw new NotFoundException(MSG.CUSTOMER_NOT_FOUND)
    }

    let building = null
    if (buildingId) {
      const findBuilding = await this.buildingService.buildingRepo.findOne({
        where: { id: buildingId },
      })
      if (!findBuilding) {
        throw new NotFoundException(MSG.BUILDING_NOT_FOUND)
      }
      building = { id: buildingId }
    }

    let dtAgent
    if (agent && !_.isEmpty(agent)) {
      const {
        id,
        phoneCode,
        phoneNumber,
        postalCode,
        blockNo,
        inChargeId,
        inChargeCategory,
        ...rest
      } = agent

      if (id) {
        const findAgent = await this.agentService.agentRepo.findOne({
          where: { id: id },
        })

        if (!findAgent) {
          throw new NotFoundException(MSG.AGENT_NOT_FOUND)
        }
      }

      let inCharge = null
      if (inChargeId) {
        const findInCharge = await this.inChargeService.inChargeRepo.findOne({
          where: { id: inChargeId },
        })

        if (!findInCharge) {
          throw new NotFoundException(MSG.IN_CHARGE_NOT_FOUND)
        }
        inCharge = { id: inChargeId }
      }

      dtAgent = await this.agentService.agentRepo.save({
        ...rest,
        id: id,
        phoneCode,
        phoneNumber,
        postalCode,
        blockNo,
        inChargeCategory,
        inCharge,
      })
    }

    // Case: agent already exists in the customer but request empty agent
    if (findCustomer.agent && !agent && _.isEmpty(agent)) {
      await this.agentService.agentRepo.delete(findCustomer.agent.id)
    }

    await this.customerRepo.save({
      ...rest,
      id: customerId,
      pubNumber,
      phoneCode,
      phoneNumber,
      officePhoneCode,
      officePhoneNumber,
      building,
      agent: dtAgent ? { id: dtAgent.id } : null,
    })

    return await this.customerRepo.findOne({
      where: {
        id: customerId,
      },
      relations: ['agent', 'building'],
    })
  }

  async updateActiveCustomer(
    customerId: Customer['id'],
    body: UpdateActiveCustomerDTO,
    { user }: LoggedUserDTO,
  ): Promise<boolean> {
    const customer = await this.customerRepo.findOne({
      where: {
        id: customerId,
        managedBy: {
          id: user.id,
        },
      },
    } as FindOneOptions<Customer>)
    if (!customer) {
      throw new NotFoundException(MSG.CUSTOMER_NOT_FOUND)
    }

    await this.customerRepo.update(customerId, {
      isActive: body?.isActive,
    })

    return true
  }

  async deleteCustomer(customerId: Customer['id'], { user }: LoggedUserDTO) {
    const findCustomer = await this.customerRepo.findOne({
      where: {
        id: customerId,
        managedBy: {
          id: user.id,
        },
      },
    } as FindOneOptions<Customer>)
    if (!findCustomer) {
      throw new NotFoundException(MSG.CUSTOMER_NOT_FOUND)
    }

    return this.customerRepo.update(customerId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
