import { AGENT_STATUS } from '@core/enums/system.enum'
import { CreateAgentDTO, UpdateAgentDTO } from '@modules/agent/dto/agent.dto'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, TransformFnParams, Type } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class CreateCustomerDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  pubNumber: string

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  officePhoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  officePhoneNumber: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  buildingId?: string

  @ApiPropertyOptional({ type: CreateAgentDTO })
  @ValidateNested({ each: true })
  @Type(() => CreateAgentDTO)
  @IsOptional()
  agent?: CreateAgentDTO

  @ApiProperty()
  @IsString()
  @IsOptional()
  buildingCategory?: string
}

export class UpdateCustomerDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  pubNumber: string

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  officePhoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  officePhoneNumber: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  buildingId?: string

  @ApiPropertyOptional({ type: UpdateAgentDTO })
  @ValidateNested({ each: true })
  @Type(() => UpdateAgentDTO)
  @IsOptional()
  agent?: UpdateAgentDTO

  @ApiProperty()
  @IsString()
  @IsOptional()
  buildingCategory?: string
}

export class GetCustomersDTO extends PaginatedQueryParams {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  building?: string[]

  @ApiPropertyOptional({
    type: 'enum',
    enum: AGENT_STATUS,
  })
  @IsEnum(AGENT_STATUS)
  @IsOptional()
  agent?: string
}

export class UpdateActiveCustomerDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean
}
