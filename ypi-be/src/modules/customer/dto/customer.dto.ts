import { AGENT_STATUS } from '@core/enums/system.enum'
import { CreateAgentDTO, UpdateAgentDTO } from '@modules/agent/dto/agent.dto'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, TransformFnParams, Type } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
  Length,
  Matches,
  IsPhoneNumber,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class CreateCustomerDTO {
  @ApiProperty({ description: 'Customer name', example: 'ABC Company Pte Ltd' })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  @Length(1, 255, { message: 'Name must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string

  @ApiProperty({ description: 'PUB number', example: 'PUB123456' })
  @IsString({ message: 'PUB number must be a string' })
  @IsNotEmpty({ message: 'PUB number cannot be empty' })
  @Length(1, 50, { message: 'PUB number must be between 1 and 50 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  pubNumber: string

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email cannot be empty' })
  @Length(1, 255, { message: 'Email must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim().toLowerCase())
  email: string

  @ApiProperty({ description: 'Phone country code', example: '+65' })
  @IsString({ message: 'Phone code must be a string' })
  @IsNotEmpty({ message: 'Phone code cannot be empty' })
  @Matches(/^\+\d{1,4}$/, {
    message: 'Phone code must be in format +XX or +XXX',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneCode: string

  @ApiProperty({ description: 'Phone number', example: '91234567' })
  @IsString({ message: 'Phone number must be a string' })
  @IsNotEmpty({ message: 'Phone number cannot be empty' })
  @Matches(/^\d{7,15}$/, {
    message: 'Phone number must contain 7-15 digits only',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneNumber: string

  @ApiProperty({ description: 'Office phone country code', example: '+65' })
  @IsString({ message: 'Office phone code must be a string' })
  @IsNotEmpty({ message: 'Office phone code cannot be empty' })
  @Matches(/^\+\d{1,4}$/, {
    message: 'Office phone code must be in format +XX or +XXX',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  officePhoneCode: string

  @ApiProperty({ description: 'Office phone number', example: '62345678' })
  @IsString({ message: 'Office phone number must be a string' })
  @IsNotEmpty({ message: 'Office phone number cannot be empty' })
  @Matches(/^\d{7,15}$/, {
    message: 'Office phone number must contain 7-15 digits only',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  officePhoneNumber: string

  @ApiPropertyOptional({ description: 'Building ID', example: 'uuid-string' })
  @IsUUID('4', { message: 'Building ID must be a valid UUID' })
  @IsOptional()
  @IsString({ message: 'Building ID must be a string' })
  buildingId?: string

  @ApiPropertyOptional({
    type: CreateAgentDTO,
    description: 'Agent information',
  })
  @ValidateNested({ each: true })
  @Type(() => CreateAgentDTO)
  @IsOptional()
  agent?: CreateAgentDTO

  @ApiPropertyOptional({
    description: 'Building category',
    example: 'Residential',
  })
  @IsString({ message: 'Building category must be a string' })
  @IsOptional()
  @Length(0, 100, {
    message: 'Building category must be at most 100 characters',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  buildingCategory?: string
}

export class UpdateCustomerDTO {
  @ApiProperty({ description: 'Customer name', example: 'ABC Company Pte Ltd' })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  @Length(1, 255, { message: 'Name must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string

  @ApiProperty({ description: 'PUB number', example: 'PUB123456' })
  @IsString({ message: 'PUB number must be a string' })
  @IsNotEmpty({ message: 'PUB number cannot be empty' })
  @Length(1, 50, { message: 'PUB number must be between 1 and 50 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  pubNumber: string

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email cannot be empty' })
  @Length(1, 255, { message: 'Email must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim().toLowerCase())
  email: string

  @ApiProperty({ description: 'Phone country code', example: '+65' })
  @IsString({ message: 'Phone code must be a string' })
  @IsNotEmpty({ message: 'Phone code cannot be empty' })
  @Matches(/^\+\d{1,4}$/, {
    message: 'Phone code must be in format +XX or +XXX',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneCode: string

  @ApiProperty({ description: 'Phone number', example: '91234567' })
  @IsString({ message: 'Phone number must be a string' })
  @IsNotEmpty({ message: 'Phone number cannot be empty' })
  @Matches(/^\d{7,15}$/, {
    message: 'Phone number must contain 7-15 digits only',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneNumber: string

  @ApiProperty({ description: 'Office phone country code', example: '+65' })
  @IsString({ message: 'Office phone code must be a string' })
  @IsNotEmpty({ message: 'Office phone code cannot be empty' })
  @Matches(/^\+\d{1,4}$/, {
    message: 'Office phone code must be in format +XX or +XXX',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  officePhoneCode: string

  @ApiProperty({ description: 'Office phone number', example: '62345678' })
  @IsString({ message: 'Office phone number must be a string' })
  @IsNotEmpty({ message: 'Office phone number cannot be empty' })
  @Matches(/^\d{7,15}$/, {
    message: 'Office phone number must contain 7-15 digits only',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  officePhoneNumber: string

  @ApiPropertyOptional({ description: 'Building ID', example: 'uuid-string' })
  @IsUUID('4', { message: 'Building ID must be a valid UUID' })
  @IsOptional()
  @IsString({ message: 'Building ID must be a string' })
  buildingId?: string

  @ApiPropertyOptional({
    type: UpdateAgentDTO,
    description: 'Agent information',
  })
  @ValidateNested({ each: true })
  @Type(() => UpdateAgentDTO)
  @IsOptional()
  agent?: UpdateAgentDTO

  @ApiPropertyOptional({
    description: 'Building category',
    example: 'Residential',
  })
  @IsString({ message: 'Building category must be a string' })
  @IsOptional()
  @Length(0, 100, {
    message: 'Building category must be at most 100 characters',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  buildingCategory?: string
}

export class GetCustomersDTO extends PaginatedQueryParams {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  building?: string[]

  @ApiPropertyOptional({
    type: 'enum',
    enum: AGENT_STATUS,
  })
  @IsEnum(AGENT_STATUS)
  @IsOptional()
  agent?: string
}

export class UpdateActiveCustomerDTO {
  @ApiProperty({ description: 'Active status', example: true })
  @IsNotEmpty({ message: 'Active status cannot be empty' })
  @IsBoolean({ message: 'Active status must be a boolean value' })
  isActive: boolean
}
