import { LoggedUser } from '@core/decorators/auth.decorator'
import { AdminGuard } from '@modules/auth/guards/role.guard'
import { Customer } from '@modules/customer/customer.entity'
import { CustomerService } from '@modules/customer/customer.service'
import {
  CreateCustomerDTO,
  GetCustomersDTO,
  UpdateActiveCustomerDTO,
  UpdateCustomerDTO,
} from '@modules/customer/dto/customer.dto'
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { LoggedUserDTO } from '../../commons/dto/logged'

@Controller('customer')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Post('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Create customer' })
  async createCustomer(
    @Body() body: CreateCustomerDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.customerService.createCustomer(body, user)
    return {
      data: true,
    }
  }

  @Get('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get customers' })
  async getCustomers(
    @Query() body: GetCustomersDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtCustomer = await this.customerService.getCustomers(body, user)
    return {
      data: dtCustomer,
    }
  }

  @Get('/:customerId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get customer' })
  async getCustomer(
    @Param('customerId') customerId: Customer['id'],
  ): Promise<any> {
    const dtCustomer = await this.customerService.getCustomer(customerId)
    return {
      data: dtCustomer,
    }
  }

  @Put('/:customerId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update customer' })
  async updateCustomer(
    @Param('customerId') customerId: Customer['id'],
    @LoggedUser() user: LoggedUserDTO,
    @Body() body: UpdateCustomerDTO,
  ): Promise<any> {
    const dtCustomer = await this.customerService.updateCustomer(
      body,
      customerId,
      user,
    )
    return {
      data: dtCustomer,
    }
  }

  @Put('/active/:customerId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update active customer' })
  async updateActiveCustomer(
    @Param('customerId') customerId: Customer['id'],
    @LoggedUser() user: LoggedUserDTO,
    @Body() body: UpdateActiveCustomerDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.customerService.updateActiveCustomer(customerId, body, user)
    return { data: true }
  }

  @Delete('/:customerId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Delete customer' })
  async deleteCustomer(
    @Param('customerId') customerId: Customer['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.customerService.deleteCustomer(customerId, user)
    return {
      data: true,
    }
  }
}
