import { Inject, Injectable } from '@nestjs/common'
// import { S3 } from 'aws-sdk'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { Logger } from 'winston'
// import sharp from 'sharp'
import { S3 } from '@aws-sdk/client-s3'
import path from 'path'
import { formatURL } from '../../utils/functions/string'

@Injectable()
export class S3Service {
  private readonly _s3: S3
  constructor(
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger,
  ) {
    this._s3 = new S3({
      region: process.env.AWS_S3_REGION,
      credentials: {
        accessKeyId: process.env.AWS_S3_ACCESS_KEY,
        secretAccessKey: process.env.AWS_S3_SECRET_KEY,
      },
    })
  }

  get s3() {
    return this._s3
  }

  // getS3Link(key) {
  //   if (key) {
  //     return process.env.AWS_S3_URL + '/' + key
  //   }
  //   return null
  // }

  async processImageUpload(prefix, file) {
    const filename =
      formatURL(path.parse(file.originalname).name) +
      '-' +
      Date.now() +
      path.extname(file.originalname)
    const destination = prefix + '/' + filename
    // const imageQuality = 90
    // const compressBuffer = await sharp(file.buffer)
    // .rotate()
    // .jpeg({ quality: imageQuality, force: false })
    // .png({ quality: imageQuality, force: false })
    // .toBuffer()
    const compressFile = {
      buffer: file.buffer,
      originalname: destination,
      mimetype: file.mimetype,
    }
    await this.upload(compressFile)
    return {
      name: path.parse(file.originalname).name,
      mineType: compressFile.mimetype,
      link: destination,
    }
  }

  async processFileUpload(prefix, file) {
    const filename =
      formatURL(path.parse(file.originalname).name) +
      '-' +
      Date.now() +
      path.extname(file.originalname)
    const destination = prefix + '/' + filename
    const compressFile = {
      buffer: file.buffer,
      originalname: destination,
      mimetype: file.mimetype,
    }
    await this.upload(compressFile)
    return {
      name: path.parse(file.originalname).name,
      mineType: compressFile.mimetype,
      link: destination,
    }
  }

  async upload(file) {
    const { buffer, originalname, mimetype } = file
    return await this.s3.putObject({
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: String(originalname),
      ContentType: mimetype ? mimetype : undefined,
      Body: buffer,
      ACL: 'public-read',
    })
  }
}
