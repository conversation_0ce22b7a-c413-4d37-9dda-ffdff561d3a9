import { BaseEntity } from '@core/databases/entities/base.entity'
import { FILE_TYPE } from '@core/enums/file.enum'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { Staff } from '@modules/staff/staff.entity'
import { Tank } from '@modules/tank/tank.entity'
import { Task } from '@modules/task/task.entity'
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm'

@Entity('file')
export class File extends BaseEntity {
  @Column({ nullable: true })
  name: string

  @Column({ nullable: true })
  mineType: string

  @Column({ nullable: true })
  link: string

  @Column({
    type: 'enum',
    enum: FILE_TYPE,
    default: null,
  })
  type: FILE_TYPE

  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffId' })
  staff: Staff

  @ManyToOne(() => Task, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'taskId' })
  task: Task

  @ManyToOne(() => Tank, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'tankId' })
  tank: Tank

  @ManyToOne(() => AssignTask, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'assignTaskId' })
  assignTask: AssignTask

  @ManyToOne(() => Task, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'inspectorRemarkId' })
  inspectorRemark: Task
}
