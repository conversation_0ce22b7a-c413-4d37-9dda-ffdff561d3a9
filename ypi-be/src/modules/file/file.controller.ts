import { Controller, Delete, Param } from '@nestjs/common'
import { FileService } from '@modules/file/file.service'
import { ApiOperation } from '@nestjs/swagger'
import { File } from '@modules/file/file.entity'

@Controller('file')
export class FileController {
  constructor(private readonly fileService: FileService) {
  }

  @Delete('/:fileId')
  @ApiOperation({ summary: 'Delete file' })
  async deleteFile(
    @Param('fileId') fileId: File['id'],
  ): Promise<any> {
    await this.fileService.deleteFile(fileId)
    return {
      data: true,
    }
  }
}
