import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { File } from '@modules/file/file.entity'
import { FileService } from '@modules/file/file.service'
import { AwsModule } from '@modules/aws/aws.module'
import { FileController } from '@modules/file/file.controller'

@Module({
  imports: [
    TypeOrmModule.forFeature([File]),
    AwsModule,
  ],
  controllers: [FileController],
  providers: [FileService],
  exports: [FileService],
})
export class FileModule {
}
