import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { File } from '@modules/file/file.entity'
import { Repository } from 'typeorm'
import { S3Service } from '@modules/aws/s3.service'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class FileService {
  constructor(
    @InjectRepository(File)
    public fileRepo: Repository<File>,
    private readonly s3Service: S3Service,
  ) {
  }

  async deleteFile(fileId: File['id']) {
    const findFile = await this.fileRepo.findOne({
      where: { id: fileId },
    })

    if (!findFile) {
      throw new NotFoundException(MSG.FILE_NOT_FOUND)
    }

    return this.fileRepo.update(fileId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
