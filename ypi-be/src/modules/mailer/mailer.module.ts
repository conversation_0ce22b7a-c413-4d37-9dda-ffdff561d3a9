import { MailerService } from '@modules/mailer/mailer.service'
import { RedisModule } from '@modules/redis/redis.module'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { MailerController } from './mailer.controller'

@Module({
  imports: [TypeOrmModule.forFeature([]), RedisModule],
  controllers: [MailerController],
  providers: [MailerService],
  exports: [MailerService],
})
export class MailerModule {}
