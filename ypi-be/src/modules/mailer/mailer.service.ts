import { configService } from '@core/configs/env.config'
import { EMAIL_TEMPLATE } from '@core/enums/system.enum'
import { RedisService } from '@modules/redis/redis.service'
import { Injectable } from '@nestjs/common'
import { readFile } from 'fs'
import mjml2html from 'mjml'
import { render } from 'mustache'
import * as nodemailer from 'nodemailer'
import { SendEmailDto } from './dto/email.dto'

@Injectable()
export class MailerService {
  private readonly transporter: nodemailer.Transporter
  private readonly sender: string

  constructor(private readonly redisService: RedisService) {
    this.sender = configService.get('AWS_SES_SENDER')

    this.transporter = nodemailer.createTransport({
      host: configService.get('AWS_SES_ENDPOINT'),
      port: configService.get('AWS_SES_PORT'),
      secure: false,
      tls: {
        rejectUnauthorized: false,
        ciphers: 'SSLv3',
      },
      auth: {
        user: configService.get('AWS_SES_SMTP_USER'),
        pass: configService.get('AWS_SES_SMTP_PASS'),
      },
    })
  }

  static generateMail(
    template: string,
    patterns: Record<string, any> = {},
  ): string {
    return render(template, patterns)
  }

  async getEmailHTMLContent(
    data: SendEmailDto,
    params: Record<string, any> = {},
    extension: 'html' | 'mjml' = 'html',
  ) {
    const { subject, html } = data
    const getHtml = await this.loadTemplate(`${html}.${extension}`)
    const parsedTemplate = MailerService.generateMail(getHtml, {
      subject,
      ...params,
    })

    const htmlContent =
      extension === 'mjml' ? mjml2html(parsedTemplate).html : parsedTemplate

    return htmlContent
  }

  private async checkValidEmailReceiver(
    emails: string | string[],
    params: Record<string, any> = {},
    template: EMAIL_TEMPLATE,
  ): Promise<string | string[]> {
    const emailList = Array.isArray(emails) ? emails : [emails]

    const paramStr = JSON.stringify(params)

    const validEmails = await emailList.reduce(async (accPromise, email) => {
      const acc = await accPromise

      const redisKey = `${email}_${template}_${paramStr}`

      const cacheExists = await this.redisService.get(redisKey)

      if (!cacheExists) {
        acc.push(email)

        await this.redisService.set(redisKey, 'exist', 60 * 15)
      }

      return acc
    }, Promise.resolve([]))

    return validEmails
  }

  async sendMail(
    data: SendEmailDto,
    params: Record<string, any> = {},
    extension: 'html' | 'mjml' = 'html',
  ) {
    const { to, subject, text, html } = data
    const getHtml = await this.loadTemplate(`${html}.${extension}`)
    const parsedTemplate = MailerService.generateMail(getHtml, {
      subject,
      ...params,
    })

    const validEmailReceivers = await this.checkValidEmailReceiver(
      to,
      params,
      data.html as EMAIL_TEMPLATE,
    )

    if (validEmailReceivers.length <= 0) return

    const htmlContent =
      extension === 'mjml' ? mjml2html(parsedTemplate).html : parsedTemplate

    return this.transporter
      .sendMail({
        from: this.sender,
        to: validEmailReceivers,
        subject,
        text,
        html: htmlContent,
      })
      .then(() => {
        console.log('@@@: Send mail success')
        return {
          status: 'success',
        }
      })
      .catch((err) => {
        console.log('@@@ Send mail error', err)
        throw err
      })
  }

  async loadTemplate(filename: string) {
    return await new Promise<string>((res, rej) => {
      readFile(`${process.cwd()}/emails/${filename}`, 'utf8', (err, data) => {
        if (err) {
          rej(err)
        } else {
          res(data)
        }
      })
    })
  }
}
