import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class TestEmailDto {
  @ApiProperty({ description: 'Email address to send the test email to' })
  @IsEmail()
  @IsNotEmpty()
  to: string

  @ApiProperty({ description: 'Email subject', default: 'Test Email' })
  @IsString()
  @IsOptional()
  subject?: string = 'Test Email'

  @ApiProperty({
    description: 'Email text content',
    default: 'This is a test email',
  })
  @IsString()
  @IsOptional()
  text?: string = 'This is a test email'
}
