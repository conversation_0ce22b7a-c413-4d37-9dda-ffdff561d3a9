import { Public } from '@core/decorators/public.decorator'
import { EMAIL_TEMPLATE } from '@core/enums/system.enum'
import { Body, Controller, Post } from '@nestjs/common'
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import { SendEmailDto } from './dto/email.dto'
import { TestEmailDto } from './dto/test-email.dto'
import { MailerService } from './mailer.service'

@ApiTags('Mailer')
@Controller('mailer')
export class MailerController {
  constructor(private readonly mailerService: MailerService) {}

  @Public()
  @Post('/test')
  @ApiOperation({
    summary: 'Test email sending (Public endpoint, no authentication required)',
  })
  @ApiResponse({ status: 200, description: 'Email sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 500, description: 'Server error' })
  async testSendEmail(@Body() testEmailDto: TestEmailDto) {
    try {
      // Create the email data from the test DTO
      const emailData: SendEmailDto = {
        to: testEmailDto.to,
        subject: testEmailDto.subject || 'Test Email',
        text: testEmailDto.text || 'This is a test email',
        html: EMAIL_TEMPLATE.FORGOT_PASSWORD, // Using an existing template
      }

      // Send the email
      const result = await this.mailerService.sendMail(emailData)

      return {
        success: true,
        message: 'Test email sent successfully',
        data: result,
      }
    } catch (error) {
      return {
        success: false,
        message: 'Failed to send test email',
        error: error.message,
      }
    }
  }
}
