import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, TransformFnParams } from 'class-transformer'
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
} from 'class-validator'

export class CreateAgentDTO {
  @ApiProperty({ description: 'Agent name', example: '<PERSON>' })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  @Length(1, 255, { message: 'Name must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string

  @ApiPropertyOptional({ description: 'Company name', example: 'ABC Company' })
  @IsString({ message: 'Company must be a string' })
  @IsOptional()
  @Length(0, 255, { message: 'Company must be at most 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  company?: string

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email cannot be empty' })
  @Length(1, 255, { message: 'Email must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim().toLowerCase())
  email: string

  @ApiProperty({ description: 'Phone country code', example: '+65' })
  @IsString({ message: 'Phone code must be a string' })
  @IsNotEmpty({ message: 'Phone code cannot be empty' })
  @Matches(/^\+\d{1,4}$/, {
    message: 'Phone code must be in format +XX or +XXX',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneCode: string

  @ApiProperty({ description: 'Phone number', example: '91234567' })
  @IsString({ message: 'Phone number must be a string' })
  @IsNotEmpty({ message: 'Phone number cannot be empty' })
  @Matches(/^\d{7,15}$/, {
    message: 'Phone number must contain 7-15 digits only',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneNumber: string

  @ApiProperty({ description: 'Postal code', example: '123456' })
  @IsString({ message: 'Postal code must be a string' })
  @IsNotEmpty({ message: 'Postal code cannot be empty' })
  @Matches(/^\d{6}$/, { message: 'Postal code must be 6 digits' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  postalCode: string

  @ApiProperty({ description: 'Block number', example: '123A' })
  @IsString({ message: 'Block number must be a string' })
  @IsNotEmpty({ message: 'Block number cannot be empty' })
  @Length(1, 20, {
    message: 'Block number must be between 1 and 20 characters',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  blockNo: string

  @ApiProperty({ description: 'Street name', example: 'Main Street' })
  @IsNotEmpty({ message: 'Street cannot be empty' })
  @IsString({ message: 'Street must be a string' })
  @Length(1, 255, { message: 'Street must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  street: string

  @ApiPropertyOptional({
    description: 'Building name',
    example: 'ABC Building',
  })
  @IsString({ message: 'Building must be a string' })
  @IsOptional()
  @Length(0, 255, { message: 'Building must be at most 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  building?: string

  @ApiPropertyOptional({ description: 'Designation', example: 'Manager' })
  @IsString({ message: 'Designation must be a string' })
  @IsOptional()
  @Length(0, 100, { message: 'Designation must be at most 100 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  designation?: string

  @ApiPropertyOptional({
    description: 'In charge category',
    example: 'Technical',
  })
  @IsString({ message: 'In charge category must be a string' })
  @IsOptional()
  @Length(0, 100, {
    message: 'In charge category must be at most 100 characters',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  inChargeCategory?: string

  @ApiPropertyOptional({ description: 'In charge ID', example: 'uuid-string' })
  @IsUUID('4', { message: 'In charge ID must be a valid UUID' })
  @IsOptional()
  @IsString({ message: 'In charge ID must be a string' })
  inChargeId?: string
}

export class UpdateAgentDTO {
  @ApiPropertyOptional({ description: 'Agent ID', example: 'uuid-string' })
  @IsUUID('4', { message: 'Agent id must be a UUID' })
  @IsOptional()
  id?: string

  @ApiProperty({ description: 'Agent name', example: 'John Doe' })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name cannot be empty' })
  @Length(1, 255, { message: 'Name must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string

  @ApiPropertyOptional({ description: 'Company name', example: 'ABC Company' })
  @IsString({ message: 'Company must be a string' })
  @IsOptional()
  @Length(0, 255, { message: 'Company must be at most 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  company?: string

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email cannot be empty' })
  @Length(1, 255, { message: 'Email must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim().toLowerCase())
  email: string

  @ApiProperty({ description: 'Phone country code', example: '+65' })
  @IsString({ message: 'Phone code must be a string' })
  @IsNotEmpty({ message: 'Phone code cannot be empty' })
  @Matches(/^\+\d{1,4}$/, {
    message: 'Phone code must be in format +XX or +XXX',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneCode: string

  @ApiProperty({ description: 'Phone number', example: '91234567' })
  @IsString({ message: 'Phone number must be a string' })
  @IsNotEmpty({ message: 'Phone number cannot be empty' })
  @Matches(/^\d{7,15}$/, {
    message: 'Phone number must contain 7-15 digits only',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneNumber: string

  @ApiProperty({ description: 'Postal code', example: '123456' })
  @IsString({ message: 'Postal code must be a string' })
  @IsNotEmpty({ message: 'Postal code cannot be empty' })
  @Matches(/^\d{6}$/, { message: 'Postal code must be 6 digits' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  postalCode: string

  @ApiProperty({ description: 'Block number', example: '123A' })
  @IsString({ message: 'Block number must be a string' })
  @IsNotEmpty({ message: 'Block number cannot be empty' })
  @Length(1, 20, {
    message: 'Block number must be between 1 and 20 characters',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  blockNo: string

  @ApiProperty({ description: 'Street name', example: 'Main Street' })
  @IsNotEmpty({ message: 'Street cannot be empty' })
  @IsString({ message: 'Street must be a string' })
  @Length(1, 255, { message: 'Street must be between 1 and 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  street: string

  @ApiPropertyOptional({
    description: 'Building name',
    example: 'ABC Building',
  })
  @IsString({ message: 'Building must be a string' })
  @IsOptional()
  @Length(0, 255, { message: 'Building must be at most 255 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  building?: string

  @ApiPropertyOptional({ description: 'Designation', example: 'Manager' })
  @IsString({ message: 'Designation must be a string' })
  @IsOptional()
  @Length(0, 100, { message: 'Designation must be at most 100 characters' })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  designation?: string

  @ApiPropertyOptional({
    description: 'In charge category',
    example: 'Technical',
  })
  @IsString({ message: 'In charge category must be a string' })
  @IsOptional()
  @Length(0, 100, {
    message: 'In charge category must be at most 100 characters',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  inChargeCategory?: string

  @ApiPropertyOptional({ description: 'In charge ID', example: 'uuid-string' })
  @IsUUID('4', { message: 'In charge ID must be a valid UUID' })
  @IsOptional()
  @IsString({ message: 'In charge ID must be a string' })
  inChargeId?: string
}
