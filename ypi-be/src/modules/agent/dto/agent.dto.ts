import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, TransformFnParams } from 'class-transformer'
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator'

export class CreateAgentDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  company?: string

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  postalCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  blockNo: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  street: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  building?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  designation?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  inChargeCategory?: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  inChargeId?: string
}

export class UpdateAgentDTO {
  @ApiPropertyOptional()
  @IsUUID('4', { message: 'Agent id must be a UUID' })
  @IsOptional()
  id?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  company?: string

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  postalCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  blockNo: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  street: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  building?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  designation?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  inChargeCategory?: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  inChargeId?: string
}
