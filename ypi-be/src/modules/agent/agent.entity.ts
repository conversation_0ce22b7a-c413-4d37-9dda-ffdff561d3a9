import { BaseEntity } from '@core/databases/entities/base.entity'
import { Customer } from '@modules/customer/customer.entity'
import { InCharge } from '@modules/in-charge/in-charge.entity'
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm'

@Entity('agent')
export class Agent extends BaseEntity {
  @Column()
  name: string

  @Column({ nullable: true })
  company: string

  @Column({ nullable: true })
  email: string

  @Column({ nullable: true })
  phoneCode: string

  @Column({ nullable: true })
  phoneNumber: string

  @Column({ nullable: true })
  postalCode: string

  @Column({ nullable: true })
  designation: string

  @Column({ nullable: true })
  blockNo: string

  @Column({ nullable: true })
  street: string

  @Column({ nullable: true })
  building: string

  @Column({ nullable: true })
  inChargeCategory: string

  @ManyToOne(() => InCharge, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'inChargeId' })
  inCharge: InCharge

  @ManyToOne(() => Customer, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'customerId' })
  customer: Customer
}
