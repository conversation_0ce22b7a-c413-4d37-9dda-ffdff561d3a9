import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm'
import { Agent } from '@modules/agent/agent.entity'
import { AgentController } from '@modules/agent/agent.controller'
import { AgentService } from '@modules/agent/agent.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([Agent]),
  ],
  controllers: [AgentController],
  providers: [AgentService],
  exports: [AgentService],
})
export class AgentModule {}
