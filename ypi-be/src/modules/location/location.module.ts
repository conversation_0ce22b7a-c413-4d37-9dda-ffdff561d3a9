import { AgentModule } from '@modules/agent/agent.module'
import { BuildingModule } from '@modules/building/building.module'
import { CustomerModule } from '@modules/customer/customer.module'
import { InChargeModule } from '@modules/in-charge/in-charge.module'
import { LocationController } from '@modules/location/location.controller'
import { Location } from '@modules/location/location.entity'
import { LocationService } from '@modules/location/location.service'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

@Module({
  imports: [
    TypeOrmModule.forFeature([Location]),
    CustomerModule,
    BuildingModule,
    InChargeModule,
    AgentModule,
  ],
  controllers: [LocationController],
  providers: [LocationService],
  exports: [LocationService],
})
export class LocationModule {}
