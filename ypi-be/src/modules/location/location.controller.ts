import { AdminGuard } from '@modules/auth/guards/role.guard'
import { Customer } from '@modules/customer/customer.entity'
import { CustomerService } from '@modules/customer/customer.service'
import {
  CreateLocationDTO,
  GetLocationsDTO,
  UpdateLocationDTO,
} from '@modules/location/dto/location.dto'
import { Location } from '@modules/location/location.entity'
import { LocationService } from '@modules/location/location.service'
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Logger,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiOperation } from '@nestjs/swagger'

@Controller('location')
export class LocationController {
  private readonly logger = new Logger(LocationController.name)

  constructor(
    private readonly locationService: LocationService,
    @Inject(CustomerService)
    private readonly customerService: CustomerService,
  ) {}

  @Get('/customer/:customerId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get locations' })
  async getLocations(
    @Param('customerId') customerId: Customer['id'],
    @Query() body: GetLocationsDTO,
  ): Promise<any> {
    const dtLocation = await this.locationService.getLocations(body, customerId)
    return {
      data: dtLocation,
    }
  }

  @Get('/:locationId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get location' })
  async getLocation(
    @Param('locationId') locationId: Location['id'],
  ): Promise<any> {
    const dtLocation = await this.locationService.getLocation(locationId)
    return {
      data: dtLocation,
    }
  }

  @Post('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Create location' })
  async createLocation(@Body() body: CreateLocationDTO): Promise<any> {
    const dtLocation = await this.locationService.createLocation(body)
    return {
      data: dtLocation,
    }
  }

  @Put('/:locationId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update location' })
  async updateLocation(
    @Param('locationId') locationId: Location['id'],
    @Body() body: UpdateLocationDTO,
  ): Promise<any> {
    const dtLocation = await this.locationService.updateLocation(
      body,
      locationId,
    )
    return {
      data: dtLocation,
    }
  }

  @Delete('/:locationId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Delete location' })
  async deleteLocation(
    @Param('locationId') locationId: Location['id'],
  ): Promise<any> {
    await this.locationService.deleteLocation(locationId)
    return {
      data: true,
    }
  }

  @Post('/import')
  @UseGuards(AdminGuard)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import locations from JSON file' })
  async importLocations(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<any> {
    const results = []
    const errors = []
    const skipped = []

    try {
      const data = JSON.parse(file.buffer.toString())
      this.logger.log(`Starting import of ${data.length} customers`)

      for (const item of data) {
        try {
          // Trim customer name to handle extra spaces
          const trimmedCustomerName = item.customer.trim()

          // Find customer by exact name match, trimming both sides
          const customers = await this.customerService.customerRepo
            .createQueryBuilder('customer')
            .where('TRIM(customer.name) = :name', {
              name: trimmedCustomerName,
            })
            .getMany()

          this.logger.log(
            `Found ${customers.length} customers for name: ${trimmedCustomerName}`,
          )

          if (customers.length === 0) {
            // Try to find similar names for debugging
            const similarCustomers = await this.customerService.customerRepo
              .createQueryBuilder('customer')
              .where('TRIM(customer.name) LIKE :name', {
                name: `%${trimmedCustomerName}%`,
              })
              .getMany()

            if (similarCustomers.length > 0) {
              this.logger.warn(
                `Found similar customers for ${trimmedCustomerName}: ${similarCustomers.map(
                  (c) => c.name,
                )}`,
              )
            }

            errors.push({
              customerName: trimmedCustomerName,
              error: 'Customer not found',
              similarCustomers: similarCustomers.map((c) => c.name),
            })
            continue
          }

          // Create location for each customer found
          for (const customer of customers) {
            // Process each location for this customer
            for (const location of item.location) {
              // Check for existing location
              const existingLocation =
                await this.locationService.locationRepo.findOne({
                  where: {
                    customer: { id: customer.id },
                    building: location.building,
                    postalCode: location.postal_code,
                  },
                })

              if (existingLocation) {
                this.logger.log(
                  `Skipping duplicate location for customer ${customer.name}: ${location.building}`,
                )
                skipped.push({
                  customerName: customer.name,
                  building: location.building,
                  postalCode: location.postal_code,
                })
                continue
              }

              const createLocationDTO: CreateLocationDTO = {
                postalCode: location.postal_code,
                blockNo: location.postal_code,
                street: location.address,
                building: location.building,
                customerId: customer.id,
              }

              const createdLocation =
                await this.locationService.createLocation(createLocationDTO)
              results.push(createdLocation)
            }
          }
        } catch (error) {
          this.logger.error(
            `Error processing customer ${item.customer}: ${error.message}`,
          )
          errors.push({
            customerName: item.customer,
            error: error.message,
          })
        }
      }
    } catch (error) {
      this.logger.error(`Error parsing JSON file: ${error.message}`)
      return {
        data: {
          success: [],
          errors: [{ error: 'Invalid JSON file format' }],
        },
      }
    }

    this.logger.log(
      `Import completed. Success: ${results.length}, Errors: ${errors.length}, Skipped: ${skipped.length}`,
    )
    return {
      data: {
        success: results,
        errors,
        skipped,
      },
    }
  }
}
