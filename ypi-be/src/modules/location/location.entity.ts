import { BaseEntity } from '@core/databases/entities/base.entity'
import { Customer } from '@modules/customer/customer.entity'
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm'

@Entity('location')
export class Location extends BaseEntity {
  @Column({ nullable: true })
  postalCode: string

  @Column({ nullable: true })
  blockNo: string

  @Column({ nullable: true })
  street: string

  @Column({ nullable: true })
  building: string

  @Column({ nullable: true })
  lat: string

  @Column({ nullable: true })
  long: string

  @Column({ nullable: true })
  address: string

  @ManyToOne(() => Customer, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'customerId' })
  customer: Customer
}
