import { STAFF_POSITION } from '@core/enums/staff.enum'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, TransformFnParams } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class GetStaffsDTO extends PaginatedQueryParams {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsEnum(STAFF_POSITION, { each: true })
  position?: STAFF_POSITION[]
}

export class GetAllowStaffsDTO {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsEnum(STAFF_POSITION, { each: true })
  position?: STAFF_POSITION[]
}

export class CreateStaffDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fullName: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty({ type: STAFF_POSITION })
  @IsEnum(STAFF_POSITION)
  @IsNotEmpty()
  position: STAFF_POSITION

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  address?: string

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  birthday?: Date

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @IsUrl({ require_tld: false, require_protocol: true })
  callbackURL?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  fileIds?: string[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  avatar?: string
}

export class UpdateStaffDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fullName: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string

  @ApiProperty({ type: STAFF_POSITION })
  @IsEnum(STAFF_POSITION)
  @IsNotEmpty()
  position: STAFF_POSITION

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  address?: string

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  birthday?: Date

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  fileIds?: string[]

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  removeFileIds?: string[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  avatar?: string
}

export class UpdateActiveStaffDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean
}
