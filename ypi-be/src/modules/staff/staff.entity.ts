import { BaseEntity } from '@core/databases/entities/base.entity'
import { STAFF_POSITION } from '@core/enums/staff.enum'
import { File } from '@modules/file/file.entity'
import { User } from '@modules/user/user.entity'
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm'

@Entity('staff')
export class Staff extends BaseEntity {
  @Column({ nullable: true })
  code: string

  @Column({ nullable: true })
  email: string

  @Column({ nullable: true })
  phoneCode: string

  @Column({ nullable: true })
  phoneNumber: string

  @Column({ nullable: true })
  fullName: string

  @Column({ nullable: true })
  firstName: string

  @Column({ nullable: true })
  lastName: string

  @Column({ nullable: true })
  address: string

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  birthday: Date

  @Column({
    type: 'enum',
    enum: STAFF_POSITION,
    default: null,
  })
  position: STAFF_POSITION

  @Column({ default: true })
  isActive: boolean

  @OneToMany(() => File, (file) => file.staff, { onDelete: 'SET NULL' })
  @JoinColumn()
  documents: File[]

  @OneToMany(() => File, (file) => file.staff, { onDelete: 'SET NULL' })
  @JoinColumn()
  certifications: File[]

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'managedBy' })
  managedBy: User
}
