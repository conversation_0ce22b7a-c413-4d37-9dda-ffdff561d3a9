import { Module } from '@nestjs/common'
import { StaffService } from '@modules/staff/staff.service'
import { StaffController } from '@modules/staff/staff.controller'
import { TypeOrmModule } from '@nestjs/typeorm'
import { Staff } from '@modules/staff/staff.entity'
import { File } from '@modules/file/file.entity'
import { AwsModule } from '@modules/aws/aws.module'
import { RoleModule } from '@modules/role/role.module'
import { User } from '@modules/user/user.entity'
import { MailerModule } from '@modules/mailer/mailer.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([Staff, File, User]),
    AwsModule,
    RoleModule,
    MailerModule,
  ],
  controllers: [StaffController],
  providers: [StaffService],
  exports: [StaffService],
})
export class StaffModule {}
