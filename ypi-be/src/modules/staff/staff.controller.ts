import { LoggedUser } from '@core/decorators/auth.decorator'
import { AdminGuard } from '@modules/auth/guards/role.guard'
import {
  CreateStaffDTO,
  GetAllowStaffsDTO,
  GetStaffsDTO,
  UpdateActiveStaffDTO,
  UpdateStaffDTO,
} from '@modules/staff/dto/staff.dto'
import { Staff } from '@modules/staff/staff.entity'
import { StaffService } from '@modules/staff/staff.service'
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { LoggedUserDTO } from '../../commons/dto/logged'

@Controller('staff')
export class StaffController {
  constructor(private readonly staffService: StaffService) {}

  @Get('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get staffs' })
  async getStaffs(
    @Query() body: GetStaffsDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtStaff = await this.staffService.getStaffs(body, user)
    return {
      data: dtStaff,
    }
  }

  @Get('/allow')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get allow staffs' })
  async getAllowStaffs(
    @Query() body: GetAllowStaffsDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtStaff = await this.staffService.getAllowStaffs(body, user)
    return {
      data: dtStaff,
    }
  }

  @Get('/:staffId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get staff' })
  async getStaff(@Param('staffId') staffId: Staff['id']): Promise<any> {
    const dtStaff = await this.staffService.getStaff(staffId)
    return {
      data: dtStaff,
    }
  }

  @Post('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Create staff' })
  async createStaff(
    @Body() body: CreateStaffDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtStaff = await this.staffService.createStaff(body, user)
    return {
      data: dtStaff,
    }
  }

  @Put('/:staffId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update staff' })
  async updateStaff(
    @Param('staffId') staffId: Staff['id'],
    @Body() body: UpdateStaffDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtStaff = await this.staffService.updateStaff(body, staffId, user)
    return {
      data: dtStaff,
    }
  }

  @Delete('/:staffId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Delete staff' })
  async deleteStaff(
    @Param('staffId') staffId: Staff['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.staffService.deleteStaff(staffId, user)
    return {
      data: true,
    }
  }

  @Put('/active/:staffId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update active staff' })
  async updateActiveStaff(
    @Param('staffId') staffId: Staff['id'],
    @LoggedUser() user: LoggedUserDTO,
    @Body() body: UpdateActiveStaffDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.staffService.updateActiveStaff(staffId, body, user)
    return { data: true }
  }
}
