import { FILE_TYPE } from '@core/enums/file.enum'
import { ROLE } from '@core/enums/role.enum'
import { EMAIL_TEMPLATE } from '@core/enums/system.enum'
import { S3Service } from '@modules/aws/s3.service'
import { File } from '@modules/file/file.entity'
import { MailerService } from '@modules/mailer/mailer.service'
import { RoleService } from '@modules/role/role.service'
import {
  CreateStaffDTO,
  GetAllowStaffsDTO,
  GetStaffsDTO,
  UpdateActiveStaffDTO,
  UpdateStaffDTO,
} from '@modules/staff/dto/staff.dto'
import { Staff } from '@modules/staff/staff.entity'
import { User } from '@modules/user/user.entity'
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Brackets, FindOneOptions, Repository } from 'typeorm'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { encodeToken } from '../../utils/functions/encrypt'
import { processLinkAsset } from '../../utils/functions/image'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class StaffService {
  constructor(
    @InjectRepository(Staff)
    public staffRepo: Repository<Staff>,
    @InjectRepository(File)
    private fileRepo: Repository<File>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    private readonly s3Service: S3Service,
    private readonly roleService: RoleService,
    private readonly mailerService: MailerService,
  ) {}

  async createStaff(
    payload: CreateStaffDTO,
    { user }: LoggedUserDTO,
  ): Promise<any> {
    const {
      email,
      fullName,
      phoneCode,
      phoneNumber,
      birthday,
      fileIds,
      avatar,
      ...rest
    } = payload

    let findStaff = await this.staffRepo
      .createQueryBuilder('staff')
      .where(
        '(staff.code = :code OR CONCAT(staff.phoneCode, staff.phoneNumber) = :phone OR staff.email = :email)',
        {
          code: rest.code,
          phone: `${phoneCode}${phoneNumber}`,
          email,
        },
      )
      .getOne()
    if (findStaff) {
      if (findStaff.phoneNumber.includes(phoneNumber)) {
        throw new BadRequestException(MSG.STAFF_PHONE_ALREADY_EXISTS)
      }
      if (findStaff.code.includes(rest.code)) {
        throw new BadRequestException(MSG.STAFF_CODE_ALREADY_EXISTS)
      }
      if (findStaff.email.includes(email)) {
        throw new BadRequestException(MSG.STAFF_EMAIL_ALREADY_EXISTS)
      }
      throw new BadRequestException(MSG.STAFF_ALREADY_EXISTS)
    }

    const findEmail = await this.userRepo.findOne({ where: { email } })
    if (findEmail) {
      throw new BadRequestException(MSG.STAFF_EMAIL_ALREADY_EXISTS)
    }

    const findRole = await this.roleService.getRoleValue(
      ROLE.STAFF.toUpperCase(),
    )
    if (!findRole) {
      throw new NotFoundException(MSG.ROLE_NOT_FOUND)
    }

    const nameParts = fullName.split(' ')
    const lastName = nameParts.pop()
    const firstName = nameParts.join(' ')

    // 1. Generate a random default password
    const defaultPassword = Math.random().toString(36).slice(-8) // 8-char random password
    const passwordSalt = require('crypto').randomBytes(16).toString('hex')
    const passwordHash = require('crypto')
      .scryptSync(defaultPassword, passwordSalt, 32)
      .toString('hex')

    const dtStaff = await this.staffRepo.save({
      ...rest,
      email,
      firstName,
      lastName,
      fullName,
      phoneCode,
      phoneNumber,
      birthday: birthday ? moment(birthday).endOf('day').toDate() : null,
      managedBy: {
        id: user.id,
      },
    })

    const dtUser = await this.userRepo.save({
      passwordHash,
      passwordSalt,
      email,
      avatar,
      role: findRole,
      staff: {
        id: dtStaff.id,
      },
    })

    const tokenDate = new Date()
    const tokenTimestamp = Math.floor(tokenDate.getTime() / 1000)
    const token = encodeToken(
      `?userId=${dtUser.id}&timestamp=${tokenTimestamp}`,
    )
    await this.userRepo.save({ id: dtUser.id, passwordChangeToken: token })

    const hostUrl =
      process.env.NODE_ENV === 'production'
        ? 'https://api.carelaplus.com'
        : 'https://ypi-dev.thepineapple.io'

    await this.mailerService
      .sendMail(
        {
          to: email,
          subject: 'YPI - New user is registered',
          html: EMAIL_TEMPLATE.CREATE_USER,
        },
        {
          fullName: dtUser.fullName || nameParts,
          email,
          password: defaultPassword, // send password in email
          loginLink: undefined, // remove login button
          hostUrl,
          note: 'Please update your password by forgot password after login successfully.',
        },
      )
      .catch(console.error)

    if (fileIds && fileIds.length) {
      for (const fileId of fileIds) {
        const findFile = await this.fileRepo.findOne({
          where: { id: fileId },
        })
        if (findFile) {
          await this.fileRepo.save({
            staff: {
              id: dtStaff.id,
            },
            id: fileId,
          })
        }
      }
    }

    findStaff = await this.staffRepo
      .createQueryBuilder('staff')
      .leftJoinAndSelect(
        'staff.documents',
        'documents',
        `documents.type = :fileDocument`,
        { fileDocument: FILE_TYPE.DOCUMENT },
      )
      .leftJoinAndSelect(
        'staff.certifications',
        'certifications',
        'certifications.type = :fileCert',
        { fileCert: FILE_TYPE.CERTIFICATION },
      )
      .where('staff.id = :staffId', { staffId: dtStaff.id })
      .getOne()

    if (findStaff.documents.length) {
      processLinkAsset(findStaff.documents, FILE_TYPE.DOCUMENT)
    }

    if (findStaff.certifications.length) {
      processLinkAsset(findStaff.certifications, FILE_TYPE.CERTIFICATION)
    }

    return findStaff
  }

  async getStaffs(
    { keyword, pageIndex, pageSize, position }: GetStaffsDTO,
    { user }: LoggedUserDTO,
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)

    const queryBuilder = this.staffRepo
      .createQueryBuilder('staff')
      .leftJoinAndSelect(
        'staff.documents',
        'documents',
        `documents.type = :fileDocument`,
        { fileDocument: FILE_TYPE.DOCUMENT },
      )
      .leftJoinAndSelect(
        'staff.certifications',
        'certifications',
        'certifications.type = :fileCert',
        { fileCert: FILE_TYPE.CERTIFICATION },
      )
      .innerJoinAndMapOne('staff.user', User, 'user', 'user.staffId = staff.id')
      .leftJoin('staff.managedBy', 'managedBy')
      .where('managedBy.id = :userId', { userId: user.id })
      .skip(skip)
      .take(take)

    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('staff.email ILIKE :keyword', { keyword: `%${keyword}%` })
            .orWhere('staff.phoneNumber ILIKE :keyword', {
              keyword: `%${keyword}%`,
            })
            .orWhere('staff.email ILIKE :keyword', { keyword: `%${keyword}%` })
        }),
      )
    }

    if (position && position.length) {
      queryBuilder.andWhere('staff.position IN (:...positions)', {
        positions: position,
      })
    }

    queryBuilder.orderBy('staff.fullName', 'ASC')
    const [data, total] = await queryBuilder.getManyAndCount()

    data.map((obj) => {
      if (obj?.documents?.length) {
        processLinkAsset(obj.documents, FILE_TYPE.DOCUMENT)
      }
      if (obj?.certifications?.length) {
        processLinkAsset(obj.certifications, FILE_TYPE.CERTIFICATION)
      }
    })

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  async getAllowStaffs(
    { position }: GetAllowStaffsDTO,
    { user }: LoggedUserDTO,
  ) {
    const queryBuilder = this.staffRepo
      .createQueryBuilder('staff')
      .innerJoinAndMapOne('staff.user', User, 'user', 'user.staffId = staff.id')
      .where('staff.isActive = :isActive', { isActive: true })
      .andWhere('staff.managedBy.id = :managedById', { managedById: user.id })
    if (position && position.length) {
      queryBuilder.andWhere('staff.position IN (:...positions)', {
        positions: position,
      })
    }
    queryBuilder.orderBy('staff.fullName', 'ASC')
    return queryBuilder.getMany()
  }

  async getStaff(staffId: Staff['id']): Promise<Staff> {
    const findStaff = await this.staffRepo
      .createQueryBuilder('staff')
      .select([
        'user.id',
        'user.email',
        'user.avatar',
        'user.fullName',
        'user.firstName',
        'user.lastName',
        'user.company',
        'user.phoneCode',
        'user.phoneNumber',
        'user.isActive',
        'staff',
        'certifications',
        'documents',
      ])
      .leftJoin(
        'staff.documents',
        'documents',
        `documents.type = :fileDocument`,
        { fileDocument: FILE_TYPE.DOCUMENT },
      )
      .leftJoin(
        'staff.certifications',
        'certifications',
        'certifications.type = :fileCert',
        { fileCert: FILE_TYPE.CERTIFICATION },
      )
      .innerJoinAndMapOne('staff.user', User, 'user', 'user.staffId = staff.id')
      .where('staff.id = :staffId', { staffId })
      .getOne()

    console.log(findStaff)

    if (findStaff.documents.length) {
      processLinkAsset(findStaff.documents, FILE_TYPE.DOCUMENT)
    }

    if (findStaff.certifications.length) {
      processLinkAsset(findStaff.certifications, FILE_TYPE.CERTIFICATION)
    }

    return findStaff
  }

  async updateStaff(
    payload: UpdateStaffDTO,
    staffId: Staff['id'],
    { user }: LoggedUserDTO,
  ): Promise<Staff> {
    const {
      fullName,
      phoneCode,
      phoneNumber,
      birthday,
      fileIds,
      removeFileIds,
      avatar,
      ...rest
    } = payload

    // let findStaff = await this.staffRepo
    //   .createQueryBuilder('staff')
    //   .where(
    //     '(staff.code = :code OR CONCAT(staff.phoneCode, staff.phoneNumber) = :phone)',
    //     {
    //       code: rest.code,
    //       phone: `${phoneCode}${phoneNumber}`,
    //     },
    //   )
    //   .andWhere('staff.id != :staffId', { staffId })
    //   .getOne()
    // if (findStaff) {
    //   throw new BadRequestException(MSG.STAFF_ALREADY_EXISTS)
    // }

    let findStaff = await this.staffRepo.findOne({
      where: {
        id: staffId,
        managedBy: {
          id: user.id,
        },
      },
      relations: ['documents', 'certifications'],
    } as FindOneOptions<Staff>)
    if (!findStaff) {
      throw new NotFoundException(MSG.STAFF_NOT_FOUND)
    }

    const nameParts = fullName.split(' ')
    const lastName = nameParts.pop()
    const firstName = nameParts.join(' ')

    if (fileIds && fileIds.length) {
      for (const fileId of fileIds) {
        const findFile = await this.fileRepo.findOne({
          where: { id: fileId },
        })
        if (findFile) {
          await this.fileRepo.save({
            staff: {
              id: staffId,
            },
            id: fileId,
          })
        }
      }
    }

    if (removeFileIds && removeFileIds.length) {
      for (const fileId of removeFileIds) {
        await this.fileRepo.update(fileId, {
          isDeleted: true,
          deletedAt: new Date(),
        })
      }
    }

    await this.staffRepo.save({
      ...rest,
      id: staffId,
      firstName,
      lastName,
      fullName,
      phoneCode,
      phoneNumber,
      birthday: moment(birthday).endOf('day').toDate(),
    })

    // update staff avatar if need
    if (avatar) {
      await this.userRepo.update({ staff: { id: staffId } }, { avatar })
    }

    findStaff = await this.staffRepo
      .createQueryBuilder('staff')
      .leftJoinAndSelect(
        'staff.documents',
        'documents',
        `documents.type = :fileDocument`,
        { fileDocument: FILE_TYPE.DOCUMENT },
      )
      .leftJoinAndSelect(
        'staff.certifications',
        'certifications',
        'certifications.type = :fileCert',
        { fileCert: FILE_TYPE.CERTIFICATION },
      )
      .where('staff.id = :staffId', { staffId })
      .getOne()

    if (findStaff.documents.length) {
      processLinkAsset(findStaff.documents, FILE_TYPE.DOCUMENT)
    }

    if (findStaff.certifications.length) {
      processLinkAsset(findStaff.certifications, FILE_TYPE.CERTIFICATION)
    }

    return findStaff
  }

  async deleteStaff(staffId: Staff['id'], { user }: LoggedUserDTO) {
    const findStaff = await this.staffRepo.findOne({
      where: {
        id: staffId,
        managedBy: {
          id: user.id,
        },
      },
    } as FindOneOptions<Staff>)

    if (!findStaff) {
      throw new NotFoundException(MSG.STAFF_NOT_FOUND)
    }

    const findUser = await this.userRepo.findOne({
      where: {
        staff: {
          id: findStaff.id,
        },
      },
    } as FindOneOptions<User>)

    if (findUser) {
      await this.userRepo.update(findUser.id, {
        isDeleted: true,
        deletedAt: new Date(),
      })
    }

    return this.staffRepo.update(staffId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }

  async updateActiveStaff(
    staffId: Staff['id'],
    body: UpdateActiveStaffDTO,
    { user }: LoggedUserDTO,
  ): Promise<boolean> {
    const staff = await this.staffRepo.findOne({
      where: {
        id: staffId,
        managedBy: {
          id: user.id,
        },
      },
    } as FindOneOptions<Staff>)
    if (!staff) {
      throw new NotFoundException(MSG.STAFF_NOT_FOUND)
    }

    const findUser = await this.userRepo.findOne({
      where: {
        staff: {
          id: staffId,
        },
      },
    } as FindOneOptions<User>)

    if (findUser) {
      await this.userRepo.update(findUser.id, {
        isActive: body?.isActive,
      })
    }

    await this.staffRepo.update(staffId, {
      isActive: body?.isActive,
    })

    return true
  }
}
