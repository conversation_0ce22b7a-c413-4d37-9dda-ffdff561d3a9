import { ROLE } from '@core/enums/role.enum'
import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common'
import { MSG } from '../../../utils/messages/common'

@Injectable()
export class PermitAdminGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request.user
    if (
      ![ROLE.SUPER_ADMIN, ROLE.ADMIN].includes(user.role.value.toLowerCase())
    ) {
      throw new BadRequestException(MSG.UNAUTHORIZED)
    }
    return true
  }
}

@Injectable()
export class SuperAdminGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request.user
    if (![ROLE.SUPER_ADMIN].includes(user.role.value.toLowerCase())) {
      throw new BadRequestException(MSG.UNAUTHORIZED)
    }
    return true
  }
}

@Injectable()
export class AdminGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request.user
    if (![ROLE.ADMIN].includes(user.role.value.toLowerCase())) {
      throw new BadRequestException(MSG.UNAUTHORIZED)
    }
    return true
  }
}

@Injectable()
export class AppGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request.user
    if (![ROLE.INSPECTOR, ROLE.STAFF].includes(user.role.value.toLowerCase())) {
      throw new BadRequestException(MSG.UNAUTHORIZED)
    }
    return true
  }
}

@Injectable()
export class InspectorGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request.user
    if (![ROLE.INSPECTOR].includes(user.role.value.toLowerCase())) {
      throw new BadRequestException(MSG.UNAUTHORIZED)
    }
    return true
  }
}

@Injectable()
export class StaffGuard implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request.user
    if (![ROLE.STAFF].includes(user.role.value.toLowerCase())) {
      throw new BadRequestException(MSG.UNAUTHORIZED)
    }
    return true
  }
}
