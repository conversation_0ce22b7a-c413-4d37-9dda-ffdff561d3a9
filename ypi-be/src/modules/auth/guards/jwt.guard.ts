import { IS_PUBLIC_KEY } from '@core/decorators/public.decorator'
import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { AuthGuard } from '@nestjs/passport'
import { Observable } from 'rxjs'
import { MSG } from '../../../utils/messages/common'

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') implements CanActivate {
  constructor(private reflector: Reflector) {
    super()
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [context.getHandler(), context.getClass()])
    if (!isPublic) {
      return super.canActivate(context)
    } else {
      return true
    }
  }

  handleRequest(err, user, info, context: ExecutionContext) {
    if (err || !user) {
      // You can throw an exception based on either "info" or "err" arguments
      throw err || new UnauthorizedException(MSG.UNAUTHORIZED)
    }
    return user
  }
}
