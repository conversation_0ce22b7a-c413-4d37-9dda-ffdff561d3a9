import { Auth<PERSON>ontroller } from '@modules/auth/auth.controller'
import { AuthService } from '@modules/auth/auth.service'
import { JwtAuthGuard } from '@modules/auth/guards/jwt.guard'
import {
  AdminGuard,
  PermitAdminGuard,
  SuperAdminGuard,
} from '@modules/auth/guards/role.guard'
import { JwtStrategy } from '@modules/auth/strategies/jwt.strategy'
import { S3Service } from '@modules/aws/s3.service'
import { MailerService } from '@modules/mailer/mailer.service'
import { RedisModule } from '@modules/redis/redis.module'
import { Role } from '@modules/role/role.entity'
import { RoleService } from '@modules/role/role.service'
import { Staff } from '@modules/staff/staff.entity'
import { Token } from '@modules/token/token.entity'
import { TokenService } from '@modules/token/token.service'
import { User } from '@modules/user/user.entity'
import { UserService } from '@modules/user/user.service'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { JwtModule } from '@nestjs/jwt'
import { PassportModule } from '@nestjs/passport'
import { TypeOrmModule } from '@nestjs/typeorm'

@Module({
  imports: [
    TypeOrmModule.forFeature([Token, User, Role, Staff]),
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('TOKEN_SECRET'),
        signOptions: {
          algorithm: 'HS256',
          expiresIn: configService.get<string>('TOKEN_EXPIRE'),
        },
      }),
      inject: [ConfigService],
    }),
    RedisModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    JwtAuthGuard,
    SuperAdminGuard,
    AdminGuard,
    PermitAdminGuard,
    TokenService,
    UserService,
    RoleService,
    MailerService,
    S3Service,
  ],
  exports: [JwtAuthGuard, SuperAdminGuard, AdminGuard, PermitAdminGuard],
})
export class AuthModule {}
