import { configService } from '@core/configs/env.config'
import { FILE_TYPE } from '@core/enums/file.enum'
import { UpdateMeDTO } from '@modules/auth/dto/auth.dto'
import { NoAuthGuard } from '@modules/auth/guards/no-auth.guard'
import { S3Service } from '@modules/aws/s3.service'
import { Staff } from '@modules/staff/staff.entity'
import { Token } from '@modules/token/token.entity'
import { TokenService } from '@modules/token/token.service'
import { User } from '@modules/user/user.entity'
import { UserService } from '@modules/user/user.service'
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UseGuards,
} from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { InjectRepository } from '@nestjs/typeorm'
import _ from 'lodash'
import { WINSTON_MODULE_PROVIDER } from 'nest-winston'
import { MSG } from 'src/utils/messages/common'
import { Repository } from 'typeorm'
import { Logger } from 'winston'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { getLinkAsset, processLinkAsset } from '../../utils/functions/image'

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Token)
    private tokenRepo: Repository<Token>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(Staff)
    private staffRepo: Repository<Staff>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    private readonly userService: UserService,
    private readonly s3Service: S3Service,
  ) {}

  @UseGuards(NoAuthGuard)
  async signin(payload: TSignin) {
    const userFinded = await this.userRepo.findOne({
      select: {
        id: true,
        passwordHash: true,
        passwordSalt: true,
        avatar: true,
        email: true,
        fullName: true,
        firstName: true,
        lastName: true,
        company: true,
        phoneNumber: true,
      },
      where: {
        email: payload.email,
        isActive: true,
      },
      relations: ['role', 'staff'],
    })
    if (!userFinded) throw new NotFoundException(MSG.USER_NOT_FOUND)
    const isValidPw = this.userService.comparePassword({
      password: payload.password,
      passwordHash: userFinded.passwordHash,
      passwordSalt: userFinded.passwordSalt,
    })
    if (!isValidPw) throw new BadRequestException(MSG.USER_PW_NOT_MATCH)
    const { access, refresh } = this.tokenService.generateToken(userFinded)
    const jwtAccess = this.jwtService.sign(access, {
      expiresIn: configService.get('TOKEN_EXPIRE'),
    })
    const jwtRefresh = this.jwtService.sign(refresh, {
      expiresIn: configService.get('TOKEN_EXPIRE'),
    })
    await this.tokenRepo.save({
      ...access,
      ...refresh,
      user: {
        id: userFinded.id,
      },
    })

    if (!userFinded.avatar?.startsWith('http')) {
      _.set(userFinded, 'avatar', getLinkAsset(userFinded.avatar))
    }

    return {
      accessToken: jwtAccess,
      accessTokenExp: access.accessTokenExp,
      refreshToken: jwtRefresh,
      user: { ...userFinded, passwordHash: undefined, passwordSalt: undefined },
    }
  }

  async signout({ user }: LoggedUserDTO) {
    return this.tokenService.revokeToken(user.id)
  }

  async getMe({ user }: LoggedUserDTO) {
    // const findUser = await this.userRepo.findOne({
    //   select: {
    //     id: true,
    //     email: true,
    //     avatar: true,
    //     fullName: true,
    //     firstName: true,
    //     lastName: true,
    //     company: true,
    //     phoneCode: true,
    //     phoneNumber: true,
    //     isActive: true,
    //   },
    //   where: { id: user.id },
    //   relations: [
    //     'role',
    //     'staff',
    //     'staff.documents',
    //     'staff.certifications',
    //   ],
    // })

    const findUser = await this.userRepo
      .createQueryBuilder('user')
      .select([
        'user.id',
        'user.email',
        'user.avatar',
        'user.fullName',
        'user.firstName',
        'user.lastName',
        'user.company',
        'user.phoneCode',
        'user.phoneNumber',
        'user.isActive',
      ])
      .leftJoinAndSelect('user.staff', 'staff')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect(
        'staff.documents',
        'documents',
        `documents.type = :fileDocument`,
        { fileDocument: FILE_TYPE.DOCUMENT },
      )
      .leftJoinAndSelect(
        'staff.certifications',
        'certifications',
        'certifications.type = :fileCert',
        { fileCert: FILE_TYPE.CERTIFICATION },
      )
      .where('user.id = :userId', { userId: user.id })
      .getOne()

    if (!findUser.avatar?.startsWith('http')) {
      _.set(findUser, 'avatar', getLinkAsset(findUser.avatar))
    }

    if (findUser.staff && findUser.staff.documents.length) {
      processLinkAsset(findUser.staff.documents, FILE_TYPE.DOCUMENT)
    }
    if (findUser.staff && findUser.staff.certifications.length) {
      processLinkAsset(findUser.staff.certifications, FILE_TYPE.CERTIFICATION)
    }
    return findUser
  }

  async updateMe(payload: UpdateMeDTO, { user }: LoggedUserDTO) {
    const { fullName, phoneCode, phoneNumber, company, address } = payload

    const nameParts = fullName.split(' ')
    const lastName = nameParts.pop()
    const firstName = nameParts.join(' ')

    if (user.staff) {
      let findStaff = await this.staffRepo.findOne({
        where: { id: user.staff.id },
        relations: ['managedBy'],
      })
      findStaff = await this.staffRepo
        .createQueryBuilder('staff')
        .where('staff.managedBy.id = :userId', {
          userId: findStaff.managedBy.id,
        })
        .andWhere('(CONCAT(staff.phoneCode, staff.phoneNumber) = :phone)', {
          phone: `${phoneCode}${phoneNumber}`,
        })
        .andWhere('staff.id != :staffId', { staffId: user.staff.id })
        .getOne()
      if (findStaff) {
        throw new BadRequestException(MSG.STAFF_PHONE_ALREADY_EXISTS)
      }
      await this.staffRepo.save({
        id: user.staff.id,
        firstName,
        lastName,
        fullName,
        phoneCode,
        phoneNumber,
        address,
      })
    }

    await this.userRepo.save({
      id: user.id,
      company,
      firstName,
      lastName,
      fullName,
      phoneCode,
      phoneNumber,
    })

    const findUser = await this.userRepo.findOne({
      select: {
        id: true,
        avatar: true,
        email: true,
        fullName: true,
        firstName: true,
        lastName: true,
        company: true,
        phoneNumber: true,
      },
      where: {
        id: user.id,
      },
      relations: ['role', 'staff'],
    })

    _.set(findUser, 'avatar', getLinkAsset(findUser.avatar))
    return findUser
  }

  async logout() {
    // await this.repo.delete({ accessToken: token })
    return
  }
}
