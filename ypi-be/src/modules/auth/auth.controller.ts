import { LoggedUser } from '@core/decorators/auth.decorator'
import { Public } from '@core/decorators/public.decorator'
import { SigninDTO } from '@modules/user/dto/signin.dto'
import { Body, Controller, Get, Post, Put } from '@nestjs/common'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { AuthService } from './auth.service'
import { SignInOutputDTO, UpdateMeDTO } from './dto/auth.dto'

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Get('me')
  async getMe(@LoggedUser() user: LoggedUserDTO): Promise<any> {
    const dtUser = await this.authService.getMe(user)
    return {
      data: dtUser,
    }
  }

  @Post('signin')
  @Public()
  async signin(
    @Body() body: SigninDTO,
  ): Promise<IApiResponse<SignInOutputDTO>> {
    const signinData = await this.authService.signin(body)
    return {
      data: signinData,
    }
  }

  @Post('signout')
  async signout(@LoggedUser() user: LoggedUserDTO) {
    await this.authService.signout(user)
    return {
      data: true,
    }
  }

  @Put('me')
  async updateMe(
    @Body() body: UpdateMeDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtUser = await this.authService.updateMe(body, user)
    return {
      data: dtUser,
    }
  }
}
