import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { Token } from '../../token/token.entity'
import { User } from '../../user/user.entity'

export class SignInOutputDTO {
  user?: Pick<User, 'username'>
  token?: Token
  role?: Token
}

export class UpdateMeDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fullName: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  phoneCode?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  phoneNumber?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  company?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  address?: string
}
