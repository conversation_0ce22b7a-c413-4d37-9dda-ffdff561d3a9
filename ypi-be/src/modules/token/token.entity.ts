import { BaseEntity } from '@core/databases/entities/base.entity'
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm'
import { User } from '../user/user.entity'

@Entity('token')
export class Token extends BaseEntity {
  @Column({ nullable: true })
  accessToken: string

  @Column({ nullable: true })
  accessTokenExp: string

  @Column({ nullable: true })
  accessTokenIat: string

  @Column({ nullable: true })
  refreshToken: string

  @Column({ nullable: true })
  refreshTokenExp: string

  @Column({ nullable: true })
  refreshTokenIat: string

  @Column({ nullable: true })
  deviceId: string

  @ManyToOne(() => User, (user) => user.token, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'userId' })
  user: User
}
