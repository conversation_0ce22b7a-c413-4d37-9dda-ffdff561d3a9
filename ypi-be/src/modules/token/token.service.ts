import { configService } from '@core/configs/env.config'
import { Token } from '@modules/token/token.entity'
import { User } from '@modules/user/user.entity'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { randomBytes } from 'node:crypto'
import { FindManyOptions, Repository } from 'typeorm'
import { TAccessToken, TGenerateToken, TRefreshToken } from '../../types/token'

@Injectable()
export class TokenService {
  constructor(
    @InjectRepository(Token)
    private tokenRepo: Repository<Token>,
  ) {}

  generateToken = ({ staff, ...user }: User): TGenerateToken => {
    const access: TAccessToken = {
      accessToken: randomBytes(8).toString('hex'),
      accessTokenExp: (
        Math.floor(Date.now() / 1000) +
        configService.get('TOKEN_EXPIRE') / 1000
      ).toString(),
      accessTokenIat: Math.floor(Date.now() / 1000).toString(),
      user: {
        ...user,
        passwordHash: undefined,
        passwordSalt: undefined,
        staff,
      },
    }
    const refresh: TRefreshToken = {
      refreshToken: randomBytes(8).toString('hex'),
      refreshTokenExp: (
        Math.floor(Date.now() / 1000) +
        configService.get('REFRESH_TOKEN_EXPIRE') / 1000
      ).toString(),
      refreshTokenIat: Math.floor(Date.now() / 1000).toString(),
      user: {
        ...user,
        passwordHash: undefined,
        passwordSalt: undefined,
        staff,
      },
    }
    return { access, refresh }
  }

  async revokeToken(userId: string) {
    const tokensToRemove = await this.tokenRepo.find({
      where: { user: { id: userId } },
    } as FindManyOptions<Token>)
    return this.tokenRepo.remove(tokensToRemove)
  }
}
