import { ASSIGN_TASK_STATUS } from '@core/enums/assign-task.enum'
import { FILE_TYPE } from '@core/enums/file.enum'
import { TANK_TYPE } from '@core/enums/tank.enum'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { S3Service } from '@modules/aws/s3.service'
import { Location } from '@modules/location/location.entity'
import { LocationService } from '@modules/location/location.service'
import { ScanTankCodeDTO } from '@modules/tank/dto/tank-material.dto'
import {
  CreateTankDTO,
  GetTankCleanDisinfectionDTO,
  GetTankCleanDisinfectionHistoryDTO,
  GetTankDTO,
  GetTankRemarkInspectorDTO,
  GetTanksDTO,
  UpdateActiveTankDTO,
  UpdateTankDTO,
} from '@modules/tank/dto/tank.dto'
import { TankMaterialService } from '@modules/tank/material/tank-material.service'
import { Tank } from '@modules/tank/tank.entity'
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Brackets, FindOneOptions, Repository } from 'typeorm'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { formatFilter } from '../../utils/functions/date-string'
import { getLinkAsset, processLinkAsset } from '../../utils/functions/image'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class TankService {
  constructor(
    @InjectRepository(Tank)
    public tankRepo: Repository<Tank>,
    @InjectRepository(AssignTask)
    private assignTaskRepo: Repository<AssignTask>,
    private readonly locationService: LocationService,
    private readonly tankMaterialService: TankMaterialService,
    private readonly s3Service: S3Service,
  ) {}

  async createTank(payload: CreateTankDTO): Promise<Tank> {
    const {
      effectiveCap,
      floorLevel,
      waterSaved,
      locationId,
      materialId,
      ...rest
    } = payload

    const findLocation = await this.locationService.locationRepo.findOne({
      where: { id: locationId },
      relations: ['customer'],
    })

    if (!findLocation) {
      throw new NotFoundException(MSG.LOCATION_NOT_FOUND)
    }

    const findMaterial =
      await this.tankMaterialService.tankMaterialRepo.findOne({
        where: { id: materialId },
      })

    if (!findMaterial) {
      throw new NotFoundException(MSG.MATERIAL_NOT_FOUND)
    }

    const findTank = await this.tankRepo
      .createQueryBuilder('tank')
      .where('(tank.code = :code)', {
        code: rest.code,
      })
      .getOne()

    if (findTank) {
      throw new BadRequestException(MSG.TANK_ALREADY_EXISTS)
    }

    return this.tankRepo.save({
      ...rest,
      effectiveCap,
      floorLevel,
      waterSaved,
      location: findLocation,
      material: findMaterial,
    })
  }

  async getTanks(
    { keyword, pageIndex, pageSize }: GetTanksDTO,
    locationId: Location['id'],
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)

    const findLocation = await this.locationService.locationRepo.findOne({
      where: { id: locationId },
      relations: ['customer'],
    })

    const tankQueryBuilder = this.tankRepo
      .createQueryBuilder('tank')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .skip(skip)
      .take(take)
    if (findLocation) {
      tankQueryBuilder.andWhere('location.id = :locationId', {
        locationId: findLocation.id,
      })
    }
    if (keyword) {
      tankQueryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('tank.airdentity ILIKE :keyword', {
            keyword: `%${keyword}%`,
          }).orWhere('tank.code ILIKE :keyword', { keyword: `%${keyword}%` })
        }),
      )
    }
    const [data, total] = await tankQueryBuilder.getManyAndCount()

    const statisticQueryBuilder = this.tankRepo
      .createQueryBuilder('tank')
      .select([
        'CAST(SUM(CASE WHEN tank.type = :low THEN 1 ELSE 0 END) AS NUMERIC) AS low',
        'CAST(SUM(CASE WHEN tank.type = :high THEN 1 ELSE 0 END) AS NUMERIC) AS high',
        'CAST(SUM(CASE WHEN tank.type = :intermediate THEN 1 ELSE 0 END) AS NUMERIC) AS intermediate',
      ])
      .setParameter('low', TANK_TYPE.LOW)
      .setParameter('high', TANK_TYPE.HIGH)
      .setParameter('intermediate', TANK_TYPE.INTERMEDIATE)
      .leftJoin('tank.location', 'location')
    if (findLocation) {
      statisticQueryBuilder.andWhere('location.id = :locationId', {
        locationId: findLocation.id,
      })
    }
    statisticQueryBuilder.groupBy('location.id')
    const statisticType = await statisticQueryBuilder.getRawOne()

    return {
      ...pagingResponse(data, total, pageIndex, pageSize),
      statisticType,
    }
  }

  async getTank(
    tankId: Tank['id'],
    { assignTaskId }: GetTankDTO,
  ): Promise<Tank> {
    const queryBuilder = this.tankRepo
      .createQueryBuilder('tank')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoinAndSelect('tank.assignTasks', 'assignTasks')
      .leftJoinAndSelect('assignTasks.staffs', 'staffs')
      .leftJoinAndSelect('staffs.staff', 'staff')
      .leftJoinAndSelect('tank.labReports', 'labReports')
      .where('tank.id = :id', { id: tankId })

    const taskRemaining = await this.assignTaskRepo
      .createQueryBuilder('assignTasks')
      .leftJoinAndSelect('assignTasks.tasks', 'tasks')
      .where('assignTasks.tankId = :tankId', { tankId })
      .andWhere('assignTasks.status != :status', {
        status: ASSIGN_TASK_STATUS.CONFIRMED,
      })
      .getExists()

    if (assignTaskId) {
      queryBuilder.andWhere('assignTasks.id = :assignTaskId', { assignTaskId })
    }

    const dtTank = await queryBuilder.getOne()
    let transformedData = null
    if (dtTank) {
      const { assignTasks, ...restTank } = dtTank
      if (restTank.labReports.length) {
        processLinkAsset(restTank.labReports, FILE_TYPE.LAB_REPORT)
      }
      transformedData = {
        ...restTank,
        assignTask:
          assignTaskId && assignTasks[0]
            ? {
                ...assignTasks[0],
                staffs: assignTasks[0]
                  ? assignTasks[0].staffs.map((ft) => ({ ...ft.staff }))
                  : null,
              }
            : null,
        canInactive: !taskRemaining,
      }
    }
    return transformedData
  }

  async getTankCleanDisinfection(
    tankId: Tank['id'],
    { pageIndex, pageSize }: GetTankCleanDisinfectionDTO,
  ): Promise<any> {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const withStatus = [
      ASSIGN_TASK_STATUS.PREPARE,
      ASSIGN_TASK_STATUS.UNRESOLVED,
      ASSIGN_TASK_STATUS.COMPLETED,
      ASSIGN_TASK_STATUS.CONFIRMED,
    ]

    const queryAssignTaskIds = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .select([
        'assignTask.code',
        'assignTask.phase',
        'assignTask.id',
        'assignTask.status',
        'assignTask.isStaffSubmitted',
      ])
      .leftJoin('assignTask.tank', 'tank')
      .where('assignTask.tank.id = :tankId', { tankId })
      .andWhere(
        new Brackets((qb) => {
          qb.where('assignTask.status IN (:...status)', { status: withStatus })
          qb.orWhere('assignTask.isStaffSubmitted = :isStaffSubmitted', {
            isStaffSubmitted: true,
          })
        }),
      )
      .orderBy('assignTask.phase')
      .distinct(true)
      .getRawMany()

    let assignTaskIds = []
    if (queryAssignTaskIds.length) {
      assignTaskIds = [...queryAssignTaskIds.map((item) => item.assignTask_id)]
    }

    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoin('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.tasks', 'parentTaskTasks')
      .leftJoinAndSelect('parentTaskTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTaskTasks.images', 'parentTaskImages')
      .where('tank.id = :tankId', { tankId })
      .orderBy('assignTask.startAt', 'ASC')
      .skip(skip)
      .take(take)

    if (assignTaskIds.length > 0) {
      queryBuilder.where('assignTask.id IN (:...assignTaskIds)', {
        assignTaskIds,
      })
    }

    const [data, total] = await queryBuilder.getManyAndCount()

    const transformedData = await Promise.all(
      data.map(async ({ parent, tasks, ...item }) => {
        let processedTasks
        let id
        if (item.status === ASSIGN_TASK_STATUS.CONFIRMED) {
          id = parent.id
          processedTasks = parent.tasks.map(({ defect, ...taskItem }) => {
            processLinkAsset(taskItem.images, FILE_TYPE.IMAGE)
            return {
              ...taskItem,
              title: defect.title,
              type: defect.type,
            }
          })
        } else {
          id = item.id
          processedTasks = tasks.map(({ defect, ...taskItem }) => {
            processLinkAsset(taskItem.images, FILE_TYPE.IMAGE)
            return {
              ...taskItem,
              title: defect.title,
              type: defect.type,
            }
          })
        }
        processedTasks.sort((a, b) => a.title.localeCompare(b.title))
        return {
          ...item,
          id,
          tasks: processedTasks,
        }
      }),
    )

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getTankCleanDisinfectionHistory(
    tankId: Tank['id'],
    { pageIndex, pageSize, datetime }: GetTankCleanDisinfectionHistoryDTO,
  ): Promise<any> {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const pDatetime = formatFilter(datetime, 'start')
    const date = new Date(pDatetime)
    const year = date.getFullYear()
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .leftJoinAndSelect('assignTask.labReports', 'labReports')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.staffs', 'parentStaffs')
      .leftJoinAndSelect('parentStaffs.staff', 'parentStaff')
      .leftJoinAndSelect('parent.tasks', 'parentTaskTasks')
      .leftJoinAndSelect('parentTaskTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTaskTasks.images', 'parentTaskImages')
      .where('assignTask.tank.id = :tankId', { tankId })
      .andWhere('assignTask.status = :status', {
        status: ASSIGN_TASK_STATUS.COMPLETED,
      })
      .andWhere('assignTask.isConfirmed = :isConfirmed', { isConfirmed: true })
      .skip(skip)
      .take(take)
    queryBuilder.andWhere('EXTRACT(YEAR FROM assignTask.startAt) = :year', {
      year,
    })
    queryBuilder.orderBy('assignTask.startAt', 'DESC')
    const [data, total] = await queryBuilder.getManyAndCount()
    // Handle transformed data return
    const transformedData = data.map(({ staffs, parent, ...item }) => {
      item.customerSignature = getLinkAsset(item.customerSignature)
      if (item.labReports.length) {
        processLinkAsset(item.labReports, FILE_TYPE.LAB_REPORT)
      }
      const tasks = item.tasks.map(({ staffs, defect, ...taskItem }) => {
        processLinkAsset(taskItem.images, FILE_TYPE.IMAGE)
        const dtBeforeDefect = parent?.tasks.find(
          (parentTask) => parentTask.defect.id === defect.id,
        )
        let before = null
        if (dtBeforeDefect) {
          processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
          before = {
            images: dtBeforeDefect.images,
            staffRemark: dtBeforeDefect.staffRemark,
          }
        }
        return {
          ...taskItem,
          title: defect.title,
          type: defect.type,
          defect: { id: defect.id },
          staffs: staffs.map((ft) => ({ ...ft.staff })),
          before,
        }
      })
      tasks.sort((a, b) => a.title.localeCompare(b.title))
      return {
        ...item,
        tasks,
        completedDate: item.completedDate,
        sampleTakenDate: item.sampleTakenDate,
        nonCompliance: {
          date: item.startAt,
          staffs: staffs.map((ft) => ({ ...ft.staff })),
          tasks: item.tasks.map(({ staffs, defect, ...taskItem }) => {
            const dtBeforeDefect = parent?.tasks.find(
              (parentTask) => parentTask.defect.id === defect.id,
            )
            if (dtBeforeDefect) {
              processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
              taskItem.images = dtBeforeDefect.images
              taskItem.staffRemark = dtBeforeDefect.staffRemark
            }
            return {
              ...taskItem,
              title: defect.title,
              type: defect.type,
              defect: { id: defect.id },
              staffs: staffs.map((ft) => ({ ...ft.staff }))
            }
          }).sort((a, b) => a.title.localeCompare(b.title))
        },
        preInspection: {
          date: parent?.startAt,
          staffs: parent?.staffs.map((ft) => ({ ...ft.staff })) || [],
          tasks: parent?.tasks.filter(parentTask => 
            !item.tasks.some(task => task.defect.id === parentTask.defect.id)
          ).map(({ staffs, defect, ...taskItem }) => ({
            ...taskItem,
            title: defect.title,
            type: defect.type,
            defect: { id: defect.id },
            staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : []
          })).sort((a, b) => a.title.localeCompare(b.title)) || []
        }
      }
    })
    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getTankRemarkInspector(
    tankId: Tank['id'],
    { pageIndex, pageSize }: GetTankRemarkInspectorDTO,
  ): Promise<any> {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoin('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect(
        'assignTask.inspectorSubmittedBy',
        'inspectorSubmittedBy',
      )
      .where('assignTask.tank.id = :tankId', { tankId })
      .andWhere('assignTask.isInspectorSubmitted = :isInspectorSubmitted', {
        isInspectorSubmitted: true,
      })
      .orderBy('assignTask.startAt', 'ASC')
      .skip(skip)
      .take(take)
    const [data, total] = await queryBuilder.getManyAndCount()
    const transformedData = await Promise.all(
      data.map(async ({ inspectorSubmittedBy, tasks, ...item }) => {
        const processedTasks = await Promise.all(
          tasks.map(async ({ defect, ...taskItem }) => {
            processLinkAsset(taskItem.images, FILE_TYPE.IMAGE)
            processLinkAsset(taskItem.inspectorRemarkImages, FILE_TYPE.IMAGE)
            return {
              ...taskItem,
              title: defect.title,
              type: defect.type,
            }
          }),
        )
        processedTasks.sort((a, b) => a.title.localeCompare(b.title))
        return {
          ...item,
          remarkBy: inspectorSubmittedBy
            ? {
                id: inspectorSubmittedBy.id,
                fullName: inspectorSubmittedBy.fullName,
                firstName: inspectorSubmittedBy.firstName,
                lastName: inspectorSubmittedBy.lastName,
              }
            : null,
          tasks: processedTasks,
        }
      }),
    )
    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async updateTank(payload: UpdateTankDTO, tankId: Tank['id']): Promise<Tank> {
    const {
      effectiveCap,
      floorLevel,
      waterSaved,
      locationId,
      materialId,
      ...rest
    } = payload

    const findLocation = await this.locationService.locationRepo.findOne({
      where: { id: locationId },
      relations: ['customer'],
    })
    if (!findLocation) {
      throw new NotFoundException(MSG.LOCATION_NOT_FOUND)
    }

    let findTank = await this.tankRepo
      .createQueryBuilder('tank')
      .where('(tank.code = :code)', {
        code: rest.code,
      })
      .andWhere('tank.id != :tankId', { tankId })
      .getOne()
    if (findTank) {
      throw new BadRequestException(MSG.TANK_ALREADY_EXISTS)
    }

    findTank = await this.tankRepo.findOne({
      where: {
        id: tankId,
        location: {
          id: locationId,
        },
      },
      relations: ['location', 'location.customer', 'material'],
    } as FindOneOptions<Tank>)
    if (!findTank) {
      throw new NotFoundException(MSG.TANK_NOT_FOUND)
    }

    const findMaterial =
      await this.tankMaterialService.tankMaterialRepo.findOne({
        where: { id: materialId },
      })
    if (!findMaterial) {
      throw new NotFoundException(MSG.MATERIAL_NOT_FOUND)
    }

    return this.tankRepo.save({
      ...findTank,
      ...rest,
      effectiveCap,
      floorLevel,
      waterSaved,
      location: findLocation,
      material: findMaterial,
    })
  }

  async deleteTank(tankId: Tank['id']) {
    const findTank = await this.tankRepo.findOne({
      where: {
        id: tankId,
      },
    })
    if (!findTank) {
      throw new NotFoundException(MSG.TANK_NOT_FOUND)
    }
    return this.tankRepo.update(tankId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }

  async scanTankByCode(payload: ScanTankCodeDTO) {
    const { code } = payload
    return this.tankRepo.findOne({
      where: { code },
      relations: ['material', 'location', 'location.customer'],
    })
  }

  async updateActiveTank(
    tankId: Tank['id'],
    body: UpdateActiveTankDTO,
    { user }: LoggedUserDTO,
  ): Promise<boolean> {
    const tank = await this.tankRepo.findOne({
      where: {
        id: tankId,
        location: {
          customer: {
            managedBy: {
              id: user.id,
            },
          },
        },
      },
    } as FindOneOptions<Tank>)
    if (!tank) {
      throw new NotFoundException(MSG.TANK_NOT_FOUND)
    }

    const findAssignTask = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .where('assignTask.tank.id = :tankId', { tankId })
      .getOne()
    if (findAssignTask) {
      throw new BadRequestException(MSG.TANK_CANNOT_BE_DELETED)
    }

    await this.tankRepo.update(tankId, {
      isActive: body?.isActive,
    })

    return true
  }
}
