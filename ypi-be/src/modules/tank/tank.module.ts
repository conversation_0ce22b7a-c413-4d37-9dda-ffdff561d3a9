import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { Tank } from '@modules/tank/tank.entity'
import { TankController } from '@modules/tank/tank.controller'
import { TankService } from '@modules/tank/tank.service'
import { TankMaterial } from '@modules/tank/material/tank-material.entity'
import { Location } from '@modules/location/location.entity'
import { TankMaterialModule } from '@modules/tank/material/tank-material.module'
import { LocationModule } from '@modules/location/location.module'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { AwsModule } from '@modules/aws/aws.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([Tank, TankMaterial, Location, AssignTask]),
    TankMaterialModule,
    LocationModule,
    AwsModule
  ],
  controllers: [TankController],
  providers: [TankService],
  exports: [TankService],
})
export class TankModule {
}
