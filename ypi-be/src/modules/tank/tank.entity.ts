import { BaseEntity } from '@core/databases/entities/base.entity'
import { TANK_SHAPE, TANK_TYPE } from '@core/enums/tank.enum'
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { Location } from '@modules/location/location.entity'
import { TankMaterial } from '@modules/tank/material/tank-material.entity'
import { File } from '@modules/file/file.entity'
import { AssignTask } from '@modules/assign-task/assign-task.entity'

@Entity('tank')
export class Tank extends BaseEntity {
  @Column({ nullable: true })
  airdentity: string

  @Column({ nullable: true })
  code: string

  @Column({
    type: 'enum',
    enum: TANK_TYPE,
    default: null,
  })
  type: TANK_TYPE

  @Column({
    type: 'enum',
    enum: TANK_SHAPE,
    default: null,
  })
  shape: TANK_SHAPE

  @Column({ nullable: true })
  length: string

  @Column({ nullable: true })
  width: string

  @Column({ nullable: true })
  height: string

  @Column({ nullable: true })
  effectiveCap: string

  @Column({ nullable: true })
  floorLevel: string

  @Column({ nullable: true })
  waterSaved: string

  @ManyToOne(() => Location, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'locationId' })
  location: Location

  @ManyToOne(() => TankMaterial, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'materialId' })
  material: TankMaterial

  @OneToMany(() => AssignTask, (assignTask) => assignTask.tank, { onDelete: 'SET NULL' })
  @JoinColumn()
  assignTasks: AssignTask[]

  @OneToMany(() => File, (file) => file.tank, { onDelete: 'SET NULL' })
  @JoinColumn()
  labReports: File[]

  @Column({ default: true })
  isActive: boolean
}
