import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { TankMaterial } from '@modules/tank/material/tank-material.entity'
import { TankMaterialService } from '@modules/tank/material/tank-material.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([TankMaterial]),
  ],
  controllers: [],
  providers: [TankMaterialService],
  exports: [TankMaterialService],
})
export class TankMaterialModule {
}
