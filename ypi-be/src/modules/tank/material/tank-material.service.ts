import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { TankMaterial } from '@modules/tank/material/tank-material.entity'
import { CreateTankMaterialDTO, UpdateTankMaterialDTO } from '@modules/tank/dto/tank-material.dto'
import { MSG } from '../../../utils/messages/common'

@Injectable()
export class TankMaterialService {
  constructor(
    @InjectRepository(TankMaterial)
    public tankMaterialRepo: Repository<TankMaterial>,
  ) {
  }

  async getAllowTankMaterials() {
    return this.tankMaterialRepo.find()
  }

  async createTankMaterial(payload: CreateTankMaterialDTO): Promise<TankMaterial> {
    const { name } = payload
    return this.tankMaterialRepo.save({ name })
  }

  async updateTankMaterial(payload: UpdateTankMaterialDTO, materialId: TankMaterial['id']): Promise<TankMaterial> {
    const { name } = payload

    const findMaterial = await this.tankMaterialRepo.findOne({
      where: { id: materialId },
    })

    if (!findMaterial) {
      throw new NotFoundException(MSG.MATERIAL_NOT_FOUND)
    }
    return this.tankMaterialRepo.save({
      id: materialId,
      name,
    })
  }
}
