import {
  IsNotEmpty,
  IsString,
} from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class CreateTankMaterialDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string
}

export class UpdateTankMaterialDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string
}

export class ScanTankCodeDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  code: string
}
