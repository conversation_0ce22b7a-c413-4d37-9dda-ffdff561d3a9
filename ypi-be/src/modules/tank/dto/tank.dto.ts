import { TANK_SHAPE, TANK_TYPE } from '@core/enums/tank.enum'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class CreateTankDTO {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  airdentity?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  code: string

  @ApiProperty({
    type: TANK_TYPE,
  })
  @IsEnum(TANK_TYPE)
  @IsNotEmpty()
  type: TANK_TYPE

  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  materialId: string

  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  locationId: string

  @ApiProperty({
    type: TANK_SHAPE,
  })
  @IsEnum(TANK_SHAPE)
  @IsNotEmpty()
  shape: TANK_SHAPE

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  length?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  width?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  height?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  effectiveCap?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  floorLevel?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  waterSaved?: string
}

export class UpdateTankDTO {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  airdentity?: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  code: string

  @ApiProperty({
    type: TANK_TYPE,
  })
  @IsEnum(TANK_TYPE)
  @IsNotEmpty()
  type: TANK_TYPE

  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  locationId: string

  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  materialId: string

  @ApiProperty({
    type: TANK_SHAPE,
  })
  @IsEnum(TANK_SHAPE)
  @IsNotEmpty()
  shape: TANK_SHAPE

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  length?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  width?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  height?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  effectiveCap?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  floorLevel?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  waterSaved?: string
}

export class GetTanksDTO extends PaginatedQueryParams {}

export class GetTankDTO {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @IsUUID('4')
  assignTaskId?: string
}

export class GetTankCleanDisinfectionDTO extends PaginatedQueryParams {}

export class GetTankRemarkInspectorDTO extends PaginatedQueryParams {}

export class GetTankCleanDisinfectionHistoryDTO extends PaginatedQueryParams {
  @ApiProperty()
  @IsNotEmpty()
  datetime: string
}

export class UpdateActiveTankDTO {
  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean
}
