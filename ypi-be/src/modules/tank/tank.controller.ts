import { ApiOperation } from '@nestjs/swagger'
import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common'
import { TankService } from '@modules/tank/tank.service'
import { AdminGuard, AppGuard, PermitAdminGuard } from '@modules/auth/guards/role.guard'
import { TankMaterialService } from '@modules/tank/material/tank-material.service'
import { CreateTankMaterialDTO, ScanTankCodeDTO, UpdateTankMaterialDTO } from '@modules/tank/dto/tank-material.dto'
import { TankMaterial } from '@modules/tank/material/tank-material.entity'
import { Location } from '@modules/location/location.entity'
import {
  CreateTankDTO,
  GetTankCleanDisinfectionDTO,
  GetTankCleanDisinfectionHistoryDTO,
  GetTankDTO,
  GetTankRemarkInspectorDTO,
  GetTanksDTO,
  UpdateActiveTankDTO,
  UpdateTankDTO,
} from '@modules/tank/dto/tank.dto'
import { Tank } from '@modules/tank/tank.entity'
import { LoggedUser } from '@core/decorators/auth.decorator'
import { LoggedUserDTO } from '../../commons/dto/logged'

@Controller('tank')
export class TankController {
  constructor(
    private tankService: TankService,
    private tankMaterialService: TankMaterialService,
  ) {
  }

  @Get('/material/allow')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Get allow tank materials' })
  async getAllowTankMaterials(): Promise<any> {
    const dtMaterial = await this.tankMaterialService.getAllowTankMaterials()
    return {
      data: dtMaterial,
    }
  }

  @Get('/location/:locationId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get tanks' })
  async getTanks(
    @Param('locationId') locationId: Location['id'],
    @Query() body: GetTanksDTO,
  ): Promise<any> {
    const dtTank = await this.tankService.getTanks(body, locationId)
    return {
      data: dtTank,
    }
  }


  @Get('/scan')
  @ApiOperation({ summary: 'Scan tank by code' })
  @UseGuards(AppGuard)
  async scanTankByCode(
    @Query() body: ScanTankCodeDTO
  ): Promise<any> {
    const dtTank = await this.tankService.scanTankByCode(body)
    return {
      data: dtTank,
    }
  }

  @Get('/:tankId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get tank' })
  async getTank(
    @Param('tankId') tankId: Tank['id'],
    @Query() body: GetTankDTO
  ): Promise<any> {
    const dtTank = await this.tankService.getTank(tankId, body)
    return {
      data: dtTank,
    }
  }

  @Get('/:tankId/clean-disinfection')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get info clean-disinfection of tank' })
  async getTankCleanDisinfection(
    @Param('tankId') tankId: Tank['id'],
    @Query() body: GetTankCleanDisinfectionDTO
  ): Promise<any> {
    const dtTank = await this.tankService.getTankCleanDisinfection(tankId, body)
    return {
      data: dtTank,
    }
  }

  @Get('/:tankId/remark-inspector')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get info remark of inspector in tank' })
  async getTankRemarkInspector(
    @Param('tankId') tankId: Tank['id'],
    @Query() body: GetTankRemarkInspectorDTO
  ): Promise<any> {
    const dtTank = await this.tankService.getTankRemarkInspector(tankId, body)
    return {
      data: dtTank,
    }
  }

  @Get('/:tankId/clean-disinfection/history')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get info clean-disinfection of tank' })
  async getTankCleanDisinfectionHistory(
    @Param('tankId') tankId: Tank['id'],
    @Query() body: GetTankCleanDisinfectionHistoryDTO
  ): Promise<any> {
    const dtTank = await this.tankService.getTankCleanDisinfectionHistory(tankId, body)
    return {
      data: dtTank,
    }
  }

  @Post('/material')
  @ApiOperation({ summary: 'Create tank material' })
  @UseGuards(PermitAdminGuard)
  async createTankMaterial(
    @Body() body: CreateTankMaterialDTO,
  ): Promise<any> {
    await this.tankMaterialService.createTankMaterial(body)
    return {
      data: true,
    }
  }

  @Put('/material/:materialId')
  @ApiOperation({ summary: 'Update tank material' })
  @UseGuards(PermitAdminGuard)
  async updateTankMaterial(
    @Param('materialId') materialId: TankMaterial['id'],
    @Body() body: UpdateTankMaterialDTO,
  ): Promise<any> {
    await this.tankMaterialService.updateTankMaterial(body, materialId)
    return {
      data: true,
    }
  }

  @Post('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Create tank' })
  async createTank(
    @Body() body: CreateTankDTO,
  ): Promise<any> {
    const dtTank = await this.tankService.createTank(body)
    return {
      data: dtTank,
    }
  }

  @Put('/:tankId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update tank' })
  async updateTank(
    @Body() body: UpdateTankDTO,
    @Param('tankId') tankId: Tank['id'],
  ): Promise<any> {
    const dtTank = await this.tankService.updateTank(body, tankId)
    return {
      data: dtTank,
    }
  }

  @Put('/active/:tankId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update active staff' })
  async updateActiveTank(
    @Param('tankId') tankId: Tank['id'],
    @LoggedUser() user: LoggedUserDTO,
    @Body() body: UpdateActiveTankDTO,
  ): Promise<IApiResponse<boolean>> {
    await this.tankService.updateActiveTank(tankId, body, user)
    return { data: true }
  }

  @Delete('/:tankId')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Delete tank' })
  async deleteTank(
    @Param('tankId') tankId: Tank['id'],
  ): Promise<any> {
    await this.tankService.deleteTank(tankId)
    return {
      data: true,
    }
  }
}
