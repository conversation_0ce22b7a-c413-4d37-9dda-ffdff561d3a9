import { Module } from '@nestjs/common';
import { InChargeController } from '@modules/in-charge/in-charge.controller'
import { InChargeService } from '@modules/in-charge/in-charge.service'
import { TypeOrmModule } from '@nestjs/typeorm'
import { InCharge } from '@modules/in-charge/in-charge.entity'

@Module({
  imports: [
    TypeOrmModule.forFeature([InCharge]),
  ],
  controllers: [InChargeController],
  providers: [InChargeService],
  exports: [InChargeService],
})
export class InChargeModule {}
