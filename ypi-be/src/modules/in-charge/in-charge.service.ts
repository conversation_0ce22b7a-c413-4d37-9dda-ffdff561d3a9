import { Injectable, NotFoundException } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { InCharge } from '@modules/in-charge/in-charge.entity'
import { CreateInChargeDTO, UpdateInChargeDTO } from '@modules/in-charge/dto/in-charge.dto'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class InChargeService {
  constructor(
    @InjectRepository(InCharge)
    public inChargeRepo: Repository<InCharge>,
  ) {
  }

  async createInCharge(payload: CreateInChargeDTO): Promise<InCharge> {
    const { name } = payload
    return this.inChargeRepo.save({ name })
  }

  async getAllowInCharges() {
    return this.inChargeRepo.find()
  }

  async updateInCharge(payload: UpdateInChargeDTO, inChargeId: InCharge['id']): Promise<InCharge> {
    const { name } = payload

    const findInCharge = await this.inChargeRepo.findOne({
      where: { id: inChargeId },
    })

    if (!findInCharge) {
      throw new NotFoundException(MSG.IN_CHARGE_NOT_FOUND)
    }

    return this.inChargeRepo.save({
      id: inChargeId,
      name,
    })
  }

  async deleteInCharge(inChargeId: InCharge['id']) {
    const findInCharge = await this.inChargeRepo.findOne({
      where: { id: inChargeId },
    })

    if (!findInCharge) {
      throw new NotFoundException(MSG.IN_CHARGE_NOT_FOUND)
    }

    return this.inChargeRepo.update(inChargeId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
