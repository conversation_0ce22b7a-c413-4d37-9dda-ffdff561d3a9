import { Controller, Get, Post, Body, Param, Delete, UseGuards, Put } from '@nestjs/common'
import { PermitAdminGuard } from '@modules/auth/guards/role.guard'
import { ApiOperation } from '@nestjs/swagger'
import { CreateInChargeDTO, UpdateInChargeDTO } from '@modules/in-charge/dto/in-charge.dto'
import { InChargeService } from '@modules/in-charge/in-charge.service'
import { InCharge } from '@modules/in-charge/in-charge.entity'

@Controller('in-charge')
export class InChargeController {
  constructor(private readonly inChargeService: InChargeService) {
  }

  @Post('/')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Create in-charge' })
  async createInCharge(
    @Body() body: CreateInChargeDTO,
  ): Promise<any> {
    await this.inChargeService.createInCharge(body)
    return {
      data: true,
    }
  }

  @Get('/allow')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Get allow in-charges' })
  async getAllowInCharges(): Promise<any> {
    const dtInCharge = await this.inChargeService.getAllowInCharges()
    return {
      data: dtInCharge,
    }
  }

  @Put('/:inChargeId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Update in-charge' })
  async updateInCharge(
    @Param('inChargeId') inChargeId: InCharge['id'],
    @Body() body: UpdateInChargeDTO,
  ): Promise<any> {
    await this.inChargeService.updateInCharge(body, inChargeId)
    return {
      data: true,
    }
  }

  @Delete('/:inChargeId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Delete in-charge' })
  async deleteInCharge(
    @Param('inChargeId') inChargeId: InCharge['id'],
  ): Promise<any> {
    await this.inChargeService.deleteInCharge(inChargeId)
    return {
      data: true,
    }
  }
}
