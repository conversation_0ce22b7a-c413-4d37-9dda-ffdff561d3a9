export interface ProjectData {
  PC: string
  'Building Name': string
  'Registered Site Address': string
}

export interface CustomerData {
  "PUB's Registered Customer (name)": string
  'PUB Account': string
  PIC: string
  'PIC Mobile': string
  'Office No.'?: string
  'Managing Agent / Contractor'?: string
}

export interface AgentData {
  'Managing Agent / Contractor': string
  'MA PIC': string
  Contact: string
  eMail: string
}

export interface ImportResult {
  total: number
  processed: number
  skipped: number
  errors: ImportError[]
}

export interface ImportError {
  file: string
  row: number
  error: string
}
