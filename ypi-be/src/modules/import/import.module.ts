import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { Agent } from '../agent/agent.entity'
import { AssignTask } from '../assign-task/assign-task.entity'
import { Building } from '../building/building.entity'
import { Customer } from '../customer/customer.entity'
import { InCharge } from '../in-charge/in-charge.entity'
import { Location } from '../location/location.entity'
import { ImportController } from './import.controller'
import { ImportService } from './import.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Customer,
      Building,
      Location,
      AssignTask,
      InCharge,
      Agent,
    ]),
  ],
  controllers: [ImportController],
  providers: [ImportService],
})
export class ImportModule {}
