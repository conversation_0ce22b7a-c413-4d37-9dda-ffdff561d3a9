import { AdminGuard } from '@modules/auth/guards/role.guard'
import {
  BadRequestException,
  Controller,
  Post,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express'
import { ApiConsumes, ApiOperation } from '@nestjs/swagger'
import { ImportService } from './import.service'

@Controller('import')
export class ImportController {
  constructor(private readonly importService: ImportService) {}

  @Post('/')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Import data' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'project', maxCount: 1 },
      { name: 'customer', maxCount: 1 },
      { name: 'agent', maxCount: 1 },
    ]),
  )
  async importData(
    @UploadedFiles()
    files: {
      project?: Express.Multer.File[]
      customer?: Express.Multer.File[]
      agent?: Express.Multer.File[]
    },
  ) {
    try {
      if (!files.project || !files.customer || !files.agent) {
        return {
          error: 'Missing required files',
          data: null,
        }
      }

      // Parse JSON files
      const projectData = JSON.parse(files.project[0].buffer.toString())
      const customerData = JSON.parse(files.customer[0].buffer.toString())
      const agentData = JSON.parse(files.agent[0].buffer.toString())

      const result = await this.importService.importData(
        projectData,
        customerData,
        agentData,
      )
      return {
        data: result,
      }
    } catch (error) {
      return {
        error: error.message,
        data: null,
      }
    }
  }

  @Post('fix-all-building-relations')
  @ApiOperation({
    summary: 'Fix all building relations based on project data',
    description:
      'Update building relationship for existing customers based on project data',
  })
  @UseInterceptors(FileInterceptor('file'))
  async fixAllBuildingRelations(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File is required')
    }

    try {
      const data = await this.importService.processExcelFile(file)
      if (!data?.project?.length) {
        throw new BadRequestException('No project data found')
      }

      const result = await this.importService.updateCustomerBuildings(
        data.project,
      )
      return {
        success: true,
        data: result,
        message: `Updated ${result.updated} customers, skipped ${result.skipped} customers in ${result.batchCount} batches. Run this API again if there are still customers without buildings.`,
      }
    } catch (error) {
      throw new BadRequestException(`Error: ${error.message}`)
    }
  }

  @Post('/fix-building-relations')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary:
      'Fix missing building relations by matching customer names with building names',
  })
  async fixBuildingRelations() {
    try {
      const result = await this.importService.fixImportIssues()
      return {
        data: result,
      }
    } catch (error) {
      return {
        error: error.message,
        data: null,
      }
    }
  }
}
