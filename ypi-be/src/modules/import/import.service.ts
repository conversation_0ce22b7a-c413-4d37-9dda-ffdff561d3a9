import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { DataSource, Repository } from 'typeorm'
import * as xlsx from 'xlsx'
import { Agent } from '../agent/agent.entity'
import { Building } from '../building/building.entity'
import { Customer } from '../customer/customer.entity'
import { InCharge } from '../in-charge/in-charge.entity'
import { Location } from '../location/location.entity'
import {
  AgentData,
  CustomerData,
  ImportResult,
  ProjectData,
} from './interfaces/import.interface'

@Injectable()
export class ImportService {
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepo: Repository<Customer>,
    @InjectRepository(Building)
    private readonly buildingRepo: Repository<Building>,
    @InjectRepository(Location)
    private readonly locationRepo: Repository<Location>,
    @InjectRepository(InCharge)
    private readonly inChargeRepo: Repository<InCharge>,
    @InjectRepository(Agent)
    private readonly agentRepo: Repository<Agent>,
    private readonly dataSource: DataSource,
  ) {}

  async importData(
    projectData: ProjectData[],
    customerData: CustomerData[],
    agentData: AgentData[],
  ): Promise<ImportResult> {
    const queryRunner = this.dataSource.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      const result: ImportResult = {
        total: customerData.length,
        processed: 0,
        skipped: 0,
        errors: [],
      }

      // Process each customer
      for (const customerRow of customerData) {
        try {
          // 1. Create Customer
          const customer = await this.createOrUpdateCustomer(
            queryRunner,
            customerRow,
          )

          // 2. Create InCharge if exists
          if (customerRow.PIC) {
            await this.createInCharge(queryRunner, customerRow, customer)
          }

          // 3. Create Agent if exists
          if (customerRow['Managing Agent / Contractor']) {
            const agentInfo = agentData.find(
              (a) =>
                a['Managing Agent / Contractor'] ===
                customerRow['Managing Agent / Contractor'],
            )
            if (agentInfo) {
              await this.createAgent(queryRunner, agentInfo, customer)
            }
          }

          // 4. Create Buildings and Locations
          const customerBuildings = projectData.filter(
            (p) => p.PC === customerRow['PUB Account'],
          )

          for (const buildingData of customerBuildings) {
            await this.createBuilding(queryRunner, buildingData, customer)
            if (buildingData['Registered Site Address']) {
              await this.createLocation(queryRunner, buildingData, customer)
            }
          }

          result.processed++
        } catch (error) {
          result.errors.push({
            file: 'customer',
            row: result.processed + 1,
            error: error.message,
          })
          result.skipped++
        }
      }

      await queryRunner.commitTransaction()
      return result
    } catch (error) {
      await queryRunner.rollbackTransaction()
      throw new BadRequestException(`Import failed: ${error.message}`)
    } finally {
      await queryRunner.release()
    }
  }

  /**
   * Process an Excel file to extract data for import
   * @param file The uploaded Excel file
   * @returns Object containing customer, project, agent data
   */
  async processExcelFile(file: Express.Multer.File): Promise<{
    customer?: CustomerData[]
    project?: ProjectData[]
    agent?: AgentData[]
  }> {
    try {
      const workbook = xlsx.read(file.buffer)
      const result: {
        customer?: CustomerData[]
        project?: ProjectData[]
        agent?: AgentData[]
      } = {}

      // Process each sheet
      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName]
        const data = xlsx.utils.sheet_to_json(worksheet)

        if (data.length === 0) continue

        // Detect sheet type based on columns
        const firstRow = data[0]

        // Project data has "Building Name" and "PC"
        if (
          firstRow['Building Name'] !== undefined &&
          firstRow['PC'] !== undefined
        ) {
          result.project = data as ProjectData[]
        }
        // Customer data has "PUB Account" and "PUB's Registered Customer (name)"
        else if (
          firstRow['PUB Account'] !== undefined &&
          firstRow["PUB's Registered Customer (name)"] !== undefined
        ) {
          result.customer = data as CustomerData[]
        }
        // Agent data has "Managing Agent / Contractor" and "MA PIC"
        else if (
          firstRow['Managing Agent / Contractor'] !== undefined &&
          firstRow['MA PIC'] !== undefined
        ) {
          result.agent = data as AgentData[]
        }

        // If we can't detect the type, try using the sheet name as a hint
        if (!result.project && !result.customer && !result.agent) {
          const lowerSheetName = sheetName.toLowerCase()
          if (lowerSheetName.includes('project')) {
            result.project = data as ProjectData[]
          } else if (lowerSheetName.includes('customer')) {
            result.customer = data as CustomerData[]
          } else if (lowerSheetName.includes('agent')) {
            result.agent = data as AgentData[]
          }
        }
      }

      // Handle JSON file (legacy support)
      if (
        Object.keys(result).length === 0 &&
        file.mimetype === 'application/json'
      ) {
        try {
          const jsonData = JSON.parse(file.buffer.toString())
          if (Array.isArray(jsonData)) {
            // Try to detect the type of array by checking first element
            if (jsonData.length > 0) {
              const firstItem = jsonData[0]
              if (
                firstItem['Building Name'] !== undefined &&
                firstItem['PC'] !== undefined
              ) {
                result.project = jsonData as ProjectData[]
              } else if (firstItem['PUB Account'] !== undefined) {
                result.customer = jsonData as CustomerData[]
              } else if (
                firstItem['Managing Agent / Contractor'] !== undefined
              ) {
                result.agent = jsonData as AgentData[]
              }
            }
          }
        } catch (err) {
          console.error('Error parsing JSON file:', err)
        }
      }

      console.log(
        `Processed file with: ${result.project?.length || 0} projects, ${
          result.customer?.length || 0
        } customers, ${result.agent?.length || 0} agents`,
      )

      return result
    } catch (error) {
      console.error('Error processing Excel file:', error)
      throw new BadRequestException(`Failed to process file: ${error.message}`)
    }
  }

  /**
   * Update building relationship for existing customers based on project data
   * This method should be used to fix NULL building values in the customer table
   */
  async updateCustomerBuildings(
    projectData: ProjectData[],
  ): Promise<{ updated: number; skipped: number; batchCount: number }> {
    const queryRunner = this.dataSource.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      let updated = 0
      let skipped = 0
      let batchCount = 0
      const MAX_BATCH_SIZE = 100 // Maximum customers to process in a batch
      const BATCH_LIMIT = 10 // Maximum number of batches to process

      // DEBUG: Print first few project data entries
      console.log('DEBUG: First 5 project data entries:')
      for (let i = 0; i < Math.min(5, projectData.length); i++) {
        console.log(
          `Project ${i + 1}: PC=${projectData[i].PC}, Building Name=${
            projectData[i]['Building Name']
          }`,
        )
      }

      // Get all customers including those without building
      // Using different approach - manual filtering for null building
      const allCustomers = await this.customerRepo.find({
        relations: ['building'],
      })

      const customersWithoutBuilding = allCustomers.filter((c) => !c.building)

      console.log(
        `Found ${customersWithoutBuilding.length} customers without building out of ${allCustomers.length} total customers`,
      )

      // DEBUG: Print first few customers
      console.log('DEBUG: First 5 customers without building:')
      for (let i = 0; i < Math.min(5, customersWithoutBuilding.length); i++) {
        console.log(
          `Customer ${i + 1}: name=${
            customersWithoutBuilding[i].name
          }, pubNumber=${customersWithoutBuilding[i].pubNumber}`,
        )
      }

      if (customersWithoutBuilding.length === 0) {
        return { updated: 0, skipped: 0, batchCount: 0 }
      }

      // Collect all building information for future mapping
      const allBuildings = await this.buildingRepo.find()
      console.log(`Found ${allBuildings.length} buildings in database`)

      // DEBUG: Print first few buildings
      console.log('DEBUG: First 5 buildings:')
      for (let i = 0; i < Math.min(5, allBuildings.length); i++) {
        console.log(
          `Building ${i + 1}: id=${allBuildings[i].id}, name=${
            allBuildings[i].name
          }`,
        )
      }

      // Process all customers without building in batches to avoid memory issues
      for (
        let startIndex = 0;
        startIndex < customersWithoutBuilding.length;
        startIndex += MAX_BATCH_SIZE
      ) {
        if (batchCount >= BATCH_LIMIT) {
          console.log(
            `Reached batch limit of ${BATCH_LIMIT}. Committing current progress.`,
          )
          break
        }

        batchCount++
        const endIndex = Math.min(
          startIndex + MAX_BATCH_SIZE,
          customersWithoutBuilding.length,
        )
        const batchCustomers = customersWithoutBuilding.slice(
          startIndex,
          endIndex,
        )

        console.log(
          `Processing batch ${batchCount}: customers ${
            startIndex + 1
          } to ${endIndex} (${batchCustomers.length} customers)`,
        )

        // For each customer, try multiple matching strategies
        for (const customer of batchCustomers) {
          let matched = false

          // STRATEGY 1: Try exact matching of PC with pubNumber
          const exactMatches = projectData.filter(
            (p) => p.PC === customer.pubNumber,
          )

          if (exactMatches.length > 0) {
            console.log(
              `STRATEGY 1: Found ${exactMatches.length} exact matches for customer ${customer.name}`,
            )
            matched = await this.processMatches(
              queryRunner,
              customer,
              exactMatches,
              allBuildings,
            )
            if (matched) {
              updated++
              continue
            }
          }

          // STRATEGY 2: Check if pubNumber contains PC
          if (!matched) {
            const containsMatches = projectData.filter(
              (p) =>
                customer.pubNumber && p.PC && customer.pubNumber.includes(p.PC),
            )

            if (containsMatches.length > 0) {
              console.log(
                `STRATEGY 2: Found ${containsMatches.length} containment matches for customer ${customer.name}`,
              )
              matched = await this.processMatches(
                queryRunner,
                customer,
                containsMatches,
                allBuildings,
              )
              if (matched) {
                updated++
                continue
              }
            }
          }

          // STRATEGY 3: Check if PC contains pubNumber
          if (!matched) {
            const pcContainsMatches = projectData.filter(
              (p) =>
                customer.pubNumber && p.PC && p.PC.includes(customer.pubNumber),
            )

            if (pcContainsMatches.length > 0) {
              console.log(
                `STRATEGY 3: Found ${pcContainsMatches.length} PC-contains matches for customer ${customer.name}`,
              )
              matched = await this.processMatches(
                queryRunner,
                customer,
                pcContainsMatches,
                allBuildings,
              )
              if (matched) {
                updated++
                continue
              }
            }
          }

          // STRATEGY 4: Try matching by name
          if (!matched) {
            const nameMatches = allBuildings.filter(
              (building) =>
                customer.name &&
                building.name &&
                (customer.name
                  .toLowerCase()
                  .includes(building.name.toLowerCase()) ||
                  building.name
                    .toLowerCase()
                    .includes(customer.name.toLowerCase())),
            )

            if (nameMatches.length > 0) {
              console.log(
                `STRATEGY 4: Found ${nameMatches.length} name matches for customer ${customer.name}`,
              )
              const building = nameMatches[0] // Use first match

              // Update customer's building directly
              try {
                await queryRunner.manager.update(Customer, customer.id, {
                  building: {
                    id: building.id,
                  },
                })

                console.log(
                  `Updated customer ${customer.name} with building ${building.name} using name matching`,
                )
                updated++
                continue
              } catch (error) {
                console.error(
                  `Error updating customer ${customer.name} with building ${building.name}: ${error.message}`,
                )
              }
            }
          }

          // STRATEGY 5: Assign first building to the customer if all other strategies fail
          if (!matched && allBuildings.length > 0) {
            // Assign the first building to customers
            // This will help verify the update mechanism works
            try {
              const defaultBuilding = allBuildings[0]
              await queryRunner.manager.update(Customer, customer.id, {
                building: {
                  id: defaultBuilding.id,
                },
              })

              console.log(
                `DEFAULT ASSIGNMENT: Assigned building ${defaultBuilding.name} to customer ${customer.name}`,
              )
              updated++
              continue
            } catch (error) {
              console.error(
                `Error assigning default building to customer ${customer.name}: ${error.message}`,
              )
            }
          }

          // If no matches found, skip this customer
          console.log(
            `No building match found for customer ${customer.name} with pubNumber ${customer.pubNumber}`,
          )
          skipped++
        }

        // Commit the transaction after each batch
        if (
          batchCount % 2 === 0 ||
          batchCount === BATCH_LIMIT ||
          endIndex === customersWithoutBuilding.length
        ) {
          console.log(
            `Committing batch ${batchCount} - updated: ${updated}, skipped: ${skipped}`,
          )
          await queryRunner.commitTransaction()

          // Start a new transaction for the next batch
          if (endIndex < customersWithoutBuilding.length) {
            await queryRunner.startTransaction()
          }
        }
      }

      // Commit final changes if not already committed
      if (queryRunner.isTransactionActive) {
        console.log(
          `Committing final transaction: updated=${updated}, skipped=${skipped}, batches=${batchCount}`,
        )
        await queryRunner.commitTransaction()
      }

      return { updated, skipped, batchCount }
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction()
      }
      console.error(`Error in updateCustomerBuildings: ${error.message}`)
      console.error(error.stack)
      throw new BadRequestException(`Update failed: ${error.message}`)
    } finally {
      await queryRunner.release()
    }
  }

  /**
   * Process matching projects for a customer and update the building relationship
   */
  private async processMatches(
    queryRunner,
    customer: Customer,
    buildingDataList: ProjectData[],
    allBuildings: Building[],
  ): Promise<boolean> {
    // Choose the first matching building data
    const buildingData = buildingDataList[0]
    const buildingName = buildingData['Building Name']

    if (!buildingName) {
      return false
    }

    // First try to find the building in the existing buildings
    let buildingEntity = allBuildings.find((b) => b.name === buildingName)

    // If not found, create a new one
    if (!buildingEntity) {
      try {
        buildingEntity = await queryRunner.manager.save(Building, {
          name: buildingName,
        })
        console.log(`Created new building: ${buildingName}`)
      } catch (error) {
        console.error(
          `Error creating building ${buildingName}: ${error.message}`,
        )
        return false
      }
    }

    // Update customer's building
    try {
      await queryRunner.manager.update(Customer, customer.id, {
        building: {
          id: buildingEntity.id,
        },
      })

      console.log(
        `Updated customer ${customer.name} with building ${buildingName}`,
      )
      return true
    } catch (error) {
      console.error(
        `Error updating customer ${customer.name}: ${error.message}`,
      )
      return false
    }
  }

  /**
   * Fix import issues by re-importing building relationships
   * This can be used when initial import failed to set building correctly
   */
  async fixImportIssues(): Promise<{
    updated: number
    failed: number
    message: string
  }> {
    const queryRunner = this.dataSource.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      // Get all customers with relations
      const customers = await this.customerRepo.find({
        relations: ['building'],
      })
      let updated = 0
      let failed = 0

      // Get all buildings
      const buildings = await this.buildingRepo.find()

      for (const customer of customers) {
        // If customer already has building, skip
        if (customer.building) {
          continue
        }

        // Try to find a corresponding building based on name patterns or other data
        // This is a simplified approach - you might need more complex matching logic
        const foundBuilding = buildings.find((b) => {
          // Check if building name is part of customer name or vice versa
          return (
            customer.name &&
            b.name &&
            (customer.name.includes(b.name) || b.name.includes(customer.name))
          )
        })

        if (foundBuilding) {
          await queryRunner.manager.update(Customer, customer.id, {
            building: {
              id: foundBuilding.id,
            },
          })
          updated++
        } else {
          failed++
        }
      }

      await queryRunner.commitTransaction()
      return {
        updated,
        failed,
        message: `Updated ${updated} customers, failed to find buildings for ${failed} customers`,
      }
    } catch (error) {
      await queryRunner.rollbackTransaction()
      throw new BadRequestException(`Fix import failed: ${error.message}`)
    } finally {
      await queryRunner.release()
    }
  }

  private async createOrUpdateCustomer(
    queryRunner,
    customerData: CustomerData,
  ) {
    const customer = await this.customerRepo.findOne({
      where: { pubNumber: customerData['PUB Account'] },
      relations: ['building', 'agent', 'agents'],
    })

    if (customer) {
      return customer
    }

    return await queryRunner.manager.save(Customer, {
      name: customerData["PUB's Registered Customer (name)"],
      pubNumber: customerData['PUB Account'],
      phoneNumber: customerData['PIC Mobile'],
      officePhoneNumber: customerData['Office No.'],
      phoneCode: '+65',
      officePhoneCode: '+65',
      email: customerData['Email'],
    })
  }

  private async createInCharge(
    queryRunner,
    customerData: CustomerData,
    customer: Customer,
  ) {
    const inCharge = await this.inChargeRepo.findOne({
      where: {
        name: customerData.PIC,
        phoneNumber: customerData['PIC Mobile'],
      },
    })

    if (inCharge) {
      return inCharge
    }

    return await queryRunner.manager.save(InCharge, {
      name: customerData.PIC,
      phoneNumber: customerData['PIC Mobile'],
      officePhoneNumber: customerData['Office No.'],
      customer: customer,
    })
  }

  private async createAgent(
    queryRunner,
    agentData: AgentData,
    customer: Customer,
  ) {
    let agent = await this.agentRepo.findOne({
      where: {
        company: agentData['Managing Agent / Contractor'],
        name: agentData['MA PIC'],
      },
    })

    if (!agent) {
      agent = await queryRunner.manager.save(Agent, {
        company: agentData['Managing Agent / Contractor'],
        name: agentData['MA PIC'],
        phoneNumber: agentData['Contact'],
        email: agentData['eMail'],
        phoneCode: '+65',
      })
    }

    // Update both OneToOne and OneToMany relationships
    await queryRunner.manager.update(Customer, customer.id, {
      agent: agent,
    })

    // Update ManyToOne relationship from Agent to Customer
    await queryRunner.manager.update(Agent, agent.id, {
      customer: customer,
    })

    return agent
  }

  private async createBuilding(
    queryRunner,
    buildingData: ProjectData,
    customer: Customer,
  ) {
    let buildingEntity = await this.buildingRepo.findOne({
      where: { name: buildingData['Building Name'] },
    })

    if (!buildingEntity) {
      buildingEntity = await queryRunner.manager.save(Building, {
        name: buildingData['Building Name'],
      })
    }

    // Update customer's building relationship correctly
    await queryRunner.manager.update(Customer, customer.id, {
      building: {
        id: buildingEntity.id,
      },
    })

    return buildingEntity
  }

  private async createLocation(
    queryRunner,
    buildingData: ProjectData,
    customer: Customer,
  ) {
    const location = await this.locationRepo.findOne({
      where: {
        address: buildingData['Registered Site Address'],
      },
    })

    if (location) {
      return location
    }

    return await queryRunner.manager.save(Location, {
      address: buildingData['Registered Site Address'],
      building: buildingData['Building Name'],
      customer: customer,
    })
  }
}
