import { AssignStaff } from '@modules/assign-task/assign-staff/assign-staff.entity'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { AwsModule } from '@modules/aws/aws.module'
import { Defect } from '@modules/defect/defect.entity'
import { File } from '@modules/file/file.entity'
import { TaskStaff } from '@modules/task-staff/task-staff.entity'
import { TaskController } from '@modules/task/task.controller'
import { Task } from '@modules/task/task.entity'
import { TaskService } from '@modules/task/task.service'
import { User } from '@modules/user/user.entity'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Task,
      AssignTask,
      AssignStaff,
      Defect,
      File,
      TaskStaff,
      User,
    ]),
    AwsModule,
  ],
  controllers: [TaskController],
  providers: [TaskService],
  exports: [TaskService],
})
export class TaskModule {}
