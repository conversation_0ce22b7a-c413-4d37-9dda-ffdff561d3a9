import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class GetRectifyTasksDTO extends PaginatedQueryParams {}

export class GetInspectorRemarkTasksDTO extends PaginatedQueryParams {
  @ApiProperty()
  @IsNotEmpty()
  datetime: string
}

export class GetStaffTasksDTO extends PaginatedQueryParams {
  @ApiProperty()
  @IsNotEmpty()
  startTime: string

  @ApiProperty()
  @IsNotEmpty()
  endTime: string
}

export class CreateNewDefectDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  qty?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  size?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  imageIds?: string[]
}

export class UpdateNewDefectDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  qty?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  size?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  imageIds?: string[]

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  removeImageIds?: string[]
}

export class UpdateExtraDefectDTO {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  qty?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  size?: string
}

export class RequestRectifyDefectDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  taskIds?: string[]
}

export class InspectorSubmitDefectDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string
}

export class StaffAddRemarkDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  taskId: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  remark: string
}

export class InspectorAddRemarkDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  taskId: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  remark: string
}

export class InspectorAddRemarkImagesDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  taskId: string

  @ApiProperty()
  @IsArray()
  @IsUUID('all', { each: true })
  imageIds: string[]
}

export class StaffReceiveSignatureDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  // @IsUUID('all', { each: true })
  completeIds: string[]

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  // @IsUUID('all', { each: true })
  unCompleteIds?: string[]
}

export class GetTaskCalendarFormatDTO {
  @ApiPropertyOptional()
  @IsOptional()
  startTime?: string

  @ApiPropertyOptional()
  @IsOptional()
  endTime?: string
}
