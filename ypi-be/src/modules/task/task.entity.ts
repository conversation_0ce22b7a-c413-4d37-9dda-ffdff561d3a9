import { BaseEntity } from '@core/databases/entities/base.entity'
import { TASK_STATUS } from '@core/enums/task.enum'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { Defect } from '@modules/defect/defect.entity'
import { File } from '@modules/file/file.entity'
import { Staff } from '@modules/staff/staff.entity'
import { TaskStaff } from '@modules/task-staff/task-staff.entity'
import { User } from '@modules/user/user.entity'
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm'

@Entity('task')
export class Task extends BaseEntity {
  @ManyToOne(() => Defect, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'defectId' })
  defect: Defect

  @ManyToOne(() => AssignTask, (assignTask) => assignTask.tasks)
  @JoinColumn({ name: 'assignTaskId' })
  assignTask: AssignTask

  @Column({ nullable: true })
  inspectorRemark: string

  @OneToMany(() => File, (file) => file.inspectorRemark, {
    onDelete: 'SET NULL',
  })
  @JoinColumn()
  inspectorRemarkImages: File[]

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'inspectorRemarkBy' })
  inspectorRemarkBy: User

  @Column({ nullable: true })
  staffRemark: string

  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'staffRemarkBy' })
  staffRemarkBy: Staff

  @OneToMany(() => TaskStaff, (taskStaff) => taskStaff.task)
  @JoinColumn({ name: 'staffs' })
  staffs: TaskStaff[]

  @OneToMany(() => File, (file) => file.task, { onDelete: 'SET NULL' })
  @JoinColumn()
  images: File[]

  @Column({ nullable: true })
  customerSignature: string

  @Column({ type: 'timestamptz', nullable: true })
  customerSignatureDate: Date

  @Column({ type: 'timestamptz', nullable: true })
  completedDate: Date

  @ManyToOne(() => Staff, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'receiveSignatureBy' })
  receiveSignatureBy: Staff

  @Column({ nullable: true })
  qty: number

  @Column({ nullable: true })
  size: string

  @Column({
    type: 'enum',
    enum: TASK_STATUS,
    default: TASK_STATUS.NEW,
  })
  status: TASK_STATUS

  @Column({ default: false })
  extra: boolean
}
