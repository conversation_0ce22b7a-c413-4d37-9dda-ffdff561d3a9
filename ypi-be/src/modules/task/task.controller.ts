import { LoggedUser } from '@core/decorators/auth.decorator'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import {
  AdminGuard,
  InspectorGuard,
  StaffGuard,
} from '@modules/auth/guards/role.guard'
import { Tank } from '@modules/tank/tank.entity'
import {
  CreateNewDefectDTO,
  GetInspectorRemarkTasksDTO,
  GetRectifyTasksDTO,
  GetStaffTasksDTO,
  GetTaskCalendarFormatDTO,
  InspectorAddRemarkDTO,
  InspectorAddRemarkImagesDTO,
  InspectorSubmitDefectDTO,
  RequestRectifyDefectDTO,
  StaffAddRemarkDTO,
  StaffReceiveSignatureDTO,
  UpdateExtraDefectDTO,
  UpdateNewDefectDTO,
} from '@modules/task/dto/task.dto'
import { Task } from '@modules/task/task.entity'
import { TaskService } from '@modules/task/task.service'
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiOperation } from '@nestjs/swagger'
import { LoggedUserDTO } from '../../commons/dto/logged'

@Controller('task')
export class TaskController {
  constructor(private taskService: TaskService) {}

  @Get('/inspector/rectify/:tankId')
  @ApiOperation({ summary: 'Get tasks of inspector rectify' })
  @UseGuards(InspectorGuard)
  async getRectifyTasks(
    @Param('tankId') tankId: Tank['id'],
    @Query() body: GetRectifyTasksDTO,
  ): Promise<any> {
    const dtTask = await this.taskService.getRectifyTasks(tankId, body)
    return {
      data: dtTask,
    }
  }

  @Get('/inspector/remark/:tankId')
  @ApiOperation({ summary: 'Get tasks of inspector remark' })
  @UseGuards(InspectorGuard)
  async getInspectorRemarkTasks(
    @Param('tankId') tankId: Tank['id'],
    @Query() body: GetInspectorRemarkTasksDTO,
  ): Promise<any> {
    const dtTask = await this.taskService.getInspectorRemarkTasks(tankId, body)
    return {
      data: dtTask,
    }
  }

  @Get('/inspector/working/:assignTaskId')
  @ApiOperation({ summary: 'Get task working of inspector' })
  @UseGuards(InspectorGuard)
  async getInspectorWorkingTask(
    @Param('assignTaskId') assignTaskId: AssignTask['id'],
    // @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.taskService.getInspectorWorkingTask(assignTaskId)
    return {
      data: dtTask,
    }
  }

  @Get('/staff/working/:assignTaskId')
  @ApiOperation({ summary: 'Get task working of staff' })
  @UseGuards(StaffGuard)
  async getStaffWorkingTask(
    @Param('assignTaskId') assignTaskId: AssignTask['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.taskService.getStaffWorkingTask(
      user,
      assignTaskId,
    )
    return {
      data: dtTask,
    }
  }

  @Get('/calendar')
  @ApiOperation({ summary: 'Get tasks with calendar format' })
  @UseGuards(AdminGuard)
  async getTaskCalendarFormat(
    @Query() body: GetTaskCalendarFormatDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.taskService.getTaskCalendarFormat(user, body)
    return {
      data: dtTask,
    }
  }

  @Get('/staff')
  @ApiOperation({ summary: 'Get tasks of staff' })
  @UseGuards(StaffGuard)
  async getStaffTasks(
    @Query() body: GetStaffTasksDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.taskService.getStaffTasks(user, body)
    return {
      data: dtTask,
    }
  }

  @Post('/staff/receive/signature')
  @UseInterceptors(FileInterceptor('signature'))
  @UseGuards(StaffGuard)
  async staffReceiveSignature(
    @UploadedFile() signature: Express.Multer.File,
    @Body() body: StaffReceiveSignatureDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtSignature = await this.taskService.staffReceiveSignature(
      signature,
      user,
      body,
    )
    return {
      data: dtSignature,
    }
  }

  @Post('/staff/request-rectify')
  @ApiOperation({ summary: 'Staff request rectify defect' })
  @UseGuards(StaffGuard)
  async requestRectifyDefect(
    @Body() body: RequestRectifyDefectDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.taskService.requestRectifyDefect(body, user)
    return {
      data: true,
    }
  }

  @Post('/inspector/submit')
  @ApiOperation({ summary: 'Inspector submit defect' })
  @UseGuards(InspectorGuard)
  async inspectorSubmitDefect(
    @Body() body: InspectorSubmitDefectDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.taskService.inspectorSubmitDefect(body, user)
    return {
      data: true,
    }
  }

  @Post('/new-defect')
  @ApiOperation({ summary: 'Create new defect' })
  // @UseGuards(StaffGuard)
  async createNewDefect(
    @Body() body: CreateNewDefectDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtNewDefect = await this.taskService.createNewDefect(body, user)
    return {
      data: dtNewDefect,
    }
  }

  @Put('/new-defect/:newTaskId')
  @ApiOperation({ summary: 'Update new defect' })
  @UseGuards(StaffGuard)
  async updateNewDefect(
    @Param('newTaskId') newTaskId: Task['id'],
    @Body() body: UpdateNewDefectDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtNewDefect = await this.taskService.updateNewDefect(
      body,
      user,
      newTaskId,
    )
    return {
      data: dtNewDefect,
    }
  }

  @Put('/extra-defect/:newTaskId')
  @ApiOperation({ summary: 'Update new defect' })
  @UseGuards(StaffGuard)
  async updateExtraDefect(
    @Param('newTaskId') newTaskId: Task['id'],
    @Body() body: UpdateExtraDefectDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtExtraDefect = await this.taskService.updateExtraDefect(
      body,
      user,
      newTaskId,
    )
    return {
      data: dtExtraDefect,
    }
  }

  @Put('/staff/add-remark')
  @ApiOperation({ summary: 'Staff add remark' })
  @UseGuards(StaffGuard)
  async staffAddRemark(
    @Body() body: StaffAddRemarkDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtRemark = await this.taskService.staffAddRemark(body, user)
    return {
      data: dtRemark,
    }
  }

  @Put('/inspector/add-remark')
  @ApiOperation({ summary: 'Inspector add remark' })
  @UseGuards(InspectorGuard)
  async inspectorAddRemark(
    @Body() body: InspectorAddRemarkDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtRemark = await this.taskService.inspectorAddRemark(body, user)
    return {
      data: dtRemark,
    }
  }

  @Put('/inspector/add-remark-images')
  @ApiOperation({ summary: 'Inspector add remark images' })
  @UseGuards(InspectorGuard)
  async inspectorAddRemarkImages(
    @Body() body: InspectorAddRemarkImagesDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtRemark = await this.taskService.inspectorAddRemarkImages(body, user)
    return {
      data: dtRemark,
    }
  }

  @Delete('/new-defect/:newTaskId')
  @ApiOperation({ summary: 'Delete new defect' })
  @UseGuards(StaffGuard)
  async deleteNewDefect(
    @Param('newTaskId') newTaskId: Task['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    await this.taskService.deleteNewDefect(user, newTaskId)
    return {
      data: true,
    }
  }
}
