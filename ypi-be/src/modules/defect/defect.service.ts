import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateDefectDTO, GetDefectDTO, UpdateDefectDTO } from './dto/defect.dto'
import { Defect } from './defect.entity'
import { Repository } from 'typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class DefectService {
  constructor(
    @InjectRepository(Defect)
    public defectRepo: Repository<Defect>,
  ) {}

  async createDefect(body: CreateDefectDTO): Promise<Defect> {
    return this.defectRepo.save(body)
  }

  async getDefects({ pageIndex, pageSize }: GetDefectDTO): Promise<any> {
    const [data, total] = await this.defectRepo.findAndCount({
      ...pagingHandler(pageIndex, pageSize),
    })

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  getDefect(defectId: Defect['id']): Promise<Defect> {
    return this.defectRepo.findOne({
      where: { id: defectId },
    })
  }

  async updateDefect(defectId: Defect['id'], body: UpdateDefectDTO) {
    const defect = await this.defectRepo.findOne({
      where: { id: defectId },
    })
    if (!defect) {
      throw new NotFoundException(MSG.DEFECT_NOT_FOUND)
    }

    return this.defectRepo.update(defectId, body)
  }

  async deleteDefect(defectId: Defect['id']) {
    const defect = await this.defectRepo.findOne({
      where: { id: defectId },
    })
    if (!defect) {
      throw new NotFoundException(MSG.DEFECT_NOT_FOUND)
    }

    return this.defectRepo.update(defectId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }
}
