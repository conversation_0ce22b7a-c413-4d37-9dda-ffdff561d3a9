import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { PaginatedQueryParams } from 'src/commons/dto/query'

export class CreateDefectDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string

  @ApiProperty()
  @IsOptional()
  @IsString()
  description: string

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  qty: number

  @ApiProperty()
  @IsOptional()
  @IsString()
  size: string
}

export class GetDefectDTO extends PaginatedQueryParams {}

export class UpdateDefectDTO extends CreateDefectDTO {}
