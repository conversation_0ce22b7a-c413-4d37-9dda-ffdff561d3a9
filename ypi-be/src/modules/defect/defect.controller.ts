import { Controller, Get, Post, Body, Param, Delete, UseGuards, Query, Put } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { DefectService } from '@modules/defect/defect.service'
import { PermitAdminGuard } from '@modules/auth/guards/role.guard'
import { Defect } from '@modules/defect/defect.entity'
import { CreateDefectDTO, GetDefectDTO, UpdateDefectDTO } from './dto/defect.dto'

@Controller('defect')
export class DefectController {
  constructor(private readonly defectService: DefectService) {}

  @Post()
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Create defect' })
  async createDefect(@Body() body: CreateDefectDTO): Promise<any> {
    await this.defectService.createDefect(body)
    return { data: true }
  }

  @Get()
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Get defects' })
  async getDefects(@Query() query: GetDefectDTO): Promise<any> {
    const defects = await this.defectService.getDefects(query)
    return { data: defects }
  }

  @Get(':defectId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Get defect' })
  async getDefect(@Param('defectId') defectId: Defect['id']): Promise<any> {
    const defect = await this.defectService.getDefect(defectId)
    return { data: defect }
  }

  @Put('/:defectId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Update defect' })
  async updateDefect(@Param('defectId') defectId: Defect['id'], @Body() body: UpdateDefectDTO): Promise<any> {
    await this.defectService.updateDefect(defectId, body)
    return { data: true }
  }

  @Delete(':defectId')
  @UseGuards(PermitAdminGuard)
  @ApiOperation({ summary: 'Delete customer' })
  async deleteDefect(@Param('defectId') defectId: Defect['id']): Promise<any> {
    await this.defectService.deleteDefect(defectId)
    return { data: true }
  }
}
