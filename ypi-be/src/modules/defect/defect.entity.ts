import { Column, Entity } from 'typeorm'
import { BaseEntity } from '@core/databases/entities/base.entity'
import { DEFECT_TYPE } from '@core/enums/defect.enum'

@Entity('defect')
export class Defect extends BaseEntity {
  @Column()
  title: string

  @Column({
    type: 'enum',
    enum: DEFECT_TYPE,
    default: null,
  })
  type: DEFECT_TYPE

  @Column({ nullable: true })
  description: string

  @Column({ nullable: true })
  qty: number

  @Column({ nullable: true })
  size: string
}
