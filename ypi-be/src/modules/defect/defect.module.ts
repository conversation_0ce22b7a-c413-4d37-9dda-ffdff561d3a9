import { Module } from '@nestjs/common'
import { DefectService } from './defect.service'
import { DefectController } from './defect.controller'
import { TypeOrmModule } from '@nestjs/typeorm'
import { Defect } from './defect.entity'

@Module({
  imports: [TypeOrmModule.forFeature([Defect])],
  controllers: [DefectController],
  providers: [DefectService],
  exports: [DefectService],
})
export class DefectModule {}
