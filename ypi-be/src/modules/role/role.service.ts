import { Repository } from 'typeorm'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Role } from '@modules/role/role.entity'

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private roleRepo: Repository<Role>,
  ) {
  }

  async getRoleValue(value: string): Promise<Role> {
    return this.roleRepo.findOne({
      where: { value }
    })
  }
}
