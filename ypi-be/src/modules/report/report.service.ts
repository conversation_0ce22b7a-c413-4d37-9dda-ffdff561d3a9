import { ASSIGN_TASK_STATUS } from '@core/enums/assign-task.enum'
import { FILE_TYPE } from '@core/enums/file.enum'
import { EMAIL_TEMPLATE } from '@core/enums/system.enum'
import { TANK_TYPE } from '@core/enums/tank.enum'
import { TASK_STATUS } from '@core/enums/task.enum'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { MailerService } from '@modules/mailer/mailer.service'
import {
  EmailPreInspectionTaskDTO,
  GetCompleteTasksDTO,
  GetNonComplianceTasksDTO,
  GetPreInspectionTasksDTO,
} from '@modules/report/dto/report.dto'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import _ from 'lodash'
import moment from 'moment'
import { Brackets, Repository } from 'typeorm'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { formatFilter } from '../../utils/functions/date-string'
import { getLinkAsset, processLinkAsset } from '../../utils/functions/image'
import {
  PaginationDTO,
  pagingHandler,
  pagingResponse,
} from '../../utils/functions/pagination'

@Injectable()
export class ReportService {
  constructor(
    @InjectRepository(AssignTask)
    private assignTaskRepo: Repository<AssignTask>,
    private readonly mailerService: MailerService,
  ) {}

  async getPreInspectionTasks(
    {
      keyword,
      endTime,
      startTime,
      pageIndex,
      pageSize,
    }: GetPreInspectionTasksDTO,
    { user }: LoggedUserDTO,
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const userId = user.id
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoin('assignTask.tasks', 'taskTasks')
      .where('assignTask.status = :status', {
        status: ASSIGN_TASK_STATUS.PRE_INSPECTION,
      })
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .andWhere('assignTask.isStaffSubmitted = :isStaffSubmitted', {
        isStaffSubmitted: false,
      })
      .skip(skip)
      .take(take)

    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('assignTask.code ILIKE :keyword', {
            keyword: `%${keyword}%`,
          }).orWhere('location.street ILIKE :keyword', {
            keyword: `%${keyword}%`,
          })
        }),
      )
    }

    const pStartTime = formatFilter(startTime, 'start')
    const pEndTime = formatFilter(endTime, 'end')

    queryBuilder.andWhere(
      '(assignTask.startAt BETWEEN :startTime AND :endTime)',
      {
        startTime: pStartTime,
        endTime: pEndTime,
      },
    )

    queryBuilder.orderBy('assignTask.startAt', 'ASC')
    const [data, total] = await queryBuilder.getManyAndCount()

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  async getPreInspectionInfoTask(
    inspectionId: AssignTask['id'],
    { user }: LoggedUserDTO,
  ) {
    const userId = user.id
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId: inspectionId })
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .andWhere(
        new Brackets((qb) => {
          qb.where('taskTasks.status = :taskStatusRectify', {
            taskStatusRectify: TASK_STATUS.RECTIFY,
          }).orWhere('taskTasks.status = :taskStatusNew', {
            taskStatusNew: TASK_STATUS.NEW,
          })
        }),
      )

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    if (dtAssignTask) {
      transformedData = {
        ...dtAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({
          ...ft.staff,
        })),
        tasks: dtAssignTask.tasks
          .map(({ staffs, defect, ...item }) => {
            processLinkAsset(item.images, FILE_TYPE.IMAGE)
            processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
            return {
              ...item,
              title: defect.title,
              type: defect.type,
              defect: {
                id: defect.id,
              },
              staffs: staffs.map((ft) => ({ ...ft.staff })),
            }
          })
          .sort((a, b) => a.title.localeCompare(b.title)),
      }
    }

    return transformedData
  }

  async getNonComplianceTasks(
    {
      keyword,
      endTime,
      startTime,
      pageIndex,
      pageSize,
    }: GetNonComplianceTasksDTO,
    { user }: LoggedUserDTO,
  ): Promise<PaginationDTO<AssignTask>> {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const userId = user.id
    const withStatus = [
      ASSIGN_TASK_STATUS.PREPARE,
      ASSIGN_TASK_STATUS.UNRESOLVED,
    ]
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where(
        new Brackets((qb) => {
          qb.where('assignTask.status IN (:...status)', {
            status: withStatus,
          }).orWhere(
            'assignTask.status = :preInspectionStatus AND assignTask.isStaffSubmitted = TRUE',
            { preInspectionStatus: ASSIGN_TASK_STATUS.PRE_INSPECTION },
          )
        }),
      )
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .andWhere('assignTask.isConfirmed = :isConfirmed', { isConfirmed: false })
      .andWhere('assignTask.status != :completedStatus', {
        completedStatus: ASSIGN_TASK_STATUS.COMPLETED,
      })
      .andWhere('assignTask.isStaffSubmitted = :isStaffSubmitted', {
        isStaffSubmitted: false,
      })
      .addSelect(
        `CASE 
                WHEN assignTask.status = 'prepare' THEN 1 
                WHEN assignTask.status = 'pre-inspection' THEN 1 
                WHEN assignTask.status = 'unresolved' THEN 2 
                ELSE 3 
             END`,
        'orderstatus',
      )
      .orderBy({
        orderstatus: 'ASC',
        'assignTask.startAt': 'DESC',
      })
      .skip(skip)
      .take(take)

    // Search
    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('assignTask.code ILIKE :keyword', {
            keyword: `%${keyword}%`,
          })
            .orWhere('staff.fullName ILIKE :keyword', {
              keyword: `%${keyword}%`,
            })
            .orWhere('location.street ILIKE :keyword', {
              keyword: `%${keyword}%`,
            })
        }),
      )
    }

    const queryStartTime = formatFilter(startTime, 'start')
    const queryEndTime = formatFilter(endTime, 'end')
    queryBuilder.andWhere(
      '(assignTask.startAt BETWEEN :startTime AND :endTime)',
      {
        startTime: queryStartTime,
        endTime: queryEndTime,
      },
    )

    const [data, total] = await queryBuilder.getManyAndCount()

    const transformedData = data.map(({ staffs, ...item }) => {
      return {
        ...item,
        staffs: staffs.map((ft) => {
          return {
            ...ft.staff,
          }
        }),
      }
    })

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getNonComplianceTask(
    nonComplianceId: AssignTask['id'],
  ): Promise<AssignTask> {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.tasks', 'parentTaskTasks')
      .leftJoinAndSelect('parentTaskTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTaskTasks.images', 'parentTaskImages')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId: nonComplianceId })
    const dtAssignTask = await queryBuilder.getOne()

    let transformedData = null
    if (dtAssignTask) {
      const { parent, ...restAssignTask } = dtAssignTask
      const transformedTasks = []
      // Handle list task of parent
      if (parent && parent.tasks.length) {
        for (const task of parent.tasks) {
          processLinkAsset(task.images, FILE_TYPE.IMAGE)
          processLinkAsset(task.inspectorRemarkImages, FILE_TYPE.IMAGE)
          const foundTask = _.find(dtAssignTask.tasks, (ft) => {
            return ft.defect.id === task.defect.id
          })
          if (foundTask) {
            processLinkAsset(foundTask.images, FILE_TYPE.IMAGE)
            processLinkAsset(foundTask.inspectorRemarkImages, FILE_TYPE.IMAGE)

            transformedTasks.push({
              ...task,
              ...foundTask,
              staffs: foundTask
                ? foundTask.staffs.map((ft) => ({ ...ft.staff }))
                : [],
              title: task.defect.title,
              type: task.defect.type,
              before: {
                images: task.images,
                staffRemark: task.staffRemark,
              },
              defect: {
                id: task.defect.id,
              },
              selected: !!foundTask,
            })
          }
        }
      }
      transformedData = {
        ...restAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({ ...ft.staff })),
        tasks: transformedTasks,
      }
    }

    return transformedData
  }

  async getCompleteTasks(
    { keyword, endTime, startTime, pageIndex, pageSize }: GetCompleteTasksDTO,
    { user }: LoggedUserDTO,
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const userId = user.id
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .where('assignTask.status = :status', {
        status: ASSIGN_TASK_STATUS.CONFIRMED,
      })
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .skip(skip)
      .take(take)

    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('assignTask.code ILIKE :keyword', {
            keyword: `%${keyword}%`,
          }).orWhere('location.street ILIKE :keyword', {
            keyword: `%${keyword}%`,
          })
        }),
      )
    }

    const pStartTime = formatFilter(startTime, 'start')
    const pEndTime = formatFilter(endTime, 'end')

    queryBuilder.andWhere(
      '(assignTask.createdAt BETWEEN :startTime AND :endTime)',
      {
        startTime: pStartTime,
        endTime: pEndTime,
      },
    )

    queryBuilder.orderBy('assignTask.createdAt', 'ASC')
    const [data, total] = await queryBuilder.getManyAndCount()

    const transformedData = data.map(({ parent, ...item }) => {
      return {
        id: parent.id,
        code: item.code,
        status: item.status,
        completedDate: parent.completedDate,
        sampleTakenDate: parent.sampleTakenDate,
        reportDate: null,
        tank: item.tank,
      }
    })

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getCompleteTask(completeId: AssignTask['id'], { user }: LoggedUserDTO) {
    const userId = user.id
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .leftJoinAndSelect('assignTask.labReports', 'labReports')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.staffs', 'parentStaffs')
      .leftJoinAndSelect('parentStaffs.staff', 'parentStaff')
      .leftJoinAndSelect('parent.tasks', 'parentTaskTasks')
      .leftJoinAndSelect('parentTaskTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTaskTasks.images', 'parentTaskImages')
      .where('assignTask.id = :assignTaskId', { assignTaskId: completeId })
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    if (dtAssignTask) {
      dtAssignTask.customerSignature = getLinkAsset(
        dtAssignTask.customerSignature,
      )
      if (dtAssignTask.labReports.length) {
        processLinkAsset(dtAssignTask.labReports, FILE_TYPE.LAB_REPORT)
      }
      transformedData = {
        ...dtAssignTask,
        parent: undefined,
        staffs: dtAssignTask.staffs.map((ft) => ({ ...ft.staff })),
        nonCompliance: {
          date: dtAssignTask.startAt,
          staffs: dtAssignTask.staffs.map((ft) => ({
            ...ft.staff,
          })),
          tasks: dtAssignTask.tasks
            .map(({ staffs, defect, ...item }) => {
              processLinkAsset(item.images, FILE_TYPE.IMAGE)
              processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)

              return {
                ...item,
                title: defect.title,
                type: defect.type,
                defect: {
                  id: defect.id,
                },
                staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : [],
              }
            })
            .sort((a, b) => a.title.localeCompare(b.title)),
        },
        preInspection: {
          date: dtAssignTask.parent.startAt,
          staffs: dtAssignTask.parent.staffs.map((ft) => ({
            ...ft.staff,
          })),
          tasks: dtAssignTask.parent.tasks
            .filter(
              (parentTask) =>
                !dtAssignTask.tasks.some(
                  (task) => task.defect.id === parentTask.defect.id,
                ),
            )
            .map(({ staffs, defect, ...item }) => {
              processLinkAsset(item.images, FILE_TYPE.IMAGE)
              processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)

              return {
                ...item,
                title: defect.title,
                type: defect.type,
                defect: {
                  id: defect.id,
                },
                staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : [],
              }
            })
            .sort((a, b) => a.title.localeCompare(b.title)),
        },
        completedDate: dtAssignTask.completedDate,
        sampleTakenDate: dtAssignTask.sampleTakenDate,
        tasks: dtAssignTask.tasks
          .map(({ staffs, defect, ...item }) => {
            processLinkAsset(item.images, FILE_TYPE.IMAGE)
            processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
            let dtBeforeDefect
            if (dtAssignTask.parent) {
              for (const parentTask of dtAssignTask.parent.tasks) {
                if (parentTask.defect.id === defect.id) {
                  dtBeforeDefect = parentTask
                  break
                }
              }
            }
            if (dtBeforeDefect) {
              processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
              processLinkAsset(
                dtBeforeDefect.inspectorRemarkImages,
                FILE_TYPE.IMAGE,
              )
            }
            return {
              ...item,
              title: defect.title,
              type: defect.type,
              defect: {
                id: defect.id,
              },
              staffs: staffs.map((ft) => ({
                ...ft.staff,
              })),
              before: dtBeforeDefect
                ? {
                    images: dtBeforeDefect.images,
                    staffRemark: dtBeforeDefect.staffRemark,
                  }
                : null,
            }
          })
          .sort((a, b) => a.title.localeCompare(b.title)),
      }
    }

    return transformedData
  }

  async emailPreInspectionTask({
    assignTaskId,
    subject,
    toEmails,
  }: EmailPreInspectionTaskDTO) {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId })
      .andWhere('taskTasks.status = :taskStatus', {
        taskStatus: TASK_STATUS.RECTIFY,
      })

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    let dtTasks = dtAssignTask.tasks.map(({ staffs, defect, ...item }) => {
      processLinkAsset(item.images, FILE_TYPE.IMAGE)
      processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
      return {
        ...item,
        title: defect.title,
        type: defect.type,
        defect: {
          id: defect.id,
        },
        staffs: staffs.map((ft) => ({ ...ft.staff })),
      }
    })
    dtTasks.sort((a, b) => a.title.localeCompare(b.title))
    let count = 0
    dtTasks = dtTasks.map((task) => ({
      ...task,
      count: ++count,
    }))
    if (dtAssignTask) {
      transformedData = {
        ...dtAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({
          ...ft.staff,
        })),
        tasks: dtTasks,
      }
    }
    if (transformedData) {
      const address = `${transformedData.tank.location.street} ${transformedData.tank.location.blockNo} ${transformedData.tank.location.building}`
      const postalCode = transformedData.tank.location.postalCode
      const tankType = transformedData.tank.type
      const tankShape = transformedData.tank.shape
      const tankLength = transformedData.tank.length
      const tankWidth = transformedData.tank.width
      const tankHeight = transformedData.tank.height
      const tankEC = transformedData.tank.effectiveCap
      const tankFloor = transformedData.tank.floorLevel
      const material = transformedData.tank.material.name
      const startAt = moment(transformedData.startAt).format('DD/MM/YYYY')
      const staffNames = transformedData.staffs
        .map((staff) => staff.fullName)
        .join(', ')

      let tankColor = ''

      switch (tankType) {
        case TANK_TYPE.HIGH:
          tankColor = '#4F46E5'
          break
        case TANK_TYPE.LOW:
          tankColor = '#F59E0B'
          break
        case TANK_TYPE.INTERMEDIATE:
          tankColor = '#10B981'
          break
      }

      if (toEmails?.length > 0 && subject) {
        await this.mailerService.sendMail(
          {
            to: toEmails,
            subject,
            html: EMAIL_TEMPLATE.REPORT_PRE_INSPECTION,
          },
          {
            address,
            postalCode,
            tankType,
            tankLength,
            tankWidth,
            tankHeight,
            tankEC,
            tankFloor,
            tankShape,
            material,
            startAt,
            staffNames,
            tasks: transformedData.tasks,
            tankColor,
          },
          'mjml',
        )
      }
    }
  }

  async emailNonComplianceTask({
    assignTaskId,
    subject,
    toEmails,
  }: EmailPreInspectionTaskDTO) {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId })
    // .andWhere('taskTasks.status = :taskStatus', {
    //   taskStatus: TASK_STATUS.RECTIFY,
    // })

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    let dtTasks = dtAssignTask.tasks.map(({ staffs, defect, ...item }) => {
      processLinkAsset(item.images, FILE_TYPE.IMAGE)
      processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
      return {
        ...item,
        title: defect.title,
        type: defect.type,
        defect: {
          id: defect.id,
        },
        staffs: staffs.map((ft) => ({ ...ft.staff })),
      }
    })
    dtTasks.sort((a, b) => a.title.localeCompare(b.title))
    let count = 0
    dtTasks = dtTasks.map((task) => ({
      ...task,
      count: ++count,
    }))
    if (dtAssignTask) {
      transformedData = {
        ...dtAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({
          ...ft.staff,
        })),
        tasks: dtTasks,
      }
    }
    if (transformedData) {
      const address = `${transformedData.tank.location.street} ${transformedData.tank.location.blockNo} ${transformedData.tank.location.building}`
      const postalCode = transformedData.tank.location.postalCode
      const tankType = transformedData.tank.type
      const tankShape = transformedData.tank.shape
      const tankLength = transformedData.tank.length
      const tankWidth = transformedData.tank.width
      const tankHeight = transformedData.tank.height
      const tankEC = transformedData.tank.effectiveCap
      const tankFloor = transformedData.tank.floorLevel
      const material = transformedData.tank.material.name
      const startAt = moment(transformedData.startAt).format('DD/MM/YYYY')
      const staffNames = transformedData.staffs
        .map((staff) => staff.fullName)
        .join(', ')

      let tankColor = ''

      switch (tankType) {
        case TANK_TYPE.HIGH:
          tankColor = '#4F46E5'
          break
        case TANK_TYPE.LOW:
          tankColor = '#F59E0B'
          break
        case TANK_TYPE.INTERMEDIATE:
          tankColor = '#10B981'
          break
      }

      if (toEmails?.length > 0 && subject) {
        await this.mailerService.sendMail(
          {
            to: toEmails,
            subject,
            html: EMAIL_TEMPLATE.REPORT_NON_COMPLIANCE,
          },
          {
            address,
            postalCode,
            tankType,
            tankLength,
            tankWidth,
            tankHeight,
            tankEC,
            tankFloor,
            tankShape,
            material,
            startAt,
            staffNames,
            rectifiedTasks: transformedData.tasks.filter(
              (task) => task.status === TASK_STATUS.COMPLETED,
            ),
            unresolveTasks: transformedData.tasks.filter(
              (task) => task.status === TASK_STATUS.UN_COMPLETED,
            ),
            tankColor,
          },
          'mjml',
        )
      }
    }
  }

  async getEmailHTMLPreInspectionTask({
    assignTaskId,
    subject,
    toEmails,
  }: EmailPreInspectionTaskDTO) {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId })
      .andWhere('taskTasks.status = :taskStatus', {
        taskStatus: TASK_STATUS.RECTIFY,
      })

    const dtAssignTask = await queryBuilder.getOne()

    let transformedData = null
    let dtTasks = dtAssignTask.tasks.map(({ staffs, defect, ...item }) => {
      processLinkAsset(item.images, FILE_TYPE.IMAGE)
      processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
      // console.log(item.images)
      return {
        ...item,
        title: defect.title,
        type: defect.type,
        defect: {
          id: defect.id,
        },
        staffs: staffs.map((ft) => ({ ...ft.staff })),
      }
    })
    dtTasks.sort((a, b) => a.title.localeCompare(b.title))
    let count = 0
    dtTasks = dtTasks.map((task) => ({
      ...task,
      count: ++count,
    }))
    if (dtAssignTask) {
      transformedData = {
        ...dtAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({
          ...ft.staff,
        })),
        tasks: dtTasks,
      }
    }

    if (transformedData) {
      const address = `${transformedData.tank.location.street} ${transformedData.tank.location.blockNo} ${transformedData.tank.location.building}`
      const postalCode = transformedData.tank.location.postalCode
      const tankType = transformedData.tank.type
      const tankShape = transformedData.tank.shape
      const tankLength = transformedData.tank.length
      const tankWidth = transformedData.tank.width
      const tankHeight = transformedData.tank.height
      const tankEC = transformedData.tank.effectiveCap
      const tankFloor = transformedData.tank.floorLevel
      const material = transformedData.tank.material.name
      const startAt = moment(transformedData.startAt).format('DD/MM/YYYY')
      const staffNames = transformedData.staffs
        .map((staff) => staff.fullName)
        .join(', ')

      let tankColor = ''

      switch (tankType) {
        case TANK_TYPE.HIGH:
          tankColor = '#4F46E5'
          break
        case TANK_TYPE.LOW:
          tankColor = '#F59E0B'
          break
        case TANK_TYPE.INTERMEDIATE:
          tankColor = '#10B981'
          break
      }

      return await this.mailerService.getEmailHTMLContent(
        {
          to: toEmails,
          subject,
          html: EMAIL_TEMPLATE.REPORT_PRE_INSPECTION,
        },
        {
          address,
          postalCode,
          tankType,
          tankLength,
          tankWidth,
          tankHeight,
          tankEC,
          tankFloor,
          tankShape,
          material,
          startAt,
          staffNames,
          tasks: transformedData.tasks,
          tankColor,
        },
        'mjml',
      )
    }
  }

  async getEmailHTMLNonComplianceTask({
    assignTaskId,
    subject,
    toEmails,
  }: EmailPreInspectionTaskDTO) {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId })

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    let dtTasks = dtAssignTask.tasks.map(({ staffs, defect, ...item }) => {
      processLinkAsset(item.images, FILE_TYPE.IMAGE)
      processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
      return {
        ...item,
        title: defect.title,
        type: defect.type,
        defect: {
          id: defect.id,
        },
        staffs: staffs.map((ft) => ({ ...ft.staff })),
      }
    })
    dtTasks.sort((a, b) => a.title.localeCompare(b.title))
    let count = 0
    dtTasks = dtTasks.map((task) => ({
      ...task,
      count: ++count,
    }))
    if (dtAssignTask) {
      transformedData = {
        ...dtAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({
          ...ft.staff,
        })),
        tasks: dtTasks,
      }
    }
    if (transformedData) {
      const address = `${transformedData.tank.location.street} ${transformedData.tank.location.blockNo} ${transformedData.tank.location.building}`
      const postalCode = transformedData.tank.location.postalCode
      const tankType = transformedData.tank.type
      const tankShape = transformedData.tank.shape
      const tankLength = transformedData.tank.length
      const tankWidth = transformedData.tank.width
      const tankHeight = transformedData.tank.height
      const tankEC = transformedData.tank.effectiveCap
      const tankFloor = transformedData.tank.floorLevel
      const material = transformedData.tank.material.name
      const startAt = moment(transformedData.startAt).format('DD/MM/YYYY')
      const staffNames = transformedData.staffs
        .map((staff) => staff.fullName)
        .join(', ')

      let tankColor = ''

      switch (tankType) {
        case TANK_TYPE.HIGH:
          tankColor = '#4F46E5'
          break
        case TANK_TYPE.LOW:
          tankColor = '#F59E0B'
          break
        case TANK_TYPE.INTERMEDIATE:
          tankColor = '#10B981'
          break
      }

      return await this.mailerService.getEmailHTMLContent(
        {
          to: toEmails,
          subject,
          html: EMAIL_TEMPLATE.REPORT_NON_COMPLIANCE,
        },
        {
          address,
          postalCode,
          tankType,
          tankLength,
          tankWidth,
          tankHeight,
          tankEC,
          tankFloor,
          tankShape,
          material,
          startAt,
          staffNames,
          rectifiedTasks: transformedData.tasks.filter(
            (task) => task.status === TASK_STATUS.COMPLETED,
          ),
          unresolveTasks: transformedData.tasks.filter(
            (task) => task.status === TASK_STATUS.UN_COMPLETED,
          ),
          tankColor,
        },
        'mjml',
      )
    }
  }

  async sendEmailCompleteTask({
    assignTaskId,
    subject,
    toEmails,
  }: EmailPreInspectionTaskDTO) {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .leftJoinAndSelect('assignTask.labReports', 'labReports')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.staffs', 'parentStaffs')
      .leftJoinAndSelect('parentStaffs.staff', 'parentStaff')
      .leftJoinAndSelect('parent.tasks', 'parentTaskTasks')
      .leftJoinAndSelect('parentTaskTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTaskTasks.images', 'parentTaskImages')
      .where('assignTask.id = :assignTaskId', { assignTaskId })

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    if (dtAssignTask) {
      dtAssignTask.customerSignature = getLinkAsset(
        dtAssignTask.customerSignature,
      )
      if (dtAssignTask.labReports.length) {
        processLinkAsset(dtAssignTask.labReports, FILE_TYPE.LAB_REPORT)
      }
      transformedData = {
        ...dtAssignTask,
        parent: undefined,
        staffs: dtAssignTask.staffs.map((ft) => ({ ...ft.staff })),
        nonCompliance: {
          date: dtAssignTask.startAt,
          staffs: dtAssignTask.staffs.map((ft) => ({
            ...ft.staff,
          })),
          tasks: dtAssignTask.tasks
            .map(({ staffs, defect, ...item }) => {
              processLinkAsset(item.images, FILE_TYPE.IMAGE)
              processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)

              return {
                ...item,
                title: defect.title,
                type: defect.type,
                defect: {
                  id: defect.id,
                },
                staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : [],
              }
            })
            .sort((a, b) => a.title.localeCompare(b.title)),
        },
        preInspection: {
          date: dtAssignTask.parent.startAt,
          staffs: dtAssignTask.parent.staffs.map((ft) => ({
            ...ft.staff,
          })),
          tasks: dtAssignTask.parent.tasks
            .filter(
              (parentTask) =>
                !dtAssignTask.tasks.some(
                  (task) => task.defect.id === parentTask.defect.id,
                ),
            )
            .map(({ staffs, defect, ...item }) => {
              processLinkAsset(item.images, FILE_TYPE.IMAGE)
              processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)

              return {
                ...item,
                title: defect.title,
                type: defect.type,
                defect: {
                  id: defect.id,
                },
                staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : [],
              }
            })
            .sort((a, b) => a.title.localeCompare(b.title)),
        },
        completedDate: dtAssignTask.completedDate,
        sampleTakenDate: dtAssignTask.sampleTakenDate,
        tasks: dtAssignTask.tasks
          .map(({ staffs, defect, ...item }) => {
            processLinkAsset(item.images, FILE_TYPE.IMAGE)
            processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
            let dtBeforeDefect
            if (dtAssignTask.parent) {
              for (const parentTask of dtAssignTask.parent.tasks) {
                if (parentTask.defect.id === defect.id) {
                  dtBeforeDefect = parentTask
                  break
                }
              }
            }
            if (dtBeforeDefect) {
              processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
              processLinkAsset(
                dtBeforeDefect.inspectorRemarkImages,
                FILE_TYPE.IMAGE,
              )
            }
            return {
              ...item,
              title: defect.title,
              type: defect.type,
              defect: {
                id: defect.id,
              },
              staffs: staffs.map((ft) => ({
                ...ft.staff,
              })),
              before: dtBeforeDefect
                ? {
                    images: dtBeforeDefect.images,
                    staffRemark: dtBeforeDefect.staffRemark,
                  }
                : null,
            }
          })
          .sort((a, b) => a.title.localeCompare(b.title)),
      }
    }
    if (transformedData) {
      const address = `${transformedData.tank.location.street} ${transformedData.tank.location.blockNo} ${transformedData.tank.location.building}`
      const postalCode = transformedData.tank.location.postalCode
      const tankType = transformedData.tank.type
      const tankShape = transformedData.tank.shape
      const tankLength = transformedData.tank.length
      const tankWidth = transformedData.tank.width
      const tankHeight = transformedData.tank.height
      const tankEC = transformedData.tank.effectiveCap
      const tankFloor = transformedData.tank.floorLevel
      const material = transformedData.tank.material.name
      const startAt = moment(transformedData.startAt).format('DD/MM/YYYY')
      const staffNames = transformedData.staffs
        .map((staff) => staff.fullName)
        .join(', ')

      let tankColor = ''

      switch (tankType) {
        case TANK_TYPE.HIGH:
          tankColor = '#4F46E5'
          break
        case TANK_TYPE.LOW:
          tankColor = '#F59E0B'
          break
        case TANK_TYPE.INTERMEDIATE:
          tankColor = '#10B981'
          break
      }

      return toEmails?.length > 0 && subject
        ? await this.mailerService.sendMail(
            {
              to: toEmails,
              subject,
              html: EMAIL_TEMPLATE.REPORT_complete,
            },
            {
              address,
              postalCode,
              tankType,
              tankLength,
              tankWidth,
              tankHeight,
              tankEC,
              tankFloor,
              tankShape,
              material,
              startAt,
              staffNames,
              customerName: dtAssignTask?.tank?.location?.customer?.name,
              signature: dtAssignTask.customerSignature,
              rectifiedTasks: transformedData.tasks.filter(
                (task) => task.status === TASK_STATUS.COMPLETED,
              ),
              unresolveTasks: transformedData.tasks.filter(
                (task) => task.status === TASK_STATUS.UN_COMPLETED,
              ),
              labReports: dtAssignTask.labReports.map((file) => {
                const fileSplitted = file.link.split('/')
                const filename = fileSplitted[fileSplitted.length - 1]
                return {
                  ...file,
                  completedDate: dtAssignTask.completedDate
                    ? moment(dtAssignTask.completedDate).format('DD/MM/YYYY')
                    : undefined,
                  sampleTakenDate: dtAssignTask.sampleTakenDate
                    ? moment(dtAssignTask.sampleTakenDate).format('DD/MM/YYYY')
                    : undefined,
                  dateOfReport: file.createdAt
                    ? moment(file.createdAt).format('DD/MM/YYYY')
                    : undefined,
                  filename,
                }
              }),
              tankColor,
            },
            'mjml',
          )
        : null
    }
  }

  async getEmailHTMLCompleteTask({
    assignTaskId,
    subject,
    toEmails,
  }: EmailPreInspectionTaskDTO) {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .leftJoinAndSelect('assignTask.labReports', 'labReports')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.tasks', 'taskTasks')
      .leftJoinAndSelect('taskTasks.defect', 'taskDefect')
      .leftJoinAndSelect('taskTasks.images', 'taskImages')
      .leftJoinAndSelect(
        'taskTasks.inspectorRemarkImages',
        'taskInspectorRemarkImages',
      )
      .leftJoinAndSelect('taskTasks.staffs', 'taskStaffs')
      .leftJoinAndSelect('taskStaffs.staff', 'taskStaff')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.staffs', 'parentStaffs')
      .leftJoinAndSelect('parentStaffs.staff', 'parentStaff')
      .leftJoinAndSelect('parent.tasks', 'parentTaskTasks')
      .leftJoinAndSelect('parentTaskTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTaskTasks.images', 'parentTaskImages')
      .where('assignTask.id = :assignTaskId', { assignTaskId })

    const dtAssignTask = await queryBuilder.getOne()
    let transformedData = null
    if (dtAssignTask) {
      dtAssignTask.customerSignature = getLinkAsset(
        dtAssignTask.customerSignature,
      )
      if (dtAssignTask.labReports.length) {
        processLinkAsset(dtAssignTask.labReports, FILE_TYPE.LAB_REPORT)
      }
      transformedData = {
        ...dtAssignTask,
        parent: undefined,
        staffs: dtAssignTask.staffs.map((ft) => ({ ...ft.staff })),
        nonCompliance: {
          date: dtAssignTask.startAt,
          staffs: dtAssignTask.staffs.map((ft) => ({
            ...ft.staff,
          })),
          tasks: dtAssignTask.tasks
            .map(({ staffs, defect, ...item }) => {
              processLinkAsset(item.images, FILE_TYPE.IMAGE)
              processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)

              return {
                ...item,
                title: defect.title,
                type: defect.type,
                defect: {
                  id: defect.id,
                },
                staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : [],
              }
            })
            .sort((a, b) => a.title.localeCompare(b.title)),
        },
        preInspection: {
          date: dtAssignTask.parent.startAt,
          staffs: dtAssignTask.parent.staffs.map((ft) => ({
            ...ft.staff,
          })),
          tasks: dtAssignTask.parent.tasks
            .filter(
              (parentTask) =>
                !dtAssignTask.tasks.some(
                  (task) => task.defect.id === parentTask.defect.id,
                ),
            )
            .map(({ staffs, defect, ...item }) => {
              processLinkAsset(item.images, FILE_TYPE.IMAGE)
              processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)

              return {
                ...item,
                title: defect.title,
                type: defect.type,
                defect: {
                  id: defect.id,
                },
                staffs: staffs ? staffs.map((ft) => ({ ...ft.staff })) : [],
              }
            })
            .sort((a, b) => a.title.localeCompare(b.title)),
        },
        completedDate: dtAssignTask.completedDate,
        sampleTakenDate: dtAssignTask.sampleTakenDate,
        tasks: dtAssignTask.tasks
          .map(({ staffs, defect, ...item }) => {
            processLinkAsset(item.images, FILE_TYPE.IMAGE)
            processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
            let dtBeforeDefect
            if (dtAssignTask.parent) {
              for (const parentTask of dtAssignTask.parent.tasks) {
                if (parentTask.defect.id === defect.id) {
                  dtBeforeDefect = parentTask
                  break
                }
              }
            }
            if (dtBeforeDefect) {
              processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
              processLinkAsset(
                dtBeforeDefect.inspectorRemarkImages,
                FILE_TYPE.IMAGE,
              )
            }
            return {
              ...item,
              title: defect.title,
              type: defect.type,
              defect: {
                id: defect.id,
              },
              staffs: staffs.map((ft) => ({
                ...ft.staff,
              })),
              before: dtBeforeDefect
                ? {
                    images: dtBeforeDefect.images,
                    staffRemark: dtBeforeDefect.staffRemark,
                  }
                : null,
            }
          })
          .sort((a, b) => a.title.localeCompare(b.title)),
      }
    }
    if (transformedData) {
      const address = `${transformedData.tank.location.street} ${transformedData.tank.location.blockNo} ${transformedData.tank.location.building}`
      const postalCode = transformedData.tank.location.postalCode
      const tankType = transformedData.tank.type
      const tankShape = transformedData.tank.shape
      const tankLength = transformedData.tank.length
      const tankWidth = transformedData.tank.width
      const tankHeight = transformedData.tank.height
      const tankEC = transformedData.tank.effectiveCap
      const tankFloor = transformedData.tank.floorLevel
      const material = transformedData.tank.material.name
      const startAt = moment(transformedData.startAt).format('DD/MM/YYYY')
      const staffNames = transformedData.staffs
        .map((staff) => staff.fullName)
        .join(', ')

      let tankColor = ''

      switch (tankType) {
        case TANK_TYPE.HIGH:
          tankColor = '#4F46E5'
          break
        case TANK_TYPE.LOW:
          tankColor = '#F59E0B'
          break
        case TANK_TYPE.INTERMEDIATE:
          tankColor = '#10B981'
          break
      }

      return await this.mailerService.getEmailHTMLContent(
        {
          to: toEmails,
          subject,
          html: EMAIL_TEMPLATE.REPORT_complete,
        },
        {
          address,
          postalCode,
          tankType,
          tankLength,
          tankWidth,
          tankHeight,
          tankEC,
          tankFloor,
          tankShape,
          material,
          startAt,
          staffNames,
          customerName: dtAssignTask?.tank?.location?.customer?.name,
          signature: dtAssignTask.customerSignature,
          rectifiedTasks: transformedData.tasks.filter(
            (task) => task.status === TASK_STATUS.COMPLETED,
          ),
          unresolveTasks: transformedData.tasks.filter(
            (task) => task.status === TASK_STATUS.UN_COMPLETED,
          ),
          labReports: dtAssignTask.labReports.map((file) => {
            const fileSplitted = file.link.split('/')
            const filename = fileSplitted[fileSplitted.length - 1]
            return {
              ...file,
              completedDate: dtAssignTask.completedDate
                ? moment(dtAssignTask.completedDate).format('DD/MM/YYYY')
                : undefined,
              sampleTakenDate: dtAssignTask.sampleTakenDate
                ? moment(dtAssignTask.sampleTakenDate).format('DD/MM/YYYY')
                : undefined,
              dateOfReport: file.createdAt
                ? moment(file.createdAt).format('DD/MM/YYYY')
                : undefined,
              filename,
            }
          }),
          tankColor,
        },
        'mjml',
      )
    }
  }
}
