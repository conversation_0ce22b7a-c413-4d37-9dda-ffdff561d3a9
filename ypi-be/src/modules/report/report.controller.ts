import { LoggedUser } from '@core/decorators/auth.decorator'
import { Public } from '@core/decorators/public.decorator'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { AdminGuard } from '@modules/auth/guards/role.guard'
import {
  EmailCompleteTaskDTO,
  EmailNonComplianceTaskDTO,
  EmailPreInspectionTaskDTO,
  GetCompleteTasksDTO,
  GetNonComplianceTasksDTO,
  GetPreInspectionTasksDTO,
} from '@modules/report/dto/report.dto'
import { ReportService } from '@modules/report/report.service'
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { PaginationDTO } from '../../utils/functions/pagination'

@Controller('report')
export class ReportController {
  constructor(private reportService: ReportService) {}

  @Get('/pre-inspection')
  @ApiOperation({ summary: 'Get tasks of pre-inspection' })
  @UseGuards(AdminGuard)
  async getPreInspectionTasks(
    @Query() body: GetPreInspectionTasksDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.reportService.getPreInspectionTasks(body, user)
    return {
      data: dtTask,
    }
  }

  @Get('/pre-inspection/:inspectionId')
  @ApiOperation({ summary: 'Get info task of pre-inspection' })
  @UseGuards(AdminGuard)
  async getPreInspectionInfoTask(
    @Param('inspectionId') inspectionId: AssignTask['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.reportService.getPreInspectionInfoTask(
      inspectionId,
      user,
    )
    return {
      data: dtTask,
    }
  }

  @Get('/non-compliance')
  @ApiOperation({ summary: 'Get tasks of non-compliance' })
  @UseGuards(AdminGuard)
  async getNonComplianceTasks(
    @Query() query: GetNonComplianceTasksDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<IApiResponse<PaginationDTO<AssignTask>>> {
    const dtTask = await this.reportService.getNonComplianceTasks(query, user)
    return { data: dtTask }
  }

  @Get('/non-compliance/:nonComplianceId')
  @ApiOperation({ summary: 'Get info task of non-compliance' })
  @UseGuards(AdminGuard)
  async getNonComplianceTask(
    @Param('nonComplianceId') nonComplianceId: AssignTask['id'],
  ): Promise<IApiResponse<AssignTask>> {
    const dtTask =
      await this.reportService.getNonComplianceTask(nonComplianceId)
    return { data: dtTask }
  }

  @Get('/rectify/complete')
  @ApiOperation({ summary: 'Get tasks of rectify complete' })
  @UseGuards(AdminGuard)
  async getCompleteTasks(
    @Query() body: GetCompleteTasksDTO,
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.reportService.getCompleteTasks(body, user)
    return {
      data: dtTask,
    }
  }

  @Get('/rectify/complete/:completeId')
  @ApiOperation({ summary: 'Get info task of rectify complete' })
  @UseGuards(AdminGuard)
  async getCompleteTask(
    @Param('completeId') completeId: AssignTask['id'],
    @LoggedUser() user: LoggedUserDTO,
  ): Promise<any> {
    const dtTask = await this.reportService.getCompleteTask(completeId, user)
    return {
      data: dtTask,
    }
  }

  @Post('/pre-inspection/send-email')
  @ApiOperation({ summary: 'Send email info task of pre-inspection' })
  @UseGuards(AdminGuard)
  async emailPreInspectionTask(
    @Body() body: EmailPreInspectionTaskDTO,
  ): Promise<any> {
    return {
      data: await this.reportService.emailPreInspectionTask(body),
    }
  }

  @Post('/pre-inspection/email-content')
  @ApiOperation({ summary: 'Send email info task of pre-inspection' })
  @Public()
  async getEmailPreInspectionTaskContent(
    @Body() body: EmailPreInspectionTaskDTO,
  ): Promise<any> {
    return {
      data: await this.reportService.getEmailHTMLPreInspectionTask(body),
    }
  }

  @Post('/non-compliance/send-email')
  @ApiOperation({ summary: 'Send email info task of non-compliance' })
  @UseGuards(AdminGuard)
  async emailNonComplianceTask(
    @Body() body: EmailNonComplianceTaskDTO,
  ): Promise<any> {
    return {
      data: await this.reportService.emailNonComplianceTask(body),
    }
  }

  @Post('/non-compliance/email-content')
  @ApiOperation({ summary: 'Send email info task of non-compliance' })
  @UseGuards(AdminGuard)
  async getEmailNonComplianceTask(
    @Body() body: EmailNonComplianceTaskDTO,
  ): Promise<any> {
    return {
      data: await this.reportService.getEmailHTMLNonComplianceTask(body),
    }
  }

  @Post('/rectify/complete/send-email')
  @ApiOperation({ summary: 'Send email info task of complete' })
  @UseGuards(AdminGuard)
  async emailCompleteTask(@Body() body: EmailCompleteTaskDTO): Promise<any> {
    return {
      data: await this.reportService.sendEmailCompleteTask(body),
    }
  }

  @Post('/rectify/complete/email-content')
  @ApiOperation({ summary: 'Send email info task of complete' })
  @UseGuards(AdminGuard)
  async getEmailCompleteTask(@Body() body: EmailCompleteTaskDTO): Promise<any> {
    return {
      data: await this.reportService.getEmailHTMLCompleteTask(body),
    }
  }
}
