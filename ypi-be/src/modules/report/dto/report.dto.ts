import { ApiProperty } from '@nestjs/swagger'
import {
  ArrayMinSize,
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class GetPreInspectionTasksDTO extends PaginatedQueryParams {
  @ApiProperty()
  @IsNotEmpty()
  startTime: string

  @ApiProperty()
  @IsNotEmpty()
  endTime: string
}

export class GetNonComplianceTasksDTO extends PaginatedQueryParams {
  @ApiProperty()
  @IsNotEmpty()
  startTime: string

  @ApiProperty()
  @IsNotEmpty()
  endTime: string
}

export class GetCompleteTasksDTO extends PaginatedQueryParams {
  @ApiProperty()
  @IsNotEmpty()
  startTime: string

  @ApiProperty()
  @IsNotEmpty()
  endTime: string
}

export class EmailPreInspectionTaskDTO {
  @ApiProperty()
  @IsUUID('4')
  assignTaskId: string

  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEmail({ allow_display_name: true }, { each: true })
  toEmails: string[]

  @IsOptional()
  @IsString()
  subject: string
}

export class EmailNonComplianceTaskDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string

  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEmail({ allow_display_name: true }, { each: true })
  toEmails: string[]

  @IsString()
  @IsOptional()
  subject: string
}

export class EmailCompleteTaskDTO {
  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  assignTaskId: string

  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsEmail({ allow_display_name: true }, { each: true })
  toEmails: string[]

  @IsString()
  @IsOptional()
  subject: string
}
function ApiPropertyOptional(): (
  target: EmailPreInspectionTaskDTO,
  propertyKey: 'toEmails',
) => void {
  throw new Error('Function not implemented.')
}
