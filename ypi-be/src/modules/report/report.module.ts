import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { AwsModule } from '@modules/aws/aws.module'
import { MailerModule } from '@modules/mailer/mailer.module'
import { ReportController } from '@modules/report/report.controller'
import { ReportService } from '@modules/report/report.service'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

@Module({
  imports: [TypeOrmModule.forFeature([AssignTask]), AwsModule, MailerModule],
  controllers: [ReportController],
  providers: [ReportService],
})
export class ReportModule {}
