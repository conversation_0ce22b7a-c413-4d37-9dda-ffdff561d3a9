import { UploadFilesDTO, UploadImagesDTO } from '@modules/upload/dto/upload.dto'
import { UploadService } from '@modules/upload/upload.service'
import {
  Body,
  Controller,
  Post,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common'
import { FileFieldsInterceptor } from '@nestjs/platform-express'
import { ApiOperation } from '@nestjs/swagger'

@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('/files')
  @ApiOperation({ summary: 'Upload files' })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'documents', maxCount: 10 },
      { name: 'certifications', maxCount: 10 },
      { name: 'labReports', maxCount: 10 },
    ]),
  )
  async uploadFiles(
    @UploadedFiles()
    files: {
      documents?: Express.Multer.File[]
      certifications?: Express.Multer.File[]
      labReports?: Express.Multer.File[]
    },
    @Body() body: UploadFilesDTO,
  ): Promise<any> {
    const dtUpload = await this.uploadService
      .uploadFiles(files, body)
      .catch(console.error)
    return {
      data: dtUpload,
    }
  }

  @Post('/images')
  @ApiOperation({ summary: 'Upload files' })
  @UseInterceptors(FileFieldsInterceptor([{ name: 'images', maxCount: 10 }]))
  async uploadImages(
    @UploadedFiles() files: { images?: Express.Multer.File[] },
    @Body() body: UploadImagesDTO,
  ): Promise<any> {
    const dtUpload = await this.uploadService.uploadImages(files, body)
    return {
      data: dtUpload,
    }
  }
}
