import {
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator'
import { ApiPropertyOptional } from '@nestjs/swagger'

export class UploadFilesDTO {
  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  staffId?: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  tankId?: string

  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  phaseId?: string
}

export class UploadImagesDTO {
  @ApiPropertyOptional()
  @IsUUID('4')
  @IsOptional()
  @IsString()
  taskId?: string
}