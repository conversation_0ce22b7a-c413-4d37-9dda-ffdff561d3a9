import { FILE_TYPE } from '@core/enums/file.enum'
import { AssignTaskService } from '@modules/assign-task/assign-task.service'
import { S3Service } from '@modules/aws/s3.service'
import { File } from '@modules/file/file.entity'
import { StaffService } from '@modules/staff/staff.service'
import { TankService } from '@modules/tank/tank.service'
import { Task } from '@modules/task/task.entity'
import { UploadFilesDTO, UploadImagesDTO } from '@modules/upload/dto/upload.dto'
import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { getLinkAsset } from '../../utils/functions/image'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class UploadService {
  constructor(
    @InjectRepository(File)
    public fileRepo: Repository<File>,
    @InjectRepository(Task)
    private taskRepo: Repository<Task>,
    private readonly s3Service: S3Service,
    private readonly staffService: StaffService,
    private readonly tankService: TankService,
    private readonly assignTaskService: AssignTaskService,
  ) {}

  async uploadFiles(
    files: {
      documents?: Express.Multer.File[]
      certifications?: Express.Multer.File[]
      labReports?: Express.Multer.File[]
    },
    payload: UploadFilesDTO,
  ) {
    const { staffId, tankId, phaseId } = payload
    if (staffId) {
      const findStaff = await this.staffService.staffRepo.findOne({
        where: { id: staffId },
      })
      if (!findStaff) {
        throw new NotFoundException(MSG.STAFF_NOT_FOUND)
      }
    }

    if (tankId) {
      const findTank = await this.tankService.tankRepo.findOne({
        where: { id: tankId },
      })
      if (!findTank) {
        throw new NotFoundException(MSG.TANK_NOT_FOUND)
      }
    }

    if (phaseId) {
      const findPhase = await this.assignTaskService.assignTaskRepo.findOne({
        where: { id: phaseId },
      })
      if (!findPhase) {
        throw new NotFoundException(MSG.TASK_NOT_FOUND)
      }
    }

    const arrayDocument = []
    if (files.documents && files.documents.length) {
      const prefix = 'file/documents'
      for (const document of files.documents) {
        const dtUpload = await this.s3Service.processFileUpload(
          prefix,
          document,
        )
        if (dtUpload) {
          const dtFile = await this.fileRepo.save({
            staff: {
              id: staffId ? staffId : undefined,
            },
            name: dtUpload.name,
            mineType: dtUpload.mineType,
            type: FILE_TYPE.DOCUMENT,
            link: dtUpload.link,
          })
          arrayDocument.push({
            id: dtFile.id,
            name: dtFile.name,
            mineType: dtFile.mineType,
            type: dtFile.type,
            link: getLinkAsset(dtFile.link),
          })
        }
      }
    }
    const arrayCertification = []
    if (files.certifications && files.certifications.length) {
      const prefix = 'file/certifications'
      for (const cert of files.certifications) {
        const dtUpload = await this.s3Service.processFileUpload(prefix, cert)
        if (dtUpload) {
          const dtFile = await this.fileRepo.save({
            staff: {
              id: staffId ? staffId : undefined,
            },
            name: dtUpload.name,
            mineType: dtUpload.mineType,
            type: FILE_TYPE.CERTIFICATION,
            link: dtUpload.link,
          })
          arrayCertification.push({
            id: dtFile.id,
            name: dtFile.name,
            mineType: dtFile.mineType,
            type: dtFile.type,
            link: getLinkAsset(dtFile.link),
          })
        }
      }
    }
    const arrayLabReport = []
    if (files.labReports && files.labReports.length) {
      const prefix = 'file/lab-reports'
      for (const report of files.labReports) {
        const dtUpload = await this.s3Service.processFileUpload(prefix, report)
        if (dtUpload) {
          const dtFile = await this.fileRepo.save({
            assignTask: {
              id: phaseId ? phaseId : undefined,
            },
            tank: {
              id: tankId ? tankId : undefined,
            },
            name: dtUpload.name,
            mineType: dtUpload.mineType,
            type: FILE_TYPE.LAB_REPORT,
            link: dtUpload.link,
          })
          arrayLabReport.push({
            id: dtFile.id,
            name: dtFile.name,
            mineType: dtFile.mineType,
            type: dtFile.type,
            link: getLinkAsset(dtFile.link),
          })
        }
      }
    }

    return arrayDocument.concat(arrayCertification).concat(arrayLabReport)
  }

  async uploadImages(
    files: { images?: Express.Multer.File[] },
    payload: UploadImagesDTO,
  ) {
    const { taskId } = payload
    if (taskId) {
      const findTask = await this.taskRepo.findOne({
        where: { id: taskId },
      })
      if (!findTask) {
        throw new NotFoundException(MSG.STAFF_NOT_FOUND)
      }
    }

    const arrayImage = []
    if (files.images && files.images.length) {
      const prefix = 'images'
      for (const image of files.images) {
        const dtUpload = await this.s3Service.processImageUpload(prefix, image)
        if (dtUpload) {
          const dtFile = await this.fileRepo.save({
            task: {
              id: taskId ? taskId : undefined,
            },
            name: dtUpload.name,
            mineType: dtUpload.mineType,
            type: FILE_TYPE.IMAGE,
            link: dtUpload.link,
          })
          arrayImage.push({
            id: dtFile.id,
            name: dtFile.name,
            mineType: dtFile.mineType,
            type: dtFile.type,
            link: getLinkAsset(dtFile.link),
          })
        }
      }
    }

    return arrayImage
  }
}
