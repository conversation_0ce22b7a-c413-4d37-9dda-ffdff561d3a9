import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { File } from '@modules/file/file.entity'
import { UploadController } from '@modules/upload/upload.controller'
import { UploadService } from '@modules/upload/upload.service'
import { AwsModule } from '@modules/aws/aws.module'
import { StaffModule } from '@modules/staff/staff.module'
import { Task } from '@modules/task/task.entity'
import { TankModule } from '@modules/tank/tank.module'
import { AssignTaskModule } from '@modules/assign-task/assign-task.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([File, Task]),
    AwsModule,
    StaffModule,
    TankModule,
    AssignTaskModule,
  ],
  controllers: [UploadController],
  providers: [UploadService],
  exports: [UploadService],
})
export class UploadModule {
}
