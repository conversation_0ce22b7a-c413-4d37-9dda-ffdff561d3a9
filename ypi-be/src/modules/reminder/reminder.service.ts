import moment from 'moment'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { GetReminderPastTasksDTO, GetReminderUpcomingTasksDTO } from '@modules/reminder/dto/reminder.dto'
import { ASSIGN_TASK_STATUS } from '@core/enums/assign-task.enum'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'

@Injectable()
export class ReminderService {
  constructor(
    @InjectRepository(AssignTask)
    public assignTaskRepo: Repository<AssignTask>,
  ) {
  }

  async getReminderUpcomingTasks(
    { pageIndex, pageSize }: GetReminderUpcomingTasksDTO,
    { user }: LoggedUserDTO,
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const userId = user.id
    const withStatus = [ASSIGN_TASK_STATUS.PREPARE, ASSIGN_TASK_STATUS.PRE_INSPECTION]
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.status IN (:...status)', { status: withStatus })
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .andWhere('children IS NULL')
      .skip(skip)
      .take(take)

    const pStartTime = moment().startOf('day').toDate()
    queryBuilder.andWhere('(assignTask.startAt >= :startTime)', {
      startTime: pStartTime
    })
    queryBuilder.orderBy('assignTask.startAt', 'DESC')
    const [data, total] = await queryBuilder.getManyAndCount()

    const transformedData = data.map(({ staffs, ...item }) => {
      return {
        ...item,
        staffs: staffs.map(ft => ({
          ...ft.staff,
        })),
      }
    })

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getReminderPastTasks(
    { pageIndex, pageSize }: GetReminderPastTasksDTO,
    { user }: LoggedUserDTO,
  ) {
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const userId = user.id
    const withStatus = [ASSIGN_TASK_STATUS.PREPARE, ASSIGN_TASK_STATUS.PRE_INSPECTION]
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.children', 'children')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.status IN (:...status)', { status: withStatus })
      .andWhere('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .andWhere('children IS NULL')
      .skip(skip)
      .take(take)

    const pEndTime = moment().endOf('day').toDate()
    queryBuilder.andWhere('(assignTask.startAt <= :endTime)', {
      endTime: pEndTime
    })
    queryBuilder.orderBy('assignTask.startAt', 'DESC')
    const [data, total] = await queryBuilder.getManyAndCount()

    const transformedData = data.map(({ staffs, ...item }) => {
      return {
        ...item,
        staffs: staffs.map(ft => ({
          ...ft.staff,
        })),
      }
    })

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }
}
