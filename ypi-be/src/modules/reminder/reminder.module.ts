import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { ReminderController } from '@modules/reminder/reminder.controller'
import { ReminderService } from '@modules/reminder/reminder.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([AssignTask]),
  ],
  controllers: [ReminderController],
  providers: [ReminderService],
  exports: [ReminderService],
})
export class ReminderModule {
}
