import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import { ReminderService } from '@modules/reminder/reminder.service'
import { ApiOperation } from '@nestjs/swagger'
import { AdminGuard } from '@modules/auth/guards/role.guard'
import { LoggedUser } from '@core/decorators/auth.decorator'
import { GetReminderPastTasksDTO, GetReminderUpcomingTasksDTO } from '@modules/reminder/dto/reminder.dto'
import { LoggedUserDTO } from '../../commons/dto/logged'

@Controller('reminder')
export class ReminderController {
  constructor(private readonly reminderService: ReminderService) {
  }

  @Get('/upcoming')
  @ApiOperation({ summary: 'Get tasks of reminder upcoming' })
  @UseGuards(AdminGuard)
  async getReminderUpcomingTasks(
    @Query() body: GetReminderUpcomingTasksDTO,
    @LoggedUser() user: LoggedUserDTO
  ): Promise<any> {
    const dtTask = await this.reminderService.getReminderUpcomingTasks(body, user)
    return {
      data: dtTask
    }
  }

  @Get('/past')
  @ApiOperation({ summary: 'Get tasks of reminder past' })
  @UseGuards(AdminGuard)
  async getReminderPastTasks(
    @Query() body: GetReminderPastTasksDTO,
    @LoggedUser() user: LoggedUserDTO
  ): Promise<any> {
    const dtTask = await this.reminderService.getReminderPastTasks(body, user)
    return {
      data: dtTask
    }
  }
}
