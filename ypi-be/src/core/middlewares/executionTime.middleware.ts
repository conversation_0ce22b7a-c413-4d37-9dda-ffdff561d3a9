import { Injectable, NestMiddleware } from '@nestjs/common'
import { Request, Response, NextFunction } from 'express'

@Injectable()
export class ExecutionTimeMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now()
    res.on('finish', () => {
      const end = Date.now()
      const executionTime = end - start
      console.log(`\n@@@ REQUEST: ${req.path} \nQuery: ${JSON.stringify(req.query)} \nTook: ${executionTime}ms`)
    })
    next()
  }
}
