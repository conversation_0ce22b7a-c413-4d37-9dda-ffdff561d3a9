import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { Logger } from 'winston'
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger,
  ) {}

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp()
    const response = ctx.getResponse()
    const request = ctx.getRequest()
    let status = HttpStatus.INTERNAL_SERVER_ERROR
    let message: string | object = 'Internal server error'
    let stack = ''
    if (exception instanceof HttpException) {
      status = exception.getStatus()
      message = exception.getResponse()
    } else if (exception instanceof Error) {
      status = HttpStatus.BAD_REQUEST
      message = exception.message
      stack = exception.stack
    } else {
      status = exception?.statusCode
      message = exception
    }
    const responseBody = {
      status: status,
      path: request.url,
      message,
      stack,
    }
    this.logger.error(responseBody)
    if (typeof responseBody.message === 'string') {
      responseBody.message = {
        message: responseBody.message,
      }
    }
    response.status(status).json({
      statusCode: responseBody.status,
      ...responseBody.message,
    })
  }
}
