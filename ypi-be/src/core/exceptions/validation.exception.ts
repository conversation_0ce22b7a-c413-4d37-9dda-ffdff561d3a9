import { ValidationError } from 'class-validator'

export function validationExceptionFactory(errors: ValidationError[]): {
  statusCode: number
  message: {
    field: string
    constraints: string
  }[]
} {

  const mapValidationErrors = (errors: ValidationError[]): any => {
    let result: any[] = [];

    errors.forEach((error: ValidationError) => {
      if (error.constraints) {
        result.push({
          field: error.property,
          constraints: Object.values(error.constraints)[0],
        });
      }

      if (error.children && error.children.length > 0) {
        result = result.concat(mapValidationErrors(error.children));
      }
    });

    return result;
  };

  const customValidationErrors = mapValidationErrors(errors)

  return {
    statusCode: 400,
    message: customValidationErrors,
  }
}
