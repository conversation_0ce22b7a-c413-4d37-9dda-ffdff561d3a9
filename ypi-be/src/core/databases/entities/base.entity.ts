import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Exclude } from 'class-transformer'
import {
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
  VersionColumn,
  Column,
} from 'typeorm'

export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  @ApiProperty({
    format: 'uuid',
  })
  id?: string

  @CreateDateColumn({ type: 'timestamptz' })
  @ApiProperty({
    required: false,
    readOnly: true,
    type: 'string',
    format: 'date-time',
  })
  @Index()
  createdAt?: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  @ApiProperty({
    required: false,
    readOnly: true,
    type: 'string',
    format: 'date-time',
  })
  @Index()
  updatedAt?: Date

  @DeleteDateColumn({ type: 'timestamptz' })
  @Exclude()
  deletedAt?: Date

  @Column({
    default: false,
    nullable: true,
  })
  @ApiPropertyOptional()
  isDeleted: boolean

  @VersionColumn({
    default: 0,
  })
  @Exclude()
  __v?: number
}
