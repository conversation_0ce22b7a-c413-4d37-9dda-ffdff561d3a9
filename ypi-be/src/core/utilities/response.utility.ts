export class ApiResponse<T> implements IApiResponse<T> {
  data: T
  status?: number
  message?: string

  constructor(data: T, status?: number, message?: string) {
    this.data = data
    this.status = status
    this.message = message
  }

  static ok<T>({ data, status = 200, message = 'Success' }: IApiResponse<T>): ApiResponse<T> {
    return new ApiResponse(data, status, message)
  }

  static error<T>({ data = null, status = 400, message }: IApiResponse<T>): ApiResponse<T> {
    return new ApiResponse<T>(data, status, message)
  }

  static from<T extends IApiResponse<T>>(response: T): IApiResponse<T> {
    if (this.isApiResponse(response)) {
      return response as ApiResponse<T>
    } else {
      return ApiResponse.ok(response)
    }
  }

  // Helper method to check if a response is of type IApiResponse<T>
  private static isApiResponse<T extends object>(response: any): response is IApiResponse<T> {
    return response && 'data' in response && 'status' in response && 'message' in response
  }
}
