import { configService } from '@core/configs/env.config'
import { validationExceptionFactory } from '@core/exceptions/validation.exception'
import { RepesponseInterceptor } from '@core/interceptors/response.interceptor'
import { ValidationError, ValidationPipe } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { NestExpressApplication } from '@nestjs/platform-express'
import { DocumentBuilder } from '@nestjs/swagger'
import * as pkg from '../package.json'
import { AppModule } from './app.module'
async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    cors: true,
  })

  app.useStaticAssets('public')

  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    allowedHeaders: 'Content-Type, Authorization',
  })
  app.setGlobalPrefix('api')
  // ================================================
  app.useGlobalPipes(
    new ValidationPipe({
      // whitelist: true,
      // forbidNonWhitelisted: true,
      // transform: true,
      disableErrorMessages: false,
      exceptionFactory: (errors: ValidationError[]) =>
        validationExceptionFactory(errors),
    }),
  )
  // ================================================
  app.useGlobalInterceptors(new RepesponseInterceptor())
  // ================================================
  if (configService.get('MODE') !== 'production') {
    const baseDocument = new DocumentBuilder()
      .setTitle(pkg.name)
      .setDescription(pkg.description)
      .setVersion(pkg.version)
      .addBearerAuth()
    // const apiDocument = SwaggerModule.createDocument(app, baseDocument.build())
    // SwaggerModule.setup('documents', app, apiDocument, {
    //   swaggerOptions: {
    //     docExpansion: 'none',
    //   },
    // })
  }

  await app.listen(process.env.PORT)
}
bootstrap()
