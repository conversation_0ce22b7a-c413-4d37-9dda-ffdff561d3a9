import { dataSourceOptions } from '@core/databases/typeorm.config'
import { GlobalExceptionFilter } from '@core/exceptions/global.exception'
import { LoggerMiddleware } from '@core/middlewares/logger.middleware'
import { AgentModule } from '@modules/agent/agent.module'
import { AssignTaskModule } from '@modules/assign-task/assign-task.module'
import { AuthModule } from '@modules/auth/auth.module'
import { JwtAuthGuard } from '@modules/auth/guards/jwt.guard'
import { BuildingModule } from '@modules/building/building.module'
import { CustomerModule } from '@modules/customer/customer.module'
import { DefectModule } from '@modules/defect/defect.module'
import { FileModule } from '@modules/file/file.module'
import { ImportModule } from '@modules/import/import.module'
import { InChargeModule } from '@modules/in-charge/in-charge.module'
import { LocationModule } from '@modules/location/location.module'
import { MailerModule } from '@modules/mailer/mailer.module'
import { RedisModule } from '@modules/redis/redis.module'
import { ReminderModule } from '@modules/reminder/reminder.module'
import { ReportModule } from '@modules/report/report.module'
import { RoleModule } from '@modules/role/role.module'
import { StaffModule } from '@modules/staff/staff.module'
import { TankMaterialModule } from '@modules/tank/material/tank-material.module'
import { TankModule } from '@modules/tank/tank.module'
import { TaskModule } from '@modules/task/task.module'
import { TokenModule } from '@modules/token/token.module'
import { UploadModule } from '@modules/upload/upload.module'
import { UserModule } from '@modules/user/user.module'
import {
  DynamicModule,
  MiddlewareConsumer,
  Module,
  NestModule,
  OnModuleInit,
} from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { APP_FILTER, APP_GUARD } from '@nestjs/core'
import { ScheduleModule } from '@nestjs/schedule'
import { TypeOrmModule } from '@nestjs/typeorm'
import { WinstonModule } from 'nest-winston'
import { DataSourceOptions } from 'typeorm'
import winston from 'winston'
import { Console } from 'winston/lib/winston/transports'
import { AppController } from './app.controller'
import { AppService } from './app.service'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRoot({
      ...dataSourceOptions,
    }),
    ScheduleModule.forRoot(),
    WinstonModule.forRoot({
      transports: [
        new Console({
          // filename: './logs/error-%DATE%.log',
          // level: 'error',
          // datePattern: 'YYYY-MM-DD-HH',
          // zippedArchive: true,
          // maxSize: '50mb',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.printf(
              (info) =>
                `[${info.level}][${info.timestamp}]${info.path}: ${info.message}\n${info.stack}`,
            ),
          ),
        }),
        // new DailyRotateFile({
        //   filename: './logs/info-%DATE%.log',
        //   level: 'info',
        //   datePattern: 'YYYY-MM-DD-HH',
        //   zippedArchive: true,
        //   maxSize: '50mb',
        //   format: winston.format.combine(
        //     winston.format.timestamp(),
        //     winston.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`),
        //   ),
        // }),
      ],
    }),
    AuthModule,
    UserModule,
    RoleModule,
    TokenModule,
    BuildingModule,
    InChargeModule,
    AgentModule,
    CustomerModule,
    LocationModule,
    TankModule,
    TankMaterialModule,
    StaffModule,
    DefectModule,
    AssignTaskModule,
    UploadModule,
    FileModule,
    TaskModule,
    ReportModule,
    ReminderModule,
    RedisModule,
    ImportModule,
    MailerModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
  ],
})
export class AppModule implements OnModuleInit, NestModule {
  // constructor(private readonly someService: SomeService) {}
  onModuleInit() {
    // Initialization logic here
  }

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*')
  }

  static forRoot(connOptions: DataSourceOptions): DynamicModule {
    return {
      module: AppModule,
      controllers: [AppController],
      imports: [TypeOrmModule.forRoot(connOptions)],
      providers: [AppService],
    }
  }
}
