import { Type } from 'class-transformer';
import { IsInt, <PERSON>Optional, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class KeywordQueryParams {
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  keyword?: string;
}

export class PaginatedQueryParams extends KeywordQueryParams {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  pageIndex: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Max(1000)
  @Min(1)
  pageSize: number;
}
