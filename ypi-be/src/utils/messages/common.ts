export const MSG = Object.freeze({
  UNAUTHORIZED: {
    key: 'unauthorized',
    message: 'Unauthorized',
  },
  USER_NOT_FOUND: {
    key: 'user_not_found',
    message: 'User not found',
  },
  COULD_NOT_UPLOAD: {
    key: 'could_not_upload',
    message: 'Could not upload',
  },
  USER_PW_NOT_MATCH: {
    key: 'incorrect_password',
    message: 'Incorrect password',
  },
  ROLE_NOT_ACCEPTED: {
    key: 'role_not_accepted',
    message: 'The role is not accepted',
  },
  ROLE_NOT_FOUND: {
    key: 'role_not_found',
    message: 'Role not found',
  },
  USER_ALREADY_EXISTS: {
    key: 'user_already_exists',
    message: 'This user already exists',
  },
  USER_EMAIL_NOT_EXIST: {
    key: 'user_email_not_exist',
    message: 'There is no account associated with this email',
  },
  TOKEN_NOT_ACCEPTED: {
    key: 'token_not_accepted',
    message: 'This token is not accepted',
  },
  BUILDING_NOT_FOUND: {
    key: 'building_not_found',
    message: 'Building not found',
  },
  CUSTOMER_NOT_FOUND: {
    key: 'customer_not_found',
    message: 'Customer not found',
  },
  CUSTOMER_ALREADY_EXISTS: {
    key: 'customer_already_exists',
    message: 'This customer already exists',
  },
  CUSTOMER_EMAIL_ALREADY_EXISTS: {
    key: 'customer_email_already_exists',
    message: "This customer's email already exists",
  },
  CUSTOMER_PUB_NUMBER_ALREADY_EXISTS: {
    key: 'customer_pub_number_already_exists',
    message: "This customer's PUB number already exists",
  },
  CUSTOMER_PHONE_ALREADY_EXISTS: {
    key: 'customer_phone_already_exists',
    message: "This customer's phone already exists",
  },
  IN_CHARGE_NOT_FOUND: {
    key: 'in_charge_not_found',
    message: 'In-charge not found',
  },
  LOCATION_NOT_FOUND: {
    key: 'location_not_found',
    message: 'Location not found',
  },
  AGENT_NOT_FOUND: {
    key: 'agent_not_found',
    message: 'Agent not found',
  },
  MATERIAL_NOT_FOUND: {
    key: 'material_not_found',
    message: 'Material not found',
  },
  STAFF_ALREADY_EXISTS: {
    key: 'staff_already_exists',
    message: 'This staff already exists',
  },
  STAFF_NOT_FOUND: {
    key: 'staff_not_found',
    message: 'Staff not found',
  },
  STAFF_EMAIL_ALREADY_EXISTS: {
    key: 'staff_email_already_exists',
    message: 'This email already exists in the manager',
  },
  STAFF_PHONE_ALREADY_EXISTS: {
    key: 'staff_phone_already_exists',
    message: "This staff's phone already exists",
  },
  STAFF_PW_NOT_MATCH: {
    key: 'incorrect_password',
    message: 'Incorrect password',
  },
  STAFF_CODE_ALREADY_EXISTS: {
    key: 'staff_code_already_exists',
    message: "This staff's code already exists",
  },
  DEFECT_NOT_FOUND: {
    key: 'defect_not_found',
    message: 'Defect not found',
  },
  TANK_ALREADY_EXISTS: {
    key: 'tank_already_exists',
    message: 'This tank already exists',
  },
  TANK_NOT_FOUND: {
    key: 'tank_not_found',
    message: 'Tank not found',
  },
  TANK_CANNOT_BE_DELETED: {
    key: 'tank_cannot_be_deleted',
    message: 'This tank cannot be deleted',
  },
  TASK_ALREADY_EXISTS: {
    key: 'task_already_exists',
    message: 'This task already exists',
  },
  TASK_NOT_FOUND: {
    key: 'task_not_found',
    message: 'Task not found',
  },
  TASK_HAS_NOT_BEEN_COMPLETED: {
    key: 'task_has_not_been_completed',
    message: 'This task has not yet been completed',
  },
  TASK_CANNOT_BE_DELETED: {
    key: 'task_cannot_be_deleted',
    message: 'This task cannot be deleted',
  },
  TASK_HAS_BEEN_COMPLETED: {
    key: 'task_has_been_completed',
    message: 'This task has been completed',
  },
  TASK_HAS_BEEN_CONFIRMED: {
    key: 'task_has_been_confirmed',
    message: 'This task has been confirmed',
  },
  TASK_HAS_BEEN_SUBMITTED: {
    key: 'task_has_been_submitted',
    message: 'This task has been submitted',
  },
  ASSIGN_STAFF_NOT_FOUND: {
    key: 'assign_staff_not_found',
    message: 'Assign staff not found',
  },
  ASSIGN_STAFF_IS_SUBMITTED: {
    key: 'assign_staff_is_submitted',
    message: 'Assign staff is submitted',
  },
  FILE_NOT_FOUND: {
    key: 'file_not_found',
    message: 'File not found',
  },
  DATE_NOT_VALID: {
    key: 'date_not_valid',
    message: 'Date not valid',
  },
  DUPPLICATE_TASK_TIME_IN_TANK: {
    key: 'cannot_assign_more_tasks_at_the_same_time_in_a_tank',
    message: 'Cannot assign more tasks at the same time in a tank',
  },
})
