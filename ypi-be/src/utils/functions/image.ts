export function processLinkAsset(target, type) {
  if (target && target.length) {
    const dataTarget = target.filter((file) => file.type === type)
    dataTarget.map((file) => {
      if (!file.link.startsWith(process.env.AWS_S3_URL)) {
        file.link = process.env.AWS_S3_URL + '/' + file.link
      }
    })
  }
}

export function getLinkAsset(key) {
  if (key) {
    if (!key.startsWith(process.env.AWS_S3_URL)) {
      return process.env.AWS_S3_URL + '/' + key
    }
    return key
  }
  return null
}
