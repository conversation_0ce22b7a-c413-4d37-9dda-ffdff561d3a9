export function removeAccents(str) {
  if (str) {
    return str.normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/đ/g, 'd').replace(/Đ/g, 'D');
  }
  return str;
}

export function formatURL(str) {
  if (str) {
    str = removeAccents(str);
    let specialChars = ";%'`~!@#$^&%*()+=[]\/\\{}|:<>?,._";
    for (let i = 0; i < specialChars.length; i++) {
      str = str.replace(new RegExp("\\" + specialChars[i], "gi"), "");
    }
    str = str.replace(/\s/g, '-');
    str = str.toLowerCase();
  }
  return str;
}