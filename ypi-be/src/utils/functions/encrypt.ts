import * as crypto from 'crypto'

const secretKey = 'd7W3UBuMdGGBhUPqO443gQaXJdTtC704'
const iv = 'vk6Nb4dW0JGJDX6j'

export function encodeToken(token) {
  try {
    //@ts-ignore
    const cipher = crypto.createCipheriv(
      'aes-256-cbc',
      Buffer.from(secretKey),
      iv,
    )
    let encrypted = cipher.update(token, 'utf-8', 'hex')
    encrypted += cipher.final('hex')
    return encrypted
  } catch (e) {
    return ''
  }
}

export function decodeToken(encodedToken) {
  try {
    const decodedToken = encodedToken
    //@ts-ignore
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(secretKey),
      iv,
    )
    let decrypted = decipher.update(decodedToken, 'hex', 'utf-8')
    decrypted += decipher.final('utf-8')
    return decrypted
  } catch (e) {
    return ''
  }
}
