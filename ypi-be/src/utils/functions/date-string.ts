import moment from 'moment'

export function formatFilter(date, unit: 'start' | 'end') {
  return new Date(date.replace(' ', '+')).toISOString()

  if (date) {
    // Handle case date is "ISO_8601"
    if (moment(date, moment.ISO_8601).isValid()) {
      if (unit === 'start') {
        return moment(date).startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS')
      }
      if (unit === 'end') {
        return moment(date).endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS')
      }
    } else {
      return new Date(date.replace(' ', '+')).toISOString()
      // const clientZone =
      //   date.replace(' ', '+').slice(-6) || moment().format('Z z')
      // const replaceDate = date.replace(/\s\d{2}:\d{2}$/, '')
      // return moment(replaceDate)
      //   .utcOffset(clientZone)
      //   .format('YYYY-MM-DDTHH:mm:ss.SSS')
    }
  } else {
    if (unit === 'start') {
      return moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS')
    }
    if (unit === 'end') {
      return moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS')
    }
  }
}
