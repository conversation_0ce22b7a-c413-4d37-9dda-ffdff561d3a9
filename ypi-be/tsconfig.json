{"compilerOptions": {"module": "NodeNext", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ESNext", "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "paths": {"@core/*": ["./src/core/*"], "@modules/*": ["./src/modules/*"]}, "typeRoots": ["**.d.ts", "src/types/**/*", "./node_modules/@types/"]}}