const { pathsToModuleNameMapper } = require('ts-jest')
const { compilerOptions } = require('../tsconfig.json') // or tsconfig.e2e.json if you have a separate config for e2e tests

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  rootDir: '../', // adjust this as necessar
  testRegex: '.e2e-spec.ts$', // adjust this as necessary
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths),
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  moduleFileExtensions: ['js', 'json', 'ts'],
}
