{"editor.tabSize": 2, "editor.acceptSuggestionOnCommitCharacter": false, "editor.tabCompletion": "off", "editor.rulers": [100, 120], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.quickSuggestions": {"strings": true}, "[javascript]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascriptreact]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescript]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.preferences.importModuleSpecifier": "shortest", "javascript.updateImportsOnFileMove.enabled": "always", "javascript.preferences.importModuleSpecifier": "shortest", "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript][javascriptreact][typescript]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}}