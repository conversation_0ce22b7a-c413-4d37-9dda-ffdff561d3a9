import { MigrationInterface, QueryRunner } from 'typeorm'

export class InsertTankMaterials1734876543210 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert tank materials if they don't exist
    await queryRunner.query(`
      INSERT INTO "tank_material" ("name") 
      SELECT * FROM (VALUES 
        ('FRP/GRP'),
        ('RC'),
        ('SS')
      ) AS v(name)
      WHERE NOT EXISTS (
        SELECT 1 FROM "tank_material" WHERE "name" = v.name
      );
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the tank materials we added
    await queryRunner.query(`
      DELETE FROM "tank_material" 
      WHERE "name" IN ('FRP/GRP', 'RC', 'SS');
    `)
  }
}
