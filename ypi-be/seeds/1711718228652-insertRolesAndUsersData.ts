import { randomBytes, scryptSync } from 'node:crypto'
import { MigrationInterface, QueryRunner } from 'typeorm'

export class InsertRolesAndUsersData1711718228652
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const rolesResult = await queryRunner.query(`
      INSERT INTO "role" ("name", "value") VALUES
      ('SUPER ADMIN', 'SUPER_ADMIN'),
      ('ADMIN', 'ADMIN'),
      ('INSPECTOR', 'INSPECTOR'),
      ('STAFF', 'STAFF')
      RETURNING id, name, value;
    `)

    const superAdminRoleId = rolesResult[0].id
    const passwordSalt = randomBytes(16).toString('hex')
    const passwordHash = scryptSync('test@123456', passwordSalt, 32).toString(
      'hex',
    )

    await queryRunner.query(
      `
      INSERT INTO "user" ("email", "passwordSalt", "passwordHash", "roleId") VALUES
      ('<EMAIL>', $2, $3, $1);
    `,
      [superAdminRoleId, passwordSalt, passwordHash],
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
