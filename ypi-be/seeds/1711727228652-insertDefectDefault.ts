import { MigrationInterface, QueryRunner } from 'typeorm'

export class InsertDefectDefault1711727228652 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First truncate the defect table to ensure we start with a clean slate
    await queryRunner.query(`TRUNCATE TABLE "defect" RESTART IDENTITY CASCADE`)

    // Insert new defect list
    await queryRunner.query(`
      INSERT INTO "defect" ("title", "type", "qty", "size") VALUES
      ('Access doors using normal padlocks / lockset', 'default', null, null),
      ('Hatch & staple required', 'default', null, null),
      ('Access doors using EM locks (PUB approval?)', 'default', null, null),
      ('No record of access door key movement', 'default', null, null),
      ('Tank cover using normal padlock / not locked', 'default', null, null),
      ('Water tank not segregated from other services', 'default', null, null),
      ('SS bracket not installed / missing', 'default', null, null),
      ('SS bracket dislodged - mounting stud loosen', 'default', null, null),
      ('SS brackets not installed correctly (not at the centre / not following contour)', 'default', null, null),
      ('Tapered round nuts missing / not fully tightened', 'default', null, null),
      ('Electrode holder not secured / water leaking in', 'default', null, null),
      ('Key movement records to access door not used', 'default', null, null),
      ('Same keys used for access door & water tank manhole covers', 'default', null, null),
      ('Fencing welded mesh opening larger than 75x25mm (First turn-on 20/3/15)', 'default', null, null),
      ('Fencing not to full height (applicable to LLT)', 'default', null, null),
      ('RTT fencing less than 2.5m / No 300mm outrigger', 'default', null, null),
      ('Overflow manifold with washout pipe & terminate directly to floor trap.', 'default', null, null),
      ('Overflow / Washout / Warning pipe - Missing /Torn / Wrong netting mesh > 0.65sq', 'default', null, null),
      ('Air Vent - Damaged / Loose / Torn netting', 'default', null, null),
      ('Level Sensor - Wiring in Series (Looping) / Wiring & conduit need replacement', 'default', null, null),
      ('No sampling taps on washout pipe before valve (supply turned on or after 2005)', 'default', null, null),
      ('No Sampling tap / No padlock (normal)', 'default', null, null),
      ('Pilot valve pilot rod & return tube penetration hole not properly sealed.', 'default', null, null),
      ('Tank cover cracked / Not properly secure / Gap on closing', 'default', null, null),
      ('Water tank surface cracked / Holes / Gaps / Severe exposed fibre', 'default', null, null),
      ('Level sight indicator cracked / blur / top not capped', 'default', null, null),
      ('Cat ladder - Missing / No Cage for above 3m / Shaky', 'default', null, null),
      ('Tank leaking - Bottom / Side / Centre Panel', 'default', null, null),
      ('Non potable pipes above water tanks', 'default', null, null),
      ('Water tank area / pump room used as store', 'default', null, null),
      ('Control Panel / Component faulty', 'default', null, null),
      ('Bird dropping on tank / Surrounding tank', 'default', null, null),
      ('No proper anchoring point for fall prevention & rescue', 'default', null, null),
      ('Valves faulty - Tank supply / balancing / gravity / booster / drain - C1 / C2 / C3', 'default', null, null),
      ('Faulty Ball Float / Pilot Valve / Electrode - Repair / Replace - 1.5 / 2 / 2.5 / 3 / 4 / 6 / 8" C1 / C2 / C3', 'default', null, null)
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async down(queryRunner: QueryRunner): Promise<void> {
    // If needed, you can implement rollback logic here
  }
}
