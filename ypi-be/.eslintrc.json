{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": "./tsconfig.json",
    "sourceType": "module"
  },
  "plugins": ["@typescript-eslint/eslint-plugin", "prettier"],
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "prettier"
  ],
  "env": {
    "node": true,
    "jest": true
  },
  "ignorePatterns": [".eslintrc.json"],
  "rules": {
    // "@typescript-eslint/interface-name-prefix": "off",
    // "@typescript-eslint/explicit-function-return-type": "off",
    // "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "no-useless-escape": "off",
    "import/no-unresolved": "off",
    "import/no-duplicates": "error",
    "object-curly-newline": "off",
    "array-element-newline": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-var-requires": "off",
    "no-console": "off",
    "max-lines": ["error", 10000],
    "max-lines-per-function": ["error", 2000],
    "no-alert": "error",
    "no-multi-spaces": "error",
    "no-multiple-empty-lines": [
      "error",
      {
        "max": 1,
        "maxEOF": 0
      }
    ],
    "no-useless-catch": 0,
    "no-use-before-define": [
      "off",
      {
        "functions": false,
        "classes": false,
        "variables": true
      }
    ],
    "no-empty": [
      "error",
      {
        "allowEmptyCatch": true
      }
    ],
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ]
  }
}
