{"name": "ypi-be-project", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "nest start --watch", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.config.js", "mg:run": "npm run typeorm migration:run -- -d ./src/core/databases/typeorm.config.ts", "mg:generate": "npm run typeorm -- -d ./src/core/databases/typeorm.config.ts migration:generate ./migrations/%npm_config_name%", "mg:generate:win": "npm run typeorm -- -d ./src/core/databases/typeorm.config.ts migration:generate ./migrations/%npm_config_name%", "mg:create": "npm run typeorm -- migration:create ./migrations/%npm_config_name%", "seed:create": "npm run typeorm -- migration:create ./seeds/%npm_config_name%", "mg:revert": "npm run typeorm -- -d ./src/core/databases/typeorm.config.ts migration:revert", "typeorm": "nest build && ts-node ./node_modules/typeorm/cli"}, "dependencies": {"@aws-sdk/client-s3": "^3.631.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.1.17", "@nestjs/typeorm": "^10.0.1", "@sendgrid/mail": "^8.1.0", "aws-sdk": "^2.1583.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "ioredis": "^5.4.1", "lodash": "^4.17.21", "mjml": "^4.15.3", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mustache": "^4.2.0", "nest-winston": "^1.9.4", "nodemailer": "^6.9.14", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.11.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "^0.33.2", "typeorm": "^0.3.17", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/mustache": "^4.2.5", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.15", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.5.0", "prettier": "^3.1.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}