import { Client } from 'pg'

async function addTankMaterials() {
  // Database connection configuration
  const client = new Client({
    host: 'localhost',
    port: 54321,
    user: 'property',
    password: 'Password5pecent',
    database: 'ypi_dev',
  })

  try {
    await client.connect()
    console.log('Database connected successfully')

    // Define the materials to add
    const materials = ['FRP/GRP', 'RC', 'SS']

    for (const materialName of materials) {
      // Check if material already exists
      const existingResult = await client.query(
        'SELECT * FROM "tank_material" WHERE "name" = $1',
        [materialName],
      )

      if (existingResult.rows.length === 0) {
        // Create new material
        await client.query('INSERT INTO "tank_material" ("name") VALUES ($1)', [
          materialName,
        ])
        console.log(`Added tank material: ${materialName}`)
      } else {
        console.log(`Tank material already exists: ${materialName}`)
      }
    }

    // List all materials
    const allMaterialsResult = await client.query(
      'SELECT * FROM "tank_material" ORDER BY "name"',
    )
    console.log('\nAll tank materials:')
    allMaterialsResult.rows.forEach((material) => {
      console.log(`- ${material.name} (ID: ${material.id})`)
    })
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await client.end()
    console.log('\nDatabase connection closed')
  }
}

// Run the script
addTankMaterials()
