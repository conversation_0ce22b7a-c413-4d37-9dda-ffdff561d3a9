APP_NAME='YPI'
# production
MODE=development
PORT=8000

# 2 days
TOKEN_EXPIRE=172800000
REFRESH_TOKEN_EXPIRE=355600000
TOKEN_SECRET=8f9b7df3243c2105ba849a288f94fd4b

SENDGRID_KEY=*********************************************************************
SENDGRID_MAIL_FROM=<EMAIL>

PG_DB_HOST=localhost
PG_DB_PORT=54321
PG_DB_USER=devbe
PG_DB_PASSWORD=core!@34ypi
PG_DB_NAME=ypi

# PG_DB_HOST=**************
# PG_DB_PORT=54321
# PG_DB_USER=devbe
# PG_DB_PASSWORD=core!@34ypi
# PG_DB_NAME=ypi

REDIS_PASSWORD=redis!@34ypi
REDIS_PORT=1234

AWS_S3_URL='https://local-ladycarevn.s3.ap-southeast-1.amazonaws.com'
AWS_S3_ACCESS_KEY='********************'
AWS_S3_SECRET_KEY='u6FBMELasicbB0qPAVUakL1M0VYUdF+s28uBrwNm'
AWS_S3_BUCKET_NAME='local-ladycarevn'
AWS_S3_REGION=ap-southeast-1


# SMTP
AWS_SES_SMTP_USER=********************
AWS_SES_SMTP_PASS=BFqWpIWaUltNUMqnTXp92FHNzKqTirCxjNwIeEY1sJ1p
AWS_SES_ENDPOINT=email-smtp.ap-southeast-1.amazonaws.com
AWS_SES_SENDER=<EMAIL>