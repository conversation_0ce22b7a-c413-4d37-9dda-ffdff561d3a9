version: 2.1

jobs:
  build_dev:
    docker:
      - image: cimg/node:20.4.0-browsers
    resource_class: large
    steps:
      - run:
          shell: /bin/sh
          command: |
            sudo apt update
            sudo apt install git -y
            git --version
      - run:
          name: Install rsync
          command: |
            sudo apt-get update
            sudo apt-get install rsync
      - checkout
      - restore_cache:
          keys:
            - yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Checkout development branch
          command: |
            git checkout development
            git pull origin development
      - run:
          name: Install dependencies
          command: |
            yarn
      - save_cache:
          paths:
            - node_modules
          key: yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Create .env file
          command: |
            echo "APP_NAME=${APP_NAME}" >> .env
            echo "MODE=${MODE}" >> .env
            echo "PORT=${PORT}" >> .env
            echo "TOKEN_EXPIRE=${TOKEN_EXPIRE}" >> .env
            echo "REFRESH_TOKEN_EXPIRE=${REFRESH_TOKEN_EXPIRE}" >> .env
            echo "TOKEN_SECRET=${TOKEN_SECRET}" >> .env
            echo "PG_DB_HOST=${PG_DB_HOST}" >> .env
            echo "PG_DB_PORT=${PG_DB_PORT}" >> .env
            echo "PG_DB_USER=${PG_DB_USER}" >> .env
            echo "PG_DB_PASSWORD=${PG_DB_PASSWORD}" >> .env
            echo "PG_DB_NAME=${PG_DB_NAME}" >> .env
            echo "REDIS_PASSWORD=${REDIS_PASSWORD}" >> .env
            echo "REDIS_PORT=${REDIS_PORT}" >> .env
            echo "SENDGRID_KEY=${SENDGRID_KEY}" >> .env
            echo "SENDGRID_MAIL_FROM=${SENDGRID_MAIL_FROM}" >> .env
            echo "AWS_S3_URL=${AWS_S3_URL}" >> .env
            echo "AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}" >> .env
            echo "AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}" >> .env
            echo "AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}" >> .env
            echo "AWS_SES_SMTP_USER=${AWS_SES_SMTP_USER}" >> .env
            echo "AWS_SES_SMTP_PASS=${AWS_SES_SMTP_PASS}" >> .env
            echo "AWS_SES_ENDPOINT=${AWS_SES_ENDPOINT}" >> .env
            echo "AWS_SES_SENDER=${AWS_SES_SENDER}" >> .env
            echo "AWS_S3_REGION=${AWS_S3_REGION}" >> .env
      - run:
          name: Build
          command: |
            export NODE_OPTIONS=--max-old-space-size=8192
            yarn add sharp --ignore-engines
            yarn build
      - add_ssh_keys
      - run:
          name: Add the server to known hosts
          command: |
            ssh-keyscan -H ${SERVER_PROD} >> ~/.ssh/known_hosts
      - run:
          name: Upload files to server
          command: |
            rsync -avce ssh --delete ./. ubuntu@${SERVER_PROD}:/var/www/html
      - run:
          name: Run migration and restart server
          command: |
            ssh ubuntu@${SERVER_PROD} "source ~/.nvm/nvm.sh && cd /var/www/html && ls -la && source ~/.bashrc && npm run typeorm migration:run -- -d ./src/core/databases/typeorm.config.ts && pm2 restart ypi"

  build_prod:
    docker:
      - image: cimg/deploy:2024.08-node
    steps:
      - setup_remote_docker
      - checkout
      - run:
          name: Build Docker image
          command: |
            docker build --platform linux/amd64 -t ypi-be .
      - run:
          name: Save Docker image to tar file
          command: |
            docker save ypi-be -o ypi-be.tar
      - persist_to_workspace:
          root: .
          paths:
            - ypi-be.tar

  deploy_prod:
    docker:
      - image: cimg/base:stable
    steps:
      - setup_remote_docker
      - attach_workspace:
          at: .
      - run:
          name: Load image to docker
          command: |
            docker load -i ypi-be.tar
      - run:
          name: Login to docker hub
          command: |
            docker login -u ${DOCKER_USER} -p ${DOCKER_PASS}
      - run:
          name: Push to docker hub
          command: |
            docker tag ypi-be:latest ${DOCKER_USER}/ypi-be:latest
            docker push ${DOCKER_USER}/ypi-be:latest

workflows:
  build-and-deploy-dev:
    jobs:
      - build_dev:
          filters:
            branches:
              only: development

  build-and-deploy-prod:
    jobs:
      - build_prod:
          filters:
            branches:
              only: production
      - deploy_prod:
          requires:
            - build_prod
          filters:
            branches:
              only: production
