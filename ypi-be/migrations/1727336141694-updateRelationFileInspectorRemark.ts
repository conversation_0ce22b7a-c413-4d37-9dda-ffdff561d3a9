import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateRelationFileInspectorRemark1727336141694
  implements MigrationInterface
{
  name = 'UpdateRelationFileInspectorRemark1727336141694'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "file" ADD "inspectorRemarkId" uuid`)

    await queryRunner.query(
      `ALTER TABLE "file" ADD CONSTRAINT "FK_0e2591478f0846639ba742ee5f4" FOREIGN KEY ("inspectorRemarkId") REFERENCES "task"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "file" DROP CONSTRAINT "FK_0e2591478f0846639ba742ee5f4"`,
    )

    await queryRunner.query(
      `ALTER TABLE "file" DROP COLUMN "inspectorRemarkId"`,
    )
  }
}
