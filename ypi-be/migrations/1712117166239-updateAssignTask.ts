import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1712117166239 implements MigrationInterface {
    name = 'UpdateAssignTask1712117166239'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "isStaffSubmitted" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "isInspectorSubmitted" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "staffSubmittedBy" uuid`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "inspectorSubmittedBy" uuid`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_c806b59a8a7be25c56f967423b3" FOREIGN KEY ("staffSubmittedBy") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_97af1306ed4fd398329c95b986b" FOREIGN KEY ("inspectorSubmittedBy") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_97af1306ed4fd398329c95b986b"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_c806b59a8a7be25c56f967423b3"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "inspectorSubmittedBy"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "staffSubmittedBy"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "isInspectorSubmitted"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "isStaffSubmitted"`);
    }

}
