import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCustomerIdToAgent1741747606928 implements MigrationInterface {
  name = 'AddCustomerIdToAgent1741747606928'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "agent" ADD "customerId" uuid`)

    await queryRunner.query(
      `ALTER TABLE "agent" ADD CONSTRAINT "FK_agent_customer" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "agent" DROP CONSTRAINT "FK_agent_customer"`,
    )

    await queryRunner.query(`ALTER TABLE "agent" DROP COLUMN "customerId"`)
  }
}
