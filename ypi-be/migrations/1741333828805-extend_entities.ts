import { MigrationInterface, QueryRunner } from 'typeorm'

export class ExtendEntities1741333828805 implements MigrationInterface {
  name = 'ExtendEntities1741333828805'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "pcNumber" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "building" ADD "address" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "in_charge" ADD "phoneNumber" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "in_charge" ADD "officePhoneNumber" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "in_charge" ADD "email" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "location" ADD "address" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "assign_task" ADD "nextDueDate" TIMESTAMP WITH TIME ZONE`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "assign_task" DROP COLUMN "nextDueDate"`,
    )
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address"`)
    await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "pcNumber"`)
    await queryRunner.query(`ALTER TABLE "building" DROP COLUMN "address"`)
    await queryRunner.query(`ALTER TABLE "in_charge" DROP COLUMN "phoneNumber"`)
    await queryRunner.query(
      `ALTER TABLE "in_charge" DROP COLUMN "officePhoneNumber"`,
    )
    await queryRunner.query(`ALTER TABLE "in_charge" DROP COLUMN "email"`)
  }
}
