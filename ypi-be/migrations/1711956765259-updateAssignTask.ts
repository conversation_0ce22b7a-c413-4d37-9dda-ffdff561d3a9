import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1711956765259 implements MigrationInterface {
    name = 'UpdateAssignTask1711956765259'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_8d8e2cec17a3b01bb23782f6eef"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "REL_8d8e2cec17a3b01bb23782f6ee"`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_8d8e2cec17a3b01bb23782f6eef" FOREIGN KEY ("tankId") REFERENCES "tank"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_8d8e2cec17a3b01bb23782f6eef"`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "REL_8d8e2cec17a3b01bb23782f6ee" UNIQUE ("tankId")`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_8d8e2cec17a3b01bb23782f6eef" FOREIGN KEY ("tankId") REFERENCES "tank"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

}
