import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateAssignTask1712741353374 implements MigrationInterface {
  name = 'UpdateAssignTask1712741353374'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "assign_task" ADD "inspectorSubmittedDate" TIMESTAMP WITH TIME ZONE`)
    await queryRunner.query(`ALTER TABLE "assign_task" ADD "completedDate" TIMESTAMP WITH TIME ZONE`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "completedDate"`)
    await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "inspectorSubmittedDate"`)
  }
}
