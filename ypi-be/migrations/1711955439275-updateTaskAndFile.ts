import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTaskAndFile1711955439275 implements MigrationInterface {
    name = 'UpdateTaskAndFile1711955439275'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" ADD "qty" integer`);
        await queryRunner.query(`ALTER TABLE "task" ADD "size" character varying`);
        await queryRunner.query(`ALTER TABLE "file" ADD "taskId" uuid`);
        await queryRunner.query(`ALTER TYPE "public"."file_type_enum" RENAME TO "file_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."file_type_enum" AS ENUM('document', 'certification', 'image')`);
        await queryRunner.query(`ALTER TABLE "file" ALTER COLUMN "type" TYPE "public"."file_type_enum" USING "type"::"text"::"public"."file_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."file_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "file" ADD CONSTRAINT "FK_e0479c7972553f7b6e78361e931" FOREIGN KEY ("taskId") REFERENCES "task"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "file" DROP CONSTRAINT "FK_e0479c7972553f7b6e78361e931"`);
        await queryRunner.query(`CREATE TYPE "public"."file_type_enum_old" AS ENUM('document', 'certification')`);
        await queryRunner.query(`ALTER TABLE "file" ALTER COLUMN "type" TYPE "public"."file_type_enum_old" USING "type"::"text"::"public"."file_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."file_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."file_type_enum_old" RENAME TO "file_type_enum"`);
        await queryRunner.query(`ALTER TABLE "file" DROP COLUMN "taskId"`);
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "size"`);
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "qty"`);
    }

}
