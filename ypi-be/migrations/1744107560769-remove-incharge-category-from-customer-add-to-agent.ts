import { MigrationInterface, QueryRunner } from 'typeorm'

export class RemoveInchargeCategoryFromCustomerAddToAgent1744107560769
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add inChargeCategory to agent table
    await queryRunner.query(
      `ALTER TABLE "agent" ADD "inChargeCategory" character varying`,
    )

    // Remove inChargeCategory from customer table
    await queryRunner.query(
      `ALTER TABLE "customer" DROP COLUMN "inChargeCategory"`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add inChargeCategory back to customer table
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "inChargeCategory" character varying`,
    )

    // Remove inChargeCategory from agent table
    await queryRunner.query(
      `ALTER TABLE "agent" DROP COLUMN "inChargeCategory"`,
    )
  }
}
