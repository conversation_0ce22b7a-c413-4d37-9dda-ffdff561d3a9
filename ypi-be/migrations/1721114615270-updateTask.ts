import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTask1721114615270 implements MigrationInterface {
  name = 'UpdateTask1721114615270'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" ADD "customerSignature" character varying`)
    await queryRunner.query(`ALTER TABLE "task" ADD "customerSignatureDate" TIMESTAMP WITH TIME ZONE`)
    await queryRunner.query(`ALTER TABLE "task" ADD "completedDate" TIMESTAMP WITH TIME ZONE`)
    await queryRunner.query(`ALTER TABLE "task" ADD "receiveSignatureBy" uuid`)
    await queryRunner.query(`ALTER TYPE "public"."assign_task_status_enum" RENAME TO "assign_task_status_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."assign_task_status_enum" AS ENUM('pre-inspection', 'prepare', 'completed', 'confirmed', 'unresolved')`)
    await queryRunner.query(`ALTER TABLE "assign_task" ALTER COLUMN "status" TYPE "public"."assign_task_status_enum" USING "status"::"text"::"public"."assign_task_status_enum"`)
    await queryRunner.query(`DROP TYPE "public"."assign_task_status_enum_old"`)
    await queryRunner.query(`ALTER TABLE "task" ADD CONSTRAINT "FK_7e5cba2807c0c100bf97079686a" FOREIGN KEY ("receiveSignatureBy") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_7e5cba2807c0c100bf97079686a"`)
    await queryRunner.query(`CREATE TYPE "public"."assign_task_status_enum_old" AS ENUM('pre-inspection', 'prepare', 'completed', 'confirmed')`)
    await queryRunner.query(`ALTER TABLE "assign_task" ALTER COLUMN "status" TYPE "public"."assign_task_status_enum_old" USING "status"::"text"::"public"."assign_task_status_enum_old"`)
    await queryRunner.query(`DROP TYPE "public"."assign_task_status_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."assign_task_status_enum_old" RENAME TO "assign_task_status_enum"`)
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "receiveSignatureBy"`)
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "completedDate"`)
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "customerSignatureDate"`)
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "customerSignature"`)
  }
}
