import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTask1712045584763 implements MigrationInterface {
    name = 'UpdateTask1712045584763'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" ADD "extra" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "extra"`);
    }

}
