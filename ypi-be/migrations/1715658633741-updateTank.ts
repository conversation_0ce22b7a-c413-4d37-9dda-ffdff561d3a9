import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTank1715658633741 implements MigrationInterface {
  name = 'UpdateTank1715658633741'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tank" ADD "isActive" boolean NOT NULL DEFAULT true`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tank" DROP COLUMN "isActive"`)
  }
}
