import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddBuildingCategoryInchargeCategoryToCustomer1744082925552
  implements MigrationInterface
{
  name = 'AddBuildingCategoryInchargeCategoryToCustomer1744082925552'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "buildingCategory" character varying`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "inChargeCategory" character varying`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customer" DROP COLUMN "inChargeCategory"`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" DROP COLUMN "buildingCategory"`,
    )
  }
}
