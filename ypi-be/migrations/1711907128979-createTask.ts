import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTask1711907128979 implements MigrationInterface {
    name = 'CreateTask1711907128979'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "task" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "remark" character varying, "defectId" uuid, "assignTaskId" uuid, CONSTRAINT "PK_fb213f79ee45060ba925ecd576e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_4a54e88f8c42954be40d039f6a" ON "task" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_25f8e41dd2687e4413a471f27f" ON "task" ("updatedAt") `);
        await queryRunner.query(`ALTER TABLE "task" ADD CONSTRAINT "FK_f46164353b978d32c858f124300" FOREIGN KEY ("defectId") REFERENCES "defect"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "task" ADD CONSTRAINT "FK_869161c1a684d502c3db59ebc86" FOREIGN KEY ("assignTaskId") REFERENCES "assign_task"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_869161c1a684d502c3db59ebc86"`);
        await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_f46164353b978d32c858f124300"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_25f8e41dd2687e4413a471f27f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4a54e88f8c42954be40d039f6a"`);
        await queryRunner.query(`DROP TABLE "task"`);
    }

}
