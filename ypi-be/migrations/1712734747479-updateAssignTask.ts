import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1712734747479 implements MigrationInterface {
    name = 'UpdateAssignTask1712734747479'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "isConfirmed" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "isConfirmed"`);
    }

}
