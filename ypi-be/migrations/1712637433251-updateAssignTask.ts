import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateAssignTask1712637433251 implements MigrationInterface {
  name = 'UpdateAssignTask1712637433251'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."assign_task_status_enum" RENAME TO "assign_task_status_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."assign_task_status_enum" AS ENUM('pre-inspection', 'prepare', 'completed', 'confirmed')`)
    await queryRunner.query(`ALTER TABLE "assign_task" ALTER COLUMN "status" TYPE "public"."assign_task_status_enum" USING "status"::"text"::"public"."assign_task_status_enum"`)
    await queryRunner.query(`DROP TYPE "public"."assign_task_status_enum_old"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."assign_task_status_enum_old" AS ENUM('pre-inspection', 'prepare', 'completed')`)
    await queryRunner.query(`ALTER TABLE "assign_task" ALTER COLUMN "status" TYPE "public"."assign_task_status_enum_old" USING "status"::"text"::"public"."assign_task_status_enum_old"`)
    await queryRunner.query(`DROP TYPE "public"."assign_task_status_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."assign_task_status_enum_old" RENAME TO "assign_task_status_enum"`)
  }
}
