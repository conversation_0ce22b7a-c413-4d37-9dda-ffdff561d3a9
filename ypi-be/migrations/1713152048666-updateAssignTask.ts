import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1713152048666 implements MigrationInterface {
    name = 'UpdateAssignTask1713152048666'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "customerSignatureDate" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "customerSignatureDate"`);
    }

}
