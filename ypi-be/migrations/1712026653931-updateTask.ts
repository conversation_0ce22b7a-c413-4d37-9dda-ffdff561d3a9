import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTask1712026653931 implements MigrationInterface {
    name = 'UpdateTask1712026653931'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" ADD "staffRemark" character varying`);
        await queryRunner.query(`ALTER TABLE "task" ADD "staffRemarkBy" uuid`);
        await queryRunner.query(`ALTER TABLE "task" ADD CONSTRAINT "FK_2fcf1188bdf23ef4642a81ba597" FOREIGN KEY ("staffRemarkBy") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_2fcf1188bdf23ef4642a81ba597"`);
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "staffRemarkBy"`);
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "staffRemark"`);
    }

}
