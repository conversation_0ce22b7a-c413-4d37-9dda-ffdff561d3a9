import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateCustomer1714717315382 implements MigrationInterface {
  name = 'UpdateCustomer1714717315382'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "customer" ADD "isActive" boolean NOT NULL DEFAULT true`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "isActive"`)
  }
}
