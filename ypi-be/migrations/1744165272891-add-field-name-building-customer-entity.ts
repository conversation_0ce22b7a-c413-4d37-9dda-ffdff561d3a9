import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddFieldNameBuildingCustomerEntity1744165272891
  implements MigrationInterface
{
  name = 'AddFieldNameBuildingCustomerEntity1744165272891'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "nameBuilding" character varying`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "nameBuilding"`)
  }
}
