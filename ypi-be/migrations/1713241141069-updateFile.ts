import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFile1713241141069 implements MigrationInterface {
  name = 'UpdateFile1713241141069'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "file" ADD "tankId" uuid`)
    await queryRunner.query(`ALTER TABLE "file" ADD "assignTaskId" uuid`)
    await queryRunner.query(
      `ALTER TYPE "public"."file_type_enum" RENAME TO "file_type_enum_old"`,
    )
    await queryRunner.query(
      `CREATE TYPE "public"."file_type_enum" AS ENUM('document', 'certification', 'image', 'lab-report')`,
    )
    await queryRunner.query(
      `ALTER TABLE "file" ALTER COLUMN "type" TYPE "public"."file_type_enum" USING "type"::"text"::"public"."file_type_enum"`,
    )
    await queryRunner.query(`DROP TYPE "public"."file_type_enum_old"`)
    await queryRunner.query(
      `ALTER TABLE "file" ADD CONSTRAINT "FK_65c7b9b11cf14e84ec07673add8" FOREIGN KEY ("tankId") REFERENCES "tank"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "file" ADD CONSTRAINT "FK_3775768a6a42df25658dd562198" FOREIGN KEY ("assignTaskId") REFERENCES "assign_task"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "file" DROP CONSTRAINT "FK_3775768a6a42df25658dd562198"`,
    )
    await queryRunner.query(
      `ALTER TABLE "file" DROP CONSTRAINT "FK_65c7b9b11cf14e84ec07673add8"`,
    )
    await queryRunner.query(
      `CREATE TYPE "public"."file_type_enum_old" AS ENUM('document', 'certification', 'image')`,
    )
    await queryRunner.query(
      `ALTER TABLE "file" ALTER COLUMN "type" TYPE "public"."file_type_enum_old" USING "type"::"text"::"public"."file_type_enum_old"`,
    )
    await queryRunner.query(`DROP TYPE "public"."file_type_enum"`)
    await queryRunner.query(
      `ALTER TYPE "public"."file_type_enum_old" RENAME TO "file_type_enum"`,
    )
    await queryRunner.query(`ALTER TABLE "file" DROP COLUMN "assignTaskId"`)
    await queryRunner.query(`ALTER TABLE "file" DROP COLUMN "tankId"`)
  }
}
