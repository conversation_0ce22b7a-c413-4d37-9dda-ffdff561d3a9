import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1715311874738 implements MigrationInterface {
    name = 'UpdateAssignTask1715311874738'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "sampleTakenDate" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "sampleTakenDate"`);
    }

}
