import { MigrationInterface, QueryRunner } from 'typeorm'

export class createSe<PERSON>ForJobId1727669116999 implements MigrationInterface {
  name = 'createSequenceForJobId1727669116999'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE SEQUENCE job_id_numeric_seq
        START 1
        MINVALUE 1
        MAXVALUE 9999999
        CYCLE;
      `)

    await queryRunner.query(`
        CREATE TABLE job_id_prefix (
          current_prefix CHAR(1) DEFAULT 'A'
        );
      `)

    await queryRunner.query(`
        INSERT INTO job_id_prefix (current_prefix) VALUES ('A');
      `)

    await queryRunner.query(`
        CREATE OR REPLACE FUNCTION generate_job_id()
        RETURNS TEXT AS $$
        DECLARE
            next_num BIGINT;
            prefix CHAR(1);
        BEGIN
            next_num := nextval('job_id_numeric_seq');

            SELECT current_prefix INTO prefix FROM job_id_prefix;

            IF prefix = 'Z' AND next_num = 9999999 THEN
            next_num := 1;
            prefix := 'A';
            ALTER SEQUENCE job_id_numeric_seq RESTART WITH 1;
            UPDATE job_id_prefix SET current_prefix = prefix;
            
            ELSIF next_num = 1 AND (currval('job_id_numeric_seq') % 9999999 = 0) THEN
            prefix := chr(ascii(prefix) + 1);

            UPDATE job_id_prefix SET current_prefix = prefix;
            END IF;

            -- Return the concatenated result with zero-padded number (e.g., A0000001)
            RETURN prefix || LPAD(next_num::TEXT, 7, '0');
        END;
        $$ LANGUAGE plpgsql;
      `)

    await queryRunner.query(`
        ALTER TABLE assign_task
        ALTER COLUMN code SET DEFAULT generate_job_id();
      `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE assign_task ALTER COLUMN code DROP DEFAULT; `)

    await queryRunner.query(`
        DROP FUNCTION IF EXISTS generate_job_id;
      `)

    await queryRunner.query(`
        DROP TABLE IF EXISTS job_id_prefix;
      `)

    await queryRunner.query(`
        DROP SEQUENCE IF EXISTS job_id_numeric_seq;
      `)
  }
}
