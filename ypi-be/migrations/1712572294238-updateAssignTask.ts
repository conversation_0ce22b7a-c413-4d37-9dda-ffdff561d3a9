import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1712572294238 implements MigrationInterface {
    name = 'UpdateAssignTask1712572294238'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "staffSignature" character varying`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "inspectorSignature" character varying`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "staffSignatureBy" uuid`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "inspectorSignatureBy" uuid`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_c1c47b2da4ec8f9c5c7a41e0c58" FOREIGN KEY ("staffSignatureBy") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_ea06cb4a41996865774515d652b" FOREIGN KEY ("inspectorSignatureBy") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_ea06cb4a41996865774515d652b"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_c1c47b2da4ec8f9c5c7a41e0c58"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "inspectorSignatureBy"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "staffSignatureBy"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "inspectorSignature"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "staffSignature"`);
    }

}
