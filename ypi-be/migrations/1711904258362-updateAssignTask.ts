import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1711904258362 implements MigrationInterface {
    name = 'UpdateAssignTask1711904258362'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "phase" character varying`);
        await queryRunner.query(`CREATE TYPE "public"."assign_task_status_enum" AS ENUM(' pre-inspection', 'prepare', 'completed')`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "status" "public"."assign_task_status_enum"`);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."assign_task_status_enum"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "phase"`);
    }

}
