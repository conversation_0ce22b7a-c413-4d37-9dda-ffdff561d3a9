import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1711954882789 implements MigrationInterface {
    name = 'UpdateAssignTask1711954882789'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "acceptRectify" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "estimatedValue" real NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "estimatedValue"`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "acceptRectify"`);
    }

}
