import { MigrationInterface, QueryRunner } from 'typeorm'

export class Initial1711712028652 implements MigrationInterface {
  name = 'Initial1711712028652'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."file_type_enum" AS ENUM('document', 'certification', 'image', 'lab-report')`,
    )
    await queryRunner.query(
      `CREATE TYPE "public"."staff_position_enum" AS ENUM('worker', 'supervisor')`,
    )
    await queryRunner.query(
      `CREATE TYPE "public"."tank_type_enum" AS ENUM('low', 'intermediate', 'high')`,
    )
    await queryRunner.query(
      `CREATE TYPE "public"."tank_shape_enum" AS ENUM('rectangular', 'cylindrical', 'irregular')`,
    )
    await queryRunner.query(
      `CREATE TYPE "public"."defect_type_enum" AS ENUM('default')`,
    )
    await queryRunner.query(
      `CREATE TABLE "in_charge" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, CONSTRAINT "PK_533c49fb3424dde69186765f457" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_abdd4e48d259667d0be07c053d" ON "in_charge" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_1b362481caffcd2074fe150987" ON "in_charge" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "agent" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "company" character varying, "email" character varying, "phoneCode" character varying, "phoneNumber" character varying, "postalCode" character varying, "designation" character varying, "blockNo" character varying, "street" character varying, "building" character varying, "inChargeId" uuid, CONSTRAINT "PK_1000e989398c5d4ed585cf9a46f" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_6a9c6c5922cc23c303e37a79f8" ON "agent" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_b04e0edabde6f2aa86007b1237" ON "agent" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "building" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, CONSTRAINT "PK_bbfaf6c11f141a22d2ab105ee5f" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_04ef24a69866be541a8fe93267" ON "building" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_536debbe8434ccbc5ebafeace1" ON "building" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "token" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "accessToken" character varying, "accessTokenExp" character varying, "accessTokenIat" character varying, "refreshToken" character varying, "refreshTokenExp" character varying, "refreshTokenIat" character varying, "deviceId" character varying, "userId" uuid, CONSTRAINT "PK_82fae97f905930df5d62a702fc9" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_929f7d7caa0b6a61df7735a267" ON "token" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_197e8aaa4fb25b28d8bcb24a7f" ON "token" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "file" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "name" character varying, "mineType" character varying, "link" character varying, "type" "public"."file_type_enum", "staffId" uuid, CONSTRAINT "PK_36b46d232307066b3a2c9ea3a1d" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_18a0ad156828b598fcef570209" ON "file" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_d336bee718f4b96da84e8a2b1c" ON "file" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "staff" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "code" character varying, "email" character varying, "phoneCode" character varying, "phoneNumber" character varying, "fullName" character varying, "firstName" character varying, "lastName" character varying, "address" character varying, "birthday" TIMESTAMP WITH TIME ZONE, "position" "public"."staff_position_enum", "isActive" boolean NOT NULL DEFAULT true, "managedBy" uuid, CONSTRAINT "PK_e4ee98bb552756c180aec1e854a" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_e1d1cbbb31e98e16625555fc03" ON "staff" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_e5b5b978e4e86a0ebe8b8d7de9" ON "staff" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "value" character varying NOT NULL, CONSTRAINT "UQ_98082dbb08817c9801e32dd0155" UNIQUE ("value"), CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_3c39bd046f5e69d37f0e4fe768" ON "role" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_824e186a844b0ca85bb8e6a14e" ON "role" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "username" character varying, "email" character varying, "company" character varying, "phoneCode" character varying, "phoneNumber" character varying, "fullName" character varying, "firstName" character varying, "lastName" character varying, "avatar" character varying, "passwordSalt" character varying, "passwordHash" character varying, "passwordChangeToken" character varying, "isActive" boolean NOT NULL DEFAULT true, "roleId" uuid, "staffId" uuid, CONSTRAINT "REL_8f18060284824a8516b6bc325f" UNIQUE ("staffId"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_e11e649824a45d8ed01d597fd9" ON "user" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_80ca6e6ef65fb9ef34ea8c90f4" ON "user" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "customer" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "pubNumber" character varying, "name" character varying NOT NULL, "email" character varying, "phoneCode" character varying, "phoneNumber" character varying, "officePhoneCode" character varying, "officePhoneNumber" character varying, "buildingId" uuid, "agentId" uuid, "managedBy" uuid, CONSTRAINT "REL_e70c458e68d7634ab5b4ef7cd0" UNIQUE ("agentId"), CONSTRAINT "PK_a7a13f4cacb744524e44dfdad32" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_6042277b62323ae370b2d79681" ON "customer" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_0d1052d66fcdaf80d9587e4fb7" ON "customer" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "location" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "postalCode" character varying, "blockNo" character varying, "street" character varying, "building" character varying, "customerId" uuid, CONSTRAINT "PK_876d7bdba03c72251ec4c2dc827" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_09dee1a3d42723079bd4557791" ON "location" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_b21f4d902db7cc1c5db2e86d3d" ON "location" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "tank_material" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, CONSTRAINT "PK_3430633e5c05ad1e6efc7e5a3c0" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_061365179c4105736445692175" ON "tank_material" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_893196b142eb6b55705c5461b5" ON "tank_material" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "tank" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "airdentity" character varying, "code" character varying, "type" "public"."tank_type_enum", "shape" "public"."tank_shape_enum", "length" character varying, "width" character varying, "height" character varying, "effectiveCap" character varying, "floorLevel" character varying, "waterSaved" character varying, "locationId" uuid, "materialId" uuid, CONSTRAINT "PK_7c34d00328207090cdf572bb15a" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_723594b8b8fa5b704ecc16fa56" ON "tank" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_2e553edbe26f8156f99aa22819" ON "tank" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "assign_staff" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "staffId" uuid, "assignTaskId" uuid, CONSTRAINT "PK_5618769d79a7f07bd90142e04d1" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_d31e914d8d5a1769d8a984f35b" ON "assign_staff" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_c74a49364712d79e1327489654" ON "assign_staff" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "assign_task" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "tankId" uuid, "startAt" TIMESTAMP WITH TIME ZONE, "endAt" TIMESTAMP WITH TIME ZONE, "code" character varying NOT NULL, CONSTRAINT "UQ_5a0f4c9add2d347921d1ffe9238" UNIQUE ("code"), CONSTRAINT "REL_8d8e2cec17a3b01bb23782f6ee" UNIQUE ("tankId"), CONSTRAINT "PK_474b99b1386704515f9d35f0696" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_56d8af636ca6191a1ea6253065" ON "assign_task" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_b13e58c847dcabbf3b4b33e208" ON "assign_task" ("updatedAt") `,
    )
    await queryRunner.query(
      `CREATE TABLE "defect" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "title" character varying NOT NULL, "type" "public"."defect_type_enum", "description" character varying, "qty" integer, "size" character varying, CONSTRAINT "PK_79534adc9e0edaf32589fe26959" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_fee34414a468a992d199959116" ON "defect" ("createdAt") `,
    )
    await queryRunner.query(
      `CREATE INDEX "IDX_ba37f05b0475e088372b4b4ab0" ON "defect" ("updatedAt") `,
    )
    await queryRunner.query(
      `ALTER TABLE "agent" ADD CONSTRAINT "FK_42fbab4f08f058b1501da4690ef" FOREIGN KEY ("inChargeId") REFERENCES "in_charge"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "token" ADD CONSTRAINT "FK_94f168faad896c0786646fa3d4a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "file" ADD CONSTRAINT "FK_836ba936202b06093a920ea0557" FOREIGN KEY ("staffId") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "staff" ADD CONSTRAINT "FK_ccc6999d98a7624215a7ccaa3f8" FOREIGN KEY ("managedBy") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_c28e52f758e7bbc53828db92194" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_8f18060284824a8516b6bc325f1" FOREIGN KEY ("staffId") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_eb534ab8165663d8daefe09917c" FOREIGN KEY ("buildingId") REFERENCES "building"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_e70c458e68d7634ab5b4ef7cd0e" FOREIGN KEY ("agentId") REFERENCES "agent"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_7fb4f68175db33ef403f10a0dea" FOREIGN KEY ("managedBy") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "location" ADD CONSTRAINT "FK_8f2a33a404195b8fd5976c56e14" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "tank" ADD CONSTRAINT "FK_534522b8c10d79842c72155d38f" FOREIGN KEY ("locationId") REFERENCES "location"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "tank" ADD CONSTRAINT "FK_4f9176bd89043094d176bcc9999" FOREIGN KEY ("materialId") REFERENCES "tank_material"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "assign_staff" ADD CONSTRAINT "FK_9eb27a0984fce3cd5e3e3d9ef30" FOREIGN KEY ("staffId") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "assign_staff" ADD CONSTRAINT "FK_018fba7c8150afef967b777c986" FOREIGN KEY ("assignTaskId") REFERENCES "assign_task"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "assign_task" ADD CONSTRAINT "FK_8d8e2cec17a3b01bb23782f6eef" FOREIGN KEY ("tankId") REFERENCES "tank"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "assign_task" DROP CONSTRAINT "FK_8d8e2cec17a3b01bb23782f6eef"`,
    )
    await queryRunner.query(
      `ALTER TABLE "assign_staff" DROP CONSTRAINT "FK_018fba7c8150afef967b777c986"`,
    )
    await queryRunner.query(
      `ALTER TABLE "assign_staff" DROP CONSTRAINT "FK_9eb27a0984fce3cd5e3e3d9ef30"`,
    )
    await queryRunner.query(
      `ALTER TABLE "tank" DROP CONSTRAINT "FK_4f9176bd89043094d176bcc9999"`,
    )
    await queryRunner.query(
      `ALTER TABLE "tank" DROP CONSTRAINT "FK_534522b8c10d79842c72155d38f"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location" DROP CONSTRAINT "FK_8f2a33a404195b8fd5976c56e14"`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_7fb4f68175db33ef403f10a0dea"`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_e70c458e68d7634ab5b4ef7cd0e"`,
    )
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_eb534ab8165663d8daefe09917c"`,
    )
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_8f18060284824a8516b6bc325f1"`,
    )
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_c28e52f758e7bbc53828db92194"`,
    )
    await queryRunner.query(
      `ALTER TABLE "staff" DROP CONSTRAINT "FK_ccc6999d98a7624215a7ccaa3f8"`,
    )
    await queryRunner.query(
      `ALTER TABLE "file" DROP CONSTRAINT "FK_836ba936202b06093a920ea0557"`,
    )
    await queryRunner.query(
      `ALTER TABLE "token" DROP CONSTRAINT "FK_94f168faad896c0786646fa3d4a"`,
    )
    await queryRunner.query(
      `ALTER TABLE "agent" DROP CONSTRAINT "FK_42fbab4f08f058b1501da4690ef"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ba37f05b0475e088372b4b4ab0"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_fee34414a468a992d199959116"`,
    )
    await queryRunner.query(`DROP TABLE "defect"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b13e58c847dcabbf3b4b33e208"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_56d8af636ca6191a1ea6253065"`,
    )
    await queryRunner.query(`DROP TABLE "assign_task"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c74a49364712d79e1327489654"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d31e914d8d5a1769d8a984f35b"`,
    )
    await queryRunner.query(`DROP TABLE "assign_staff"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2e553edbe26f8156f99aa22819"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_723594b8b8fa5b704ecc16fa56"`,
    )
    await queryRunner.query(`DROP TABLE "tank"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_893196b142eb6b55705c5461b5"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_061365179c4105736445692175"`,
    )
    await queryRunner.query(`DROP TABLE "tank_material"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b21f4d902db7cc1c5db2e86d3d"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_09dee1a3d42723079bd4557791"`,
    )
    await queryRunner.query(`DROP TABLE "location"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0d1052d66fcdaf80d9587e4fb7"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6042277b62323ae370b2d79681"`,
    )
    await queryRunner.query(`DROP TABLE "customer"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_80ca6e6ef65fb9ef34ea8c90f4"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e11e649824a45d8ed01d597fd9"`,
    )
    await queryRunner.query(`DROP TABLE "user"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_824e186a844b0ca85bb8e6a14e"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3c39bd046f5e69d37f0e4fe768"`,
    )
    await queryRunner.query(`DROP TABLE "role"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e5b5b978e4e86a0ebe8b8d7de9"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e1d1cbbb31e98e16625555fc03"`,
    )
    await queryRunner.query(`DROP TABLE "staff"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d336bee718f4b96da84e8a2b1c"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_18a0ad156828b598fcef570209"`,
    )
    await queryRunner.query(`DROP TABLE "file"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_197e8aaa4fb25b28d8bcb24a7f"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_929f7d7caa0b6a61df7735a267"`,
    )
    await queryRunner.query(`DROP TABLE "token"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_536debbe8434ccbc5ebafeace1"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_04ef24a69866be541a8fe93267"`,
    )
    await queryRunner.query(`DROP TABLE "building"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b04e0edabde6f2aa86007b1237"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6a9c6c5922cc23c303e37a79f8"`,
    )
    await queryRunner.query(`DROP TABLE "agent"`)
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1b362481caffcd2074fe150987"`,
    )
    await queryRunner.query(
      `DROP INDEX "public"."IDX_abdd4e48d259667d0be07c053d"`,
    )
    await queryRunner.query(`DROP TABLE "in_charge"`)
    await queryRunner.query(`DROP TYPE "public"."file_type_enum"`)
    await queryRunner.query(`DROP TYPE "public"."staff_position_enum"`)
    await queryRunner.query(`DROP TYPE "public"."tank_type_enum"`)
    await queryRunner.query(`DROP TYPE "public"."tank_shape_enum"`)
    await queryRunner.query(`DROP TYPE "public"."defect_type_enum"`)
  }
}
