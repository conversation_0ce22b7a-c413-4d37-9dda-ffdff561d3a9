import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssignTask1711904859087 implements MigrationInterface {
    name = 'UpdateAssignTask1711904859087'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" ADD "parentId" uuid`);
        await queryRunner.query(`ALTER TABLE "assign_task" ALTER COLUMN "code" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "UQ_5a0f4c9add2d347921d1ffe9238"`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "FK_46d69db4bed1223f583019b6845" FOREIGN KEY ("parentId") REFERENCES "assign_task"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "assign_task" DROP CONSTRAINT "FK_46d69db4bed1223f583019b6845"`);
        await queryRunner.query(`ALTER TABLE "assign_task" ADD CONSTRAINT "UQ_5a0f4c9add2d347921d1ffe9238" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "assign_task" ALTER COLUMN "code" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "assign_task" DROP COLUMN "parentId"`);
    }

}
