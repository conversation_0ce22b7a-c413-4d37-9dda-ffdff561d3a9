import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTask1711914580894 implements MigrationInterface {
    name = 'UpdateTask1711914580894'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "remark"`);
        await queryRunner.query(`ALTER TABLE "task" ADD "inspectorRemark" character varying`);
        await queryRunner.query(`ALTER TABLE "task" ADD "inspectorRemarkBy" uuid`);
        await queryRunner.query(`ALTER TABLE "task" ADD CONSTRAINT "FK_7b72bcfccea3ed5f3d734adccc5" FOREIGN KEY ("inspectorRemarkBy") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_7b72bcfccea3ed5f3d734adccc5"`);
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "inspectorRemarkBy"`);
        await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "inspectorRemark"`);
        await queryRunner.query(`ALTER TABLE "task" ADD "remark" character varying`);
    }

}
