import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTaskStaff1711946738949 implements MigrationInterface {
    name = 'CreateTaskStaff1711946738949'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "task-staff" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP WITH TIME ZONE, "isDeleted" boolean DEFAULT false, "__v" integer NOT NULL DEFAULT '0', "taskId" uuid, "staffId" uuid, CONSTRAINT "PK_394d80b75962d132507a11aa3f6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_3c45cf43773a1c7374d5c70521" ON "task-staff" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_9230f946dd12b476fc334cdb3e" ON "task-staff" ("updatedAt") `);
        await queryRunner.query(`ALTER TABLE "task-staff" ADD CONSTRAINT "FK_db33b72379e1535c19c70b7fa80" FOREIGN KEY ("taskId") REFERENCES "task"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "task-staff" ADD CONSTRAINT "FK_3bc41f0724383ca077c34863d5d" FOREIGN KEY ("staffId") REFERENCES "staff"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "task-staff" DROP CONSTRAINT "FK_3bc41f0724383ca077c34863d5d"`);
        await queryRunner.query(`ALTER TABLE "task-staff" DROP CONSTRAINT "FK_db33b72379e1535c19c70b7fa80"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9230f946dd12b476fc334cdb3e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3c45cf43773a1c7374d5c70521"`);
        await queryRunner.query(`DROP TABLE "task-staff"`);
    }

}
