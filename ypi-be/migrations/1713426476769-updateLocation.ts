import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateLocation1713426476769 implements MigrationInterface {
    name = 'UpdateLocation1713426476769'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "location" ADD "lat" character varying`);
        await queryRunner.query(`ALTER TABLE "location" ADD "long" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "long"`);
        await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "lat"`);
    }

}
