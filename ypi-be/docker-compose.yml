version: '3.9'

services:
  postgres:
    image: postgres:15.2
    container_name: postgres-ypi
    ports:
      - 54321:5432
    environment:
      - POSTGRES_PASSWORD=${PG_DB_PASSWORD}
      - POSTGRES_USER=${PG_DB_USER}
      - POSTGRES_DB=${PG_DB_NAME}
  redis:
    image: 'redis:7.2.3'
    # user: 'root'
    # restart: unless-stopped
    container_name: redis-ypi
    # command: redis-server /etc/redis/redis.conf --port ${REDIS_PORT} --requirepass ${REDIS_PASSWORD}
    env_file:
      - .env
    ports:
      - '6380:${REDIS_PORT}' # Another port that can be used to connect to Redis from outside
    # volumes:
    #   - ./redis-data:/data
    #   - ./redis.conf:/etc/redis/redis.conf
    # logging:
    #   options:
    #     max-size: 50m
    # networks:
    #   app_network:
  # ypi-backend:
  #   depends_on:
  #     - redis
  #     - postgres
  #   build:
  #     context: ./
  #     dockerfile: ./Dockerfile
  #   logging:
  #     options:
  #       max-size: '5m'
  #   restart: unless-stopped
  #   volumes:
  #     - ./:/usr/src/app:cached
  #   ports:
  #     - 3003:3000
  #   env_file:
  #     - .env
# networks:
#   app_network:
# volumes:
#   data-redis:
