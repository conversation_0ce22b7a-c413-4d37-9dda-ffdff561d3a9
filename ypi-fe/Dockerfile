# Stage 1: Build the application
FROM node:22-alpine AS builder

# Install dependencies for canvas module
RUN apk add --no-cache build-base cairo-dev jpeg-dev pango-dev giflib-dev

# Set working directory
WORKDIR /app

# Copy project files
COPY . .


# Install dependencies
RUN yarn install

# Ensure panda config file is present
ARG GOOGLE_MAPS_API_KEY 

RUN if [ ! -f panda.config.ts ]; then panda init; fi

ENV APP_HOSTNAME "https://app.carelaplus.com"

ENV GOOGLE_MAPS_API_KEY=$GOOGLE_MAPS_API_KEY
# Build the Next.js app
RUN yarn build

# Stage 2: Create the final image
FROM node:22-alpine

# Set working directory
WORKDIR /app

# Copy only the necessary files from the builder stage

COPY --from=builder /app/.next/standalone /app
COPY --from=builder /app/.next/static /app/.next/static

# Expose the port the app runs on
EXPOSE 3000



# Start the Next.js app
CMD ["node", "server.js"]