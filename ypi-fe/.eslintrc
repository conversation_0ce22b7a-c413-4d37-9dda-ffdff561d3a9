{"parser": "@typescript-eslint/parser", "extends": ["next", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "import/no-anonymous-default-export": "off", "react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "off", "react/display-name": "off", "@next/next/no-img-element": "off"}, "ignorePatterns": ["src/styled-system**/*"]}