import { defineConfig } from '@pandacss/dev';
import colors from './panda/config/colors.panda';
import spacing from './panda/config/spacing.panda';
import fontSizes from './panda/config/fontSizes.panda';
import globalPanda from 'panda/config/global.panda';
import keyframesPanda from 'panda/config/keyframes.panda';

export default defineConfig({
    // Whether to use css reset
    preflight: true,
    jsxFramework: 'react',
    presets: ['@pandacss/preset-base', '@pandacss/preset-panda'],
    prefix: 'ypi',
    include: ['./src/**/*.{js,jsx,ts,tsx}'],
    gitignore: true,
    clean: true,
    optimize: process.env.NODE_ENV === 'production',
    minify: process.env.NODE_ENV === 'production',
    watch: process.env.NODE_ENV === 'development',
    conditions: { hover: '&:hover' },
    exclude: [],
    globalCss: globalPanda,
    theme: {
        extend: {
            keyframes: keyframesPanda,
        },
        tokens: {
            colors,
            fontSizes,
            shadows: {
                primary: {
                    value: '1px 4px 3.5999999046325684px 0px #00000026',
                },
                secondary: {
                    value: '0px 1px 10px 0px #00000026',
                },
            },
            fontWeights: {
                bold: {
                    value: 700,
                },
                semiBold: {
                    value: 600,
                },
                regular: {
                    value: 400,
                },
            },
            spacing: spacing,
        },
    },

    // The output directory for your css system
    outdir: 'src/styled-system',
});
