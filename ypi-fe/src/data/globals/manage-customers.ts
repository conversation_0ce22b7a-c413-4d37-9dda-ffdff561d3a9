export interface BuildingCategoriesType {
    id: string;
    name: string;
}

export const DataBuildingCategories:BuildingCategoriesType[] = [
    {
        id: 'Commercial Building',
        name: 'Commercial Building',
    },
    {
        id: 'Condominum / MCST/Block',
        name: 'Condominum / MCST/Block ',
    },
    {
        id: 'Industry',
        name: 'Industry',
    },
    {
        id: 'Hospital / Homes / Medical Facilities ',
        name: 'Hospital / Homes / Medical Facilities ',
    },
    {
        id: 'School',
        name: 'School ',
    },
    {
        id: 'HDB Street & Block',
        name: 'HDB Street & Block',
    },
    {
        id: 'Private apartment or houses',
        name: 'Private apartment or houses',
    },
]


export const DataInChargeCategory:BuildingCategoriesType[] = [
    {
        id: 'Town council',
        name: 'Town council',
    },
    {
        id: 'Term Contractor',
        name: 'Term Contractor',
    },
    {
        id: 'MA',
        name: 'MA',
    },
]