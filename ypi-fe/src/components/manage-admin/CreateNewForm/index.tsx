import { zodResolver } from '@hookform/resolvers/zod';
import { FieldValues } from 'react-hook-form';
import { createNewAdminSchema, updateAdminSchema } from './validation';
import { INewUserParams, EUserRole } from '@/utilities/types/entities/user';
import InputForm, { IInputFormProps } from '@/components/globals/InputForm';
import { getCreateNewOptions, getUpdateOptions } from './options';

interface ICreateFormProps extends Partial<IInputFormProps> {
    isLoading: boolean;
    onSubmit: (data: Partial<INewUserParams>) => void;
    initValue?: Partial<INewUserParams>;
    isEdit?: boolean;
}

export default function CreateNewForm(props: ICreateFormProps) {
    const onSubmit = (data: FieldValues) => {
        props.onSubmit({
            avatarUrl: data?.avatar || '',
            email: data.email,
            password: data.password,
            roleType: EUserRole.Admin,
            company: data.company,
            fullName: data.fullName,
            phoneCode: '+65',
            phoneNumber: data.phoneNumber,
        });
    };

    return (
        <InputForm
            {...props}
            inputs={
                props.isEdit
                    ? getUpdateOptions(props.initValue)
                    : getCreateNewOptions()
            }
            onSubmit={onSubmit}
            disabled={props.isLoading}
            defaultValues={props.initValue}
            resolver={zodResolver(
                props.isEdit ? updateAdminSchema : createNewAdminSchema
            )}
            submitText={props.isEdit ? 'Update' : 'Create'}
        />
    );
}
