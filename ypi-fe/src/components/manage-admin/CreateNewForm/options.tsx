import { css } from '@/styled-system/css';
import { INewUserParams } from '@/utilities/types/entities/user';
import { EFormInputType, TFormInputOption } from '@/utilities/types/form';
import { formAvatar } from './style';

export function getCreateNewOptions(): TFormInputOption[] {
    return [
        {
            id: 'avatar',
            name: 'avatar',
            type: EFormInputType.Avatar,
            className: formAvatar,
        },
        {
            id: 'fullName',
            name: 'fullName',
            label: 'Full name',
            type: EFormInputType.Text,
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'company',
            name: 'company',
            label: 'Company',
            type: EFormInputType.Text,
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'phoneNumber',
            name: 'phoneNumber',
            label: 'Phone',
            type: EFormInputType.Phone,
            phoneCode: '+65',
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'email',
            name: 'email',
            label: 'Email',
            type: EFormInputType.Email,
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'password',
            name: 'password',
            label: 'Password',
            type: EFormInputType.Password,
            autoGeneratePassword: true,
            variant: 'contained',
            labelPosition: 'left',
        },
    ];
}

export function getUpdateOptions(
    defaultValues?: Partial<INewUserParams>
): TFormInputOption[] {
    return [
        {
            id: 'avatar',
            name: 'avatar',
            type: EFormInputType.Avatar,
            className: formAvatar,
            // defaultValue: defaultValues?.avatar ?? '',
        },
        {
            id: 'fullName',
            name: 'fullName',
            label: 'Full name',
            type: EFormInputType.Text,
            defaultValue: defaultValues?.fullName ?? '',
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'company',
            name: 'company',
            label: 'Company',
            type: EFormInputType.Text,
            defaultValue: defaultValues?.company ?? '',
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'phoneNumber',
            name: 'phoneNumber',
            label: 'Phone',
            type: EFormInputType.Phone,
            defaultValue: defaultValues?.phoneNumber ?? '',
            phoneCode: '+65',
            variant: 'contained',
            labelPosition: 'left',
        },
        {
            id: 'email',
            name: 'email',
            label: 'Email',
            type: EFormInputType.Email,
            defaultValue: defaultValues?.email ?? '',
            readOnly: true,
            variant: 'contained',
            labelPosition: 'left',
        },
    ];
}
