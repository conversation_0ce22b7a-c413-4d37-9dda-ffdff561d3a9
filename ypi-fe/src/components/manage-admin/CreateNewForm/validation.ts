// import { isStrongPassword } from '@/utilities/globals/function/password';
import { phoneValidator } from '@/utilities/validations';
import { z } from 'zod';

export const createNewAdminSchema = z.object({
    email: z.string().email('Invalid email address'),
    fullName: z.string().min(1, 'This field cannot be empty'),
    company: z.string().min(1, 'This field cannot be empty'),
    phoneNumber: z.string().refine(phoneValidator, 'Invalid phone number'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    avatar: z.string().optional(),
});

export const updateAdminSchema = z.object({
    email: z.string().email('Invalid email address'),
    fullName: z.string().min(1, 'This field cannot be empty'),
    company: z.string().min(1, 'This field cannot be empty'),
    phoneNumber: z.string().refine(phoneValidator, 'Invalid phone number'),
    avatar: z.string().optional(),
});
