import Table, { IColProps } from '@/components/globals/Table';

import Button from '@/components/globals/Button';
import { IUser } from '@/utilities/types/entities/user';

interface UserListProps {
    onItemEdit: (item: IUser) => void;
    // onItemDelete: (item: IUser) => void;
    data: IUser[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function UserList(props: UserListProps) {
    const columns: IColProps<IUser>[] = [
        {
            dataKey: 'fullName',
            title: 'Name',
            key: 'fullName',
            align: 'left',
        },
        {
            dataKey: 'company',
            title: 'Company',
            key: 'company',
            align: 'left',
        },
        {
            dataKey: 'email',
            title: 'Email',
            key: 'email',
            align: 'left',
        },
        {
            dataKey: 'phone_number',
            title: 'Phone number',
            key: 'phone_number',
            align: 'left',
            render: (data) =>
                data.phoneNumber ? `+65 ${data.phoneNumber}` : '',
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            render: (row) => {
                //
                return (
                    <div>
                        <Button
                            styles="text"
                            visual="link_primary"
                            size="auto"
                            onClick={() => props.onItemEdit?.(row)}
                        >
                            Edit
                        </Button>
                        {/* <Button
                            styles="text"
                            visual="link_error"
                            size="auto"
                            onClick={() => props.onItemDelete?.(row)}
                        >
                            Delete
                        </Button> */}
                    </div>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={props.data}
            loading={props.loading}
            columns={columns}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
        />
    );
}
