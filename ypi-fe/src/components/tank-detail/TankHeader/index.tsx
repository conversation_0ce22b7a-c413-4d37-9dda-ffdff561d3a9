import { containerStyle, expandedStyle } from './style';
import React from 'react';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { Edit } from '@/components/globals/Icons';
import Button from '@/components/globals/Button';
import Delete from '@/components/globals/Icons/Delete';
import { flex } from '@/styled-system/patterns';
import { ITank } from '@/utilities/types/entities/tank';
import {
    getTankMaterialConfig,
    getTankShapeConfig,
    getTankTypeConfig,
} from '@/utilities/helpers/tank';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { InfoItem } from '@/components/manage-customers/InfoItem';

type Props = {
    tank?: ITank;
    onEdit?: () => void;
    onDelete?: () => void;
};

export default function TankHeader({ tank, onEdit, onDelete }: Props) {
    const tankTypeInfo = tank ? getTankTypeConfig(tank.type) : undefined;
    const meterialText =
        typeof tank?.material === 'string'
            ? tank?.material
            : tank?.material?.name;

    const tankMaterialInfo = tank
        ? getTankMaterialConfig(meterialText || '')
        : undefined;
    const tankShapeInfo = tank ? getTankShapeConfig(tank.shape) : undefined;
    return (
        <div className={containerStyle}>
            <InfoItem
                className={css({ w: 120 })}
                label="Tank type"
                value={tankTypeInfo?.shortLabel ?? tank?.type}
                valueColor={tankTypeInfo?.color}
            />
            <InfoItem
                className={css({ w: 98 })}
                label="Material"
                value={tankMaterialInfo?.label ?? meterialText}
            />
            <InfoItem
                className={css({ w: 98 })}
                label="Shape"
                value={tankShapeInfo?.label ?? tank?.shape}
            />
            <InfoItem
                className={css({ flex: 1 })}
                label="Tank dimensions"
                renderValue={() => (
                    <Typography
                        className={flex({ gap: '6px' })}
                        tag={ETypographyTag.div}
                        typography="subtitle_15"
                        color="secondary_100"
                    >
                        <div className={expandedStyle}>L: {tank?.length}</div>
                        <div className={expandedStyle}>W: {tank?.width}</div>
                        <div className={expandedStyle}>H: {tank?.height}</div>
                    </Typography>
                )}
            />
            <InfoItem
                className={css({ w: 98 })}
                label="EC"
                value={tank?.effectiveCap}
            />
            <InfoItem
                className={css({ w: 98 })}
                label="Floor level"
                value={tank?.floorLevel}
            />
            <InfoItem
                className={css({ w: 98 })}
                label="Water saved"
                value={tank?.waterSaved}
            />
            <div className={flex({ gap: '18px' })}>
                <Button
                    onClick={onEdit}
                    size="icon_normal"
                    visual="outline_icon"
                >
                    <Edit />
                </Button>
                <Button
                    onClick={onDelete}
                    size="icon_normal"
                    visual="solid_icon"
                >
                    <Delete />
                </Button>
            </div>
        </div>
    );
}
