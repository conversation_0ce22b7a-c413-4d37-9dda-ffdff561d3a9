import { useEffect, useRef, useState } from 'react';
import { taleStyles } from './style';
import ImageGrid from '@/components/globals/ImageGrid';
import ChevronDown from '@/components/globals/Icons/ChevronDown';
import Typography from '@/components/globals/Typography';
import AnimateHeight from 'react-animate-height';
import { ITask } from '@/utilities/types/entities/task';
import dayjs from 'dayjs';
import TableEmpty from '@/components/globals/Table/TableEmpty';
import TableLoading from '@/components/globals/Table/TableLoading';

type TDisinfectionTableProps = {
    tasks: ITask[];
    completedDate: string;
};

export default function DisinfectionTable({
    tasks,
    completedDate,
}: TDisinfectionTableProps) {
    const tableRef = useRef<HTMLTableElement | null>(null);
    const [collapsed, setCollapsed] = useState(true);
    const [height, setHeight] = useState<'auto' | number>('auto');
    const classNames = taleStyles({ collapsed });

    useEffect(() => {
        let t: number | undefined;
        function updateHeight() {
            if (t) {
                cancelAnimationFrame(t);
            }
            t = requestAnimationFrame(() => {
                const height = tableRef.current?.getBoundingClientRect().height;
                setHeight((height ?? 0) + 48);
            });
        }
        updateHeight();
        window.addEventListener('resize', updateHeight);

        return () => {
            if (t) {
                cancelAnimationFrame(t);
            }
            window.removeEventListener('resize', updateHeight);
        };
    }, [collapsed]);

    return (
        <div className={classNames.root}>
            <AnimateHeight
                contentClassName={classNames.container}
                height={height}
                duration={300}
            >
                <table className={classNames.table} ref={tableRef}>
                    <thead className={classNames.thead}>
                        <tr>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Day complete
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Lab sample taken
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Defects
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Complete photo
                                </Typography>
                            </th>
                        </tr>
                    </thead>
                    <tbody className={classNames.tbody}>
                        {tasks.map((item, index) => (
                            <tr key={`${item?.id}_${index}`}>
                                {index === 0 ? (
                                    <>
                                        <td width={170} rowSpan={tasks?.length}>
                                            {completedDate
                                                ? dayjs(completedDate).format(
                                                      'DD/MM/YYYY'
                                                  )
                                                : ''}
                                        </td>
                                        <td width={170} rowSpan={tasks?.length}>
                                            {/* {null
                                                ? dayjs(item?.createdAt).format(
                                                      'DD/MM/YYYY'
                                                  )
                                                : ''} */}
                                        </td>
                                    </>
                                ) : null}
                                <td>{`${index + 1}.  ${item?.title}`}</td>
                                <td width={200}>
                                    <ImageGrid
                                        photos={
                                            item?.images?.map((image) => ({
                                                id: image?.id || '',
                                                url: image?.link || '',
                                                key: image?.name || '',
                                                link: image?.link || '',
                                            })) || []
                                        }
                                        visual="inline"
                                    />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
                <div className={classNames.footerPlaceholder} />
                <div
                    className={classNames.footer}
                    onClick={() => {
                        setCollapsed(!collapsed);
                    }}
                >
                    {collapsed ? 'View more' : 'View less'}
                    <ChevronDown />
                </div>
            </AnimateHeight>
        </div>
    );
}
