import { sva } from '@/styled-system/css';

export const taleStyles = sva({
    slots: [
        'root',
        'container',
        'table',
        'thead',
        'tbody',
        'footer',
        'footerPlaceholder',
    ],
    base: {
        root: {
            mt: 3,
        },
        container: {
            overflow: 'hidden',
            borderRadius: 10,
            backgroundColor: 'background.40',
            height: '100%',
            position: 'relative',
            fontSize: 'body.15',
        },
        table: {
            width: '100%',
            '& td, th': {
                p: '12px 16px',
            },
            '& th': {
                verticalAlign: 'center',
            },
            '& td': {
                verticalAlign: 'top',
            },
        },
        thead: {
            borderBottomWidth: 1,
            borderBottomColor: 'border.normal',
            borderBottomStyle: 'solid',
        },
        tbody: {
            '& tr:not(:last-child)': {
                borderBottomWidth: 1,
                borderBottomColor: 'border.field',
                borderBottomStyle: 'solid',
            },
        },
        footer: {
            height: 48,
            borderTopWidth: 1,
            borderTopColor: 'border.normal',
            borderTopStyle: 'solid',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'background.40',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            cursor: 'pointer',
            color: 'primary.100',
            '& svg': {
                transition: 'rotate 300ms ease-in-out',
            },
        },
    },
    variants: {
        collapsed: {
            true: {
                tbody: {
                    '& tr:not(:first-child, :nth-child(2))': {
                        display: 'none',
                    },
                },
            },
            false: {
                footer: {
                    '& svg': {
                        rotate: '180deg',
                    },
                },
            },
        },
    },
});
