import ChevronRight from '@/components/globals/Icons/ChevronRight';
import Table, { IColProps } from '@/components/globals/Table';
import Typography from '@/components/globals/Typography';
import { ExpandableIconWrapper } from './style';
import { useEffect, useRef, useState } from 'react';
import Collapsible from '@/components/globals/Collapsible';
import { IAssignTask, ITask } from '@/utilities/types/entities/task';
import dayjs from 'dayjs';
import { FORMAT } from '@/configs/date';
import { flex } from '@/styled-system/patterns';
import ImageGrid from '@/components/globals/ImageGrid';

interface Props {
    data: IAssignTask[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function RemarkTaskList(props: Props) {
    const [expandedItems, setExpandedItem] = useState<string[]>([]);

    const columns: IColProps<IAssignTask>[] = [
        {
            dataKey: 'day',
            title: 'Day',
            key: 'day',
            align: 'left',
            valign: 'top',
            width: 130,
            render: (row) => {
                return (
                    <Typography typography="body_15" color="secondary_100">
                        {row?.inspectorSubmittedDate
                            ? dayjs(row.inspectorSubmittedDate).format(
                                  FORMAT.DATE_PICKER
                              )
                            : ''}
                    </Typography>
                );
            },
        },
        {
            dataKey: 'inspector',
            title: 'Inspector',
            key: 'inspector',
            align: 'left',
            valign: 'top',
            width: 160,
            render: (row) => {
                return (
                    <Typography typography="body_15" color="secondary_100">
                        {row?.remarkBy?.fullName}
                    </Typography>
                );
            },
        },
        {
            dataKey: 'remark',
            title: 'Remark',
            key: 'remark',
            align: 'left',
            valign: 'top',
            render: (row: IAssignTask) => {
                const isExpanded = expandedItems.includes(row.id);
                return (
                    <div
                        className={flex({
                            flexDirection: 'column',
                            gap: 1,
                        })}
                    >
                        {row?.tasks.slice(0, 1).map((task, index) => (
                            <div
                                key={task.id}
                                className={flex({
                                    flexDirection: 'column',
                                    gap: 1,
                                })}
                            >
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    {index + 1}. {task?.title}
                                </Typography>
                                <Typography
                                    typography="body_15"
                                    color="secondary_100"
                                >
                                    {task?.inspectorRemark}
                                </Typography>

                                <div>
                                    <ImageGrid
                                        photos={
                                            task?.inspectorRemarkImages?.map(
                                                (image) => ({
                                                    id: image?.id || '',
                                                    url: image?.link || '',
                                                    key: image?.name || '',
                                                    link: image?.link || '',
                                                })
                                            ) || []
                                        }
                                        visual="inline"
                                    />
                                </div>
                            </div>
                        ))}
                        <RemarkColumn
                            collapsed={!isExpanded}
                            data={row?.tasks || []}
                        />
                    </div>
                );
            },
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: 64,
            render: (row) => {
                const isExpanded = expandedItems.includes(row.id);
                return (
                    <ExpandableIconWrapper
                        expanded={isExpanded}
                        onClick={() => {
                            if (isExpanded) {
                                setExpandedItem(
                                    expandedItems.filter((id) => id !== row.id)
                                );
                                return;
                            }
                            setExpandedItem([...expandedItems, row.id]);
                        }}
                    >
                        <ChevronRight />
                    </ExpandableIconWrapper>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={props.data}
            margin="dense"
            loading={props.loading}
            columns={columns}
            rowKey={(row) => row.id}
            layoutFixed
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
        />
    );
}

function RemarkColumn({
    data,
    collapsed,
}: {
    data: ITask[];
    collapsed: boolean;
}) {
    const [clamped, setClamped] = useState(collapsed);
    const ref = useRef({ collapsed: true }).current;
    if (!collapsed) {
        ref.collapsed = collapsed;
    }

    useEffect(() => {
        if (!collapsed) {
            setClamped(false);
        }
    }, [collapsed]);

    return (
        <>
            <Collapsible
                onCollapsed={() => {
                    ref.collapsed = true;
                    setClamped(true);
                }}
                collapsed={collapsed}
            >
                <div
                    className={flex({
                        flexDirection: 'column',
                        gap: 1,
                    })}
                >
                    {data.slice(1, data.length).map((task, index) => (
                        <div
                            key={task.id}
                            className={flex({
                                flexDirection: 'column',
                                gap: 1,
                            })}
                        >
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                {index + 2}. {task?.title}
                            </Typography>
                            <Typography
                                typography="body_15"
                                color="secondary_100"
                            >
                                {task?.inspectorRemark}
                            </Typography>

                            <div>
                                <ImageGrid
                                    photos={
                                        task?.inspectorRemarkImages?.map(
                                            (image) => ({
                                                id: image?.id || '',
                                                url: image?.link || '',
                                                key: image?.name || '',
                                                link: image?.link || '',
                                            })
                                        ) || []
                                    }
                                    visual="inline"
                                />
                            </div>
                        </div>
                    ))}
                </div>
            </Collapsible>
        </>
    );
}
