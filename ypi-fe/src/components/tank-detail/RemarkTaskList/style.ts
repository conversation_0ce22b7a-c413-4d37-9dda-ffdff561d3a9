import { styled } from '@/styled-system/jsx';

export const ExpandableIconWrapper = styled('span', {
    base: {
        display: 'flex',
        position: 'absolute',
        top: '9px',
        bottom: '9px',
        left: 0,
        right: 0,
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        '& svg': {
            alignItems: 'center',
            rotate: '90deg',
            transition: 'rotate 300ms ease-in-out',
        },
    },
    variants: {
        expanded: {
            true: {
                '& svg': {
                    rotate: '-90deg',
                },
            },
        },
    },
});

export const RemarkText = styled('div', {
    // base: {
    //     transition: 'line-clamp 300ms linear',
    //     transitionDelay: '300ms',
    // },
    variants: {
        clamped: {
            true: {
                overflow: 'hidden',
                display: '-webkit-box',
                WebkitLineClamp: 3,
                lineClamp: 3,
                '-webkit-box-orient': 'vertical',
            },
        },
    },
});
