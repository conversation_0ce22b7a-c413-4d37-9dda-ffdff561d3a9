import Typography from '@/components/globals/Typography';
import { styles } from './style';
import { ILabReport } from '@/utilities/types/entities/lab-report';
import dayjs from 'dayjs';
import { FORMAT } from '@/configs/date';

type Props = {
    labReports: ILabReport[];
};

export default function LabReportDetail({ labReports }: Props) {
    const classNames = styles({});

    return (
        <div className={classNames.container}>
            <table className={classNames.table}>
                <thead>
                    <tr>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Date of report
                            </Typography>
                        </th>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Report results
                            </Typography>
                        </th>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Report
                            </Typography>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {labReports.map((labReport) => (
                        <tr key={labReport.id}>
                            <td width={141}>
                                {labReport.createdAt
                                    ? dayjs(labReport.createdAt).format(
                                          FORMAT.DATE_PICKER
                                      )
                                    : ''}
                            </td>
                            <td width={200}>{labReport?.name}</td>
                            <td>{labReport.link}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
