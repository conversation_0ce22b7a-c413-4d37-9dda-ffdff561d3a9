import { css, sva } from '@/styled-system/css';
import { box } from '@/styled-system/patterns';

export const styles = sva({
    slots: ['container', 'table', 'signatureImage'],
    base: {
        container: {
            overflow: 'hidden',
            borderRadius: 10,
            backgroundColor: 'background.40',
            position: 'relative',
            fontSize: 'body.15',
            mt: 3,
        },
        table: {
            width: '100%',
            '& th, td': {
                p: '12px 16px',
            },
        },
        signatureImage: {
            height: 36,
            borderRadius: 5,
        },
    },
});

export const signatureImgStyle = box({
    width: '120px',
    height: '60px',
    borderRadius: '10px',
});
