import Typography from '@/components/globals/Typography';
import { styles, signatureImgStyle } from './style';
import dayjs from 'dayjs';

interface CustomerJobSatisfiedProps {
    signatureDate: string;
    customerName: string;
    signatureImg: string;
}

export default function CustomerJobSatisfied({
    signatureDate,
    customerName,
    signatureImg,
}: CustomerJobSatisfiedProps) {
    const classNames = styles({});

    return (
        <div className={classNames.container}>
            <table className={classNames.table}>
                <thead>
                    <tr>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Job inspect complete
                            </Typography>
                        </th>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Satisfied signed
                            </Typography>
                        </th>
                        <th align="right">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Signature
                            </Typography>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td width={134}>
                            {signatureDate
                                ? dayjs(signatureDate).format('DD/MM/YYYY')
                                : ''}
                        </td>
                        <td>{customerName}</td>
                        <td align="right">
                            <img
                                src={signatureImg}
                                className={signatureImgStyle}
                                alt={signatureImg}
                            />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
}
