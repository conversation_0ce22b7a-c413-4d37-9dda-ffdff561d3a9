import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const rootStyle = css({
    width: 296,
    mt: 3,
    mb: 3,
    position: 'relative',
});

export const containerStyle = flex({
    direction: 'column',
    position: 'absolute',
    left: 0,
    top: 0,
    right: 0,
    maxHeight: '100%',
    backgroundColor: 'background.40',
    borderRadius: 10,
    overflow: 'auto',
});

export const headerContainerStyle = css({
    pt: 3,
    pl: 3,
    pr: 3,
    position: 'sticky',
    top: 0,
    backgroundColor: 'background.40',
});

export const footerContainerStyle = css({
    pl: 3,
    pr: 3,
    pb: 3,
    pt: 6,
    display: 'flex',
    justifyContent: 'center',
    position: 'sticky',
    bottom: 0,
    backgroundColor: 'background.40',
});

export const contentContainerStyle = css({
    p: 3,
    gap: 3,
    display: 'flex',
    flexDirection: 'column',
});

export const fileContainerStyle = flex({
    borderRadius: 10,
    backgroundColor: 'white',
    padding: '8px 12px',
    mt: 1,
});

export const filenameStyle = css({
    flex: 1,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    cursor: 'pointer',
});
