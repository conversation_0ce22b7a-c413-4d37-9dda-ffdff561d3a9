import Typography from '@/components/globals/Typography';
import {
    containerStyle,
    contentContainerStyle,
    fileContainerStyle,
    filenameStyle,
    footerContainerStyle,
    headerContainerStyle,
    rootStyle,
} from './style';
import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Button from '@/components/globals/Button';
import { ILabReport } from '@/utilities/types/entities/lab-report';
import { Close } from '@/components/globals/Icons';
import IconButton from '@/components/globals/IconButton';
import Upload from '@/components/globals/Icons/Upload';
import dayjs from 'dayjs';
import { FORMAT } from '@/configs/date';
import { ChangeEventHandler, useEffect, useState } from 'react';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';
import { useDeleteFile } from '@/hooks/upload';
import { beforeUpload } from '@/components/globals/InputFile/inputFileHelper';
import { request } from '@/utilities/request/client';
import { IResponseData } from '@/utilities/types/request';
import { EUploadPath } from '@/utilities/types/enums/Upload';
import toast from 'react-hot-toast';
import { box, flex } from '@/styled-system/patterns';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import DeleteIcon from '@/components/globals/Icons/DeleteIcon';

import '@cyntler/react-doc-viewer/dist/index.css';
import PreviewFileModal from '@/components/globals/PreviewFileModal';
import { handleDownload } from '@/utilities/globals/function/download';
type Props = {
    tankId: string;
    reports: ILabReport[];
};

type ItemProps = {
    report: ILabReport;
    onDelete: (id: string) => void;
};

export default function LabReportPane({ reports, tankId }: Props) {
    const [localReports, setLocalReports] = useState<ILabReport[]>([]);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const deleteFile = useDeleteFile();
    const [confirmDelete, setConfirmDelete] = useState<{
        isOpen: boolean;
        id: string;
    }>({ isOpen: false, id: '' });

    useEffect(() => {
        setLocalReports([...(reports || [])]);
    }, [reports]);

    const onDelete = async (id: string) => {
        setConfirmDelete({ isOpen: true, id });
    };

    const onConfirmDelete = async () => {
        deleteFile(confirmDelete.id)
            .then((res) => {
                setLocalReports(
                    localReports.filter((item) => item.id !== confirmDelete.id)
                );
                setConfirmDelete((pre) => ({ ...pre, isOpen: false, id: '' }));
            })
            .catch(() => {});
    };

    const onCancelDelete = () => {
        setConfirmDelete((pre) => ({ ...pre, isOpen: false, id: '' }));
    };
    const handleChange: ChangeEventHandler<HTMLInputElement> = async (
        event
    ) => {
        const files = event.target.files;
        if (!files || files?.length == 0) return;
        const isValid = beforeUpload(files, ['.pdf']);
        setIsUploading(true);
        if (!isValid) {
            setIsUploading(false);
            return;
        }
        const data = new FormData();

        Array.from(files).forEach((file) => {
            data.append('labReports', file, file.name);
        });
        data.append('tankId', tankId);

        const result = await request<IResponseData<ILabReport[]>>({
            url: EUploadPath.PATH,
            method: 'POST',
            data,
            headers: {
                'Content-Type': 'multipart/form-data', // Important: Set content type to multipart/form-data
            },
        });

        setIsUploading(false);
        if (Array.isArray(result?.data)) {
            setLocalReports([...localReports, ...(result?.data || [])]);
            toast.success('Upload successful');
        } else {
            toast.error('Upload failed');
        }
    };
    return (
        <>
            <div className={rootStyle}>
                <div className={containerStyle}>
                    <div className={headerContainerStyle}>
                        <Typography
                            typography="subtitle_24"
                            tag={ETypographyTag.h2}
                            color="primary_100"
                        >
                            Lab reports
                        </Typography>
                        <Typography
                            typography="body_16"
                            className={css({ mt: '4px' })}
                            tag={ETypographyTag.div}
                            color="secondary_100"
                        >
                            (3 years records)
                        </Typography>
                    </div>
                    {localReports.length > 0 && (
                        <div className={contentContainerStyle}>
                            {localReports.map((item) => (
                                <ReportItem
                                    key={item.id}
                                    report={item}
                                    onDelete={onDelete}
                                />
                            ))}
                        </div>
                    )}
                    <div className={footerContainerStyle}>
                        <Button
                            className={css({ width: 200 })}
                            visual="solid_primary"
                            htmlFor="labReportFileInput"
                            disabled={isUploading}
                        >
                            <input
                                hidden
                                className={css({
                                    display: 'none',
                                    position: 'absolute',
                                    w: 0,
                                    h: 0,
                                })}
                                type="file"
                                accept=".pdf,.doc,.docx,application/msword"
                                name="labReportFileInput"
                                id="labReportFileInput"
                                onChange={handleChange}
                            />
                            <Upload /> {isUploading ? 'Uploading...' : 'Upload'}
                        </Button>
                    </div>
                </div>
            </div>
            <ConfirmModal
                title="Alert"
                open={confirmDelete.isOpen}
                confirmText="Delete"
                message={format(CommonMessages.DeletingAlert, 'lab reports')}
                onCancel={onCancelDelete}
                onConfirm={onConfirmDelete}
            />
        </>
    );
}

function ReportItem({ report, onDelete }: ItemProps) {
    const [pdfModal, setPdfModal] = useState(false);

    return (
        <div>
            <div
                className={flex({
                    justifyContent: 'space-between',
                    alignItems: 'center',
                })}
            >
                <Typography typography="body_14" color="body_1">
                    · {dayjs(report.createdAt).format(FORMAT.DATE_PICKER)}
                </Typography>
                <div
                    className={flex({
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        gap: '12px',
                    })}
                >
                    <button
                        type="button"
                        onClick={async () => {
                            await handleDownload(report?.link, report?.name);
                        }}
                    >
                        <DownloadIcon
                            className={box({
                                cursor: 'pointer',
                                color: 'primary.100',
                            })}
                        />
                    </button>
                    <DeleteIcon
                        onClick={() => onDelete(report.id)}
                        className={box({
                            cursor: 'pointer',
                            color: 'warning.100',
                        })}
                    />
                </div>
            </div>
            <div className={fileContainerStyle}>
                <div
                    className={filenameStyle}
                    onClick={() => {
                        setPdfModal(true);
                    }}
                >
                    {report.name}
                </div>
            </div>

            <PreviewFileModal
                open={pdfModal}
                onClose={() => {
                    setPdfModal(false);
                }}
                documents={[
                    {
                        uri: report.link,
                        fileType: report.mineType,
                        fileName: report.name,
                    },
                ]}
            />
        </div>
    );
}
