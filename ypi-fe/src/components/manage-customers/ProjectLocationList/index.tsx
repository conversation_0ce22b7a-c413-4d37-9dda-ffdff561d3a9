import Table, { IColProps } from '@/components/globals/Table';
import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { IProjectLocation } from '@/utilities/types/entities/project-location';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter } from 'next/navigation';
import { css } from '@/styled-system/css';

interface IProjectLocationListProps {
    customerId: string;
    data: IProjectLocation[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function ProjectLocationList(props: IProjectLocationListProps) {
    const router = useRouter();
    const columns: IColProps<IProjectLocation>[] = [
        {
            dataKey: 'postalCode',
            title: 'Postal Code',
            key: 'postalCode',
            align: 'left',
            width: 150,
        },
        // {
        //     dataKey: 'blockNo',
        //     title: 'Block no',
        //     key: 'blockNo',
        //     align: 'left',
        //     width: 140,
        // },
        {
            dataKey: 'street',
            title: 'Street name',
            key: 'street',
            align: 'left',
            width: 200,
            render: (data: IProjectLocation) => (
                <div
                    className={css({
                        width: '200px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.street}
                </div>
            ),
        },

        {
            dataKey: 'building',
            title: 'Building name',
            key: 'building',
            align: 'left',
            width: 200,
            render: (data: IProjectLocation) => (
                <div
                    className={css({
                        width: '200px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.building}
                </div>
            ),
        },

        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: 32,
            render: (row) => {
                //
                return <ChevronRight />;
            },
        },
    ];
    const handleClick = (rowData: any) => {
        router.push(
            `${Slug.MANAGE_CUSTOMERS}/${props.customerId}/location/${rowData.id}`
        );
    };
    return (
        <Table
            loading={props.loading}
            dataSource={props.data}
            columns={columns}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
