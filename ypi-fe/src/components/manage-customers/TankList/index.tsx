import Table, { IColProps } from '@/components/globals/Table';
import {
    getTankShapeConfig,
    getTankTypeConfig,
} from '@/utilities/helpers/tank';

import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { DimensionTag } from './style';
import { ETankShape } from '@/utilities/types/enums/Tank';
import { ITank } from '@/utilities/types/entities/tank';
import { Slug } from '@/utilities/types/enums/Slug';
import Typography from '@/components/globals/Typography';
import { flex } from '@/styled-system/patterns';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { css } from '@/styled-system/css';

interface Props {
    customerId: string;
    data: ITank[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function TankList(props: Props) {
    const router = useRouter();

    const pathname = usePathname();

    const columns: IColProps<ITank>[] = [
        {
            dataKey: 'code',
            title: 'Tank ID',
            key: 'code',
            align: 'left',
            render: (data: ITank) => (
                <div
                    className={css({
                        width: '90px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.code}
                </div>
            ),
        },
        {
            dataKey: 'type',
            title: 'Tank type',
            key: 'type',
            align: 'left',
            width: 120,
            render: (data) => {
                const config = getTankTypeConfig(data.type);
                return (
                    <Typography typography="subtitle_15" color={config?.color}>
                        {config?.shortLabel}
                    </Typography>
                );
            },
        },

        {
            dataKey: 'material',
            title: 'Tank material',
            key: 'material',
            align: 'left',
            width: 150,
            render: (data) =>
                typeof data?.material === 'string'
                    ? data?.material
                    : data?.material?.name,
        },

        {
            dataKey: 'shape',
            title: 'Tank shape',
            key: 'shape',
            align: 'left',
            width: 130,
            render: (data) =>
                getTankShapeConfig(data.shape as ETankShape)?.label,
        },
        {
            dataKey: 'dimensions',
            title: 'Tank dimensions',
            key: 'dimensions',
            align: 'left',
            render: (data) => {
                return (
                    <div className={flex({ width: '200px', gap: '10px' })}>
                        <DimensionTag>
                            <b>L: </b>
                            {data?.length}
                        </DimensionTag>
                        <DimensionTag>
                            <b>W: </b>
                            {data?.width}
                        </DimensionTag>
                        <DimensionTag>
                            <b>H: </b>
                            {data?.height}
                        </DimensionTag>
                    </div>
                );
            },
        },
        {
            dataKey: 'effectiveCap',
            title: 'EC',
            key: 'effectiveCap',
            align: 'left',
            width: 50,
        },
        {
            dataKey: 'floorLevel',
            title: 'FL',
            key: 'floorLevel',
            align: 'left',
            width: 50,
        },
        {
            dataKey: 'waterSaved',
            title: 'Water saved',
            key: 'waterSaved',
            align: 'left',
            render: (data: ITank) => (
                <div
                    className={css({
                        width: '120px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.waterSaved}
                </div>
            ),
        },

        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: 32,
            render: (row) => <ChevronRight />,
        },
    ];
    const handleClick = (rowData: any) => {
        router.push(`${Slug.TANKS}/${rowData.id}?callbackUrl=${pathname}`);
    };
    return (
        <Table
            dataSource={props.data}
            loading={props.loading}
            columns={columns}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
