import { phoneValidator } from '@/utilities/validations';
import { z } from 'zod';

const newSchema = {
    email: z.string().email('Invalid email address'),
    pubNumber: z.string().min(1, 'This field cannot be empty'),
    nameBuilding: z.string().min(1, 'This field cannot be empty'),
    customerName: z.string().min(1, 'This field cannot be empty'),
    buildingCategory: z.string().min(1, 'This field cannot be empty'),
    contactPerson: z.string().refine(phoneValidator, 'Invalid phone number'),
    officeTel: z.string().refine(phoneValidator, 'Invalid phone number'),
};

export const createNewCustomerSchema = z.object(newSchema);

export const createNewCustomerWithAgentSchema = z.object({
    ...newSchema,
    contactName: z.string().min(1, 'This field cannot be empty'),
    companyName: z.string().min(1, 'This field cannot be empty'),
    designation: z.string().min(1, 'This field cannot be empty'),
    inChargeCategory: z.string().min(1, 'This field cannot be empty'),
    postalCode: z.string().min(1, 'This field cannot be empty'),
    blockNo: z.string().min(1, 'This field cannot be empty'),
    street: z.string().min(1, 'This field cannot be empty'),
    buildingName: z.string().min(1, 'This field cannot be empty'),
    agentOfficeTel: z.string().refine(phoneValidator, 'Invalid phone number'),
    agentEmail: z.string().email('Invalid email address'),
});
