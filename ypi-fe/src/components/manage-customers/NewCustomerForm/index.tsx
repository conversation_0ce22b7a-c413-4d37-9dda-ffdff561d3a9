'use client';

import {
    EFormInputType,
    IMultipleInputOption,
    TFormInputOption,
} from '@/utilities/types/form';
import { FieldValues, UseFormSetError, useForm } from 'react-hook-form';
import {
    ICustomer,
    INewCustomerParams,
} from '@/utilities/types/entities/customer';
import React, { useState } from 'react';
import {
    bottomContainerStyle,
    formPartStyle,
    formPartsContainerStyle,
    formStyle,
} from './style';
import {
    createNewCustomerSchema,
    createNewCustomerWithAgentSchema,
} from './validation';

import AlertCircle from '@/components/globals/Icons/AlertCircle';
import Button from '@/components/globals/Button';
import { FormPart } from '@/components/globals/InputForm/FormPart';
import MinusCircle from '@/components/globals/Icons/MinusCircle';
import Modal from '@/components/globals/Modal';
import PlusCircle from '@/components/globals/Icons/PlusCircle';
import ScrollView from '@/components/globals/ScrollView';
import Search from '@/components/globals/Icons/Search';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { useBuildings } from '@/hooks/building';
import { useInCharges } from '@/hooks/in-charge';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import {DataBuildingCategories, DataInChargeCategory} from "@/data/globals/manage-customers";

type Props = {
    disabled?: boolean;
    defaultValues?: Record<string, any>;
    hasAgent?: boolean;
    submitText?: string;
    onDelete?: (user: ICustomer) => void;
    onSubmit?: (
        data: Partial<INewCustomerParams>,
        setError: UseFormSetError<FieldValues>
    ) => void;
};

export default function NewCustomerForm(props: Props) {
    const router = useRouter();
    const [hasAgent, setHasAgent] = useState(props.hasAgent);
    const [openModal, setOpenModal] = useState<boolean>(false);

    const {
        register,
        handleSubmit,
        formState: { errors, dirtyFields },
        setValue,
        resetField,
        control,
        setError,
    } = useForm({
        defaultValues: props.defaultValues,
        resolver: zodResolver(
            hasAgent
                ? createNewCustomerWithAgentSchema
                : createNewCustomerSchema
        ),
    });

    const { data: buildingCategories } = useBuildings();
    const { data: inChargeCategories } = useInCharges();

    const AgentListLeftField: TFormInputOption[] = [
        {
            id: 'contactName',
            name: 'contactName',
            label: 'Name of contact',
            type: EFormInputType.Text,
            variant: 'outline',
        },
        {
            id: 'companyName',
            name: 'companyName',
            label: 'Company Name',
            type: EFormInputType.Text,
            variant: 'outline',
        },
        {
            id: 'inChargeCategory',
            name: 'inChargeCategory',
            label: 'In-charge Category',
            type: EFormInputType.Select,
            options: DataInChargeCategory ?? [],
            variant: 'outline',
        },
        {
            id: 'designation',
            name: 'designation',
            label: 'Designation',
            type: EFormInputType.Text,
            variant: 'outline',
        },
        {
            id: 'agentEmail',
            name: 'agentEmail',
            label: 'Email',
            type: EFormInputType.Email,
            variant: 'outline',
        },
    ];

    const AgentListRightField: TFormInputOption[] = [
        {
            id: 'agentOfficeTel',
            name: 'agentOfficeTel',
            label: 'Office Tel/Ext',
            type: EFormInputType.Phone,
            phoneCode: '+65',
            variant: 'outline',
        },
        {
            id: 'postalCode',
            name: 'postalCode',
            label: 'Postal Code',
            type: EFormInputType.Text,
            variant: 'outline',
            endAdornment: <Search />,
        },
        {
            type: EFormInputType.Multiple,
            id: 'blockStreet',
            className: css({ mt: 0 }),
            inputs: [
                {
                    id: 'blockNo',
                    name: 'blockNo',
                    label: 'Block no',
                    type: EFormInputType.Text,
                    variant: 'outline',
                    sizes: 'xs',
                    className: css({
                        maxW: '120px',
                        w: '100%',
                    }),
                },
                {
                    id: 'street',
                    name: 'street',
                    label: 'Street name',
                    type: EFormInputType.Text,
                    variant: 'outline',
                    weight: 1,
                    className: css({
                        flex: 1,
                    }),
                },
            ],
        },
        {
            id: 'buildingName',
            name: 'buildingName',
            label: 'Building name',
            type: EFormInputType.Text,
            variant: 'outline',
        },
    ];
    function checkFieldsDirty(dirtyFields: any, arr: TFormInputOption[]) {
        const objectKeys = arr.map((item) => item.id);
        return Object.keys(dirtyFields).some((key) => objectKeys.includes(key));
    }

    const handleDeleteAgent = () => {
        setHasAgent(false);
        setOpenModal(false);
        AgentListLeftField.forEach((item) => {
            resetField(item.id, { defaultValue: null });
        });
        AgentListRightField.forEach((item) => {
            resetField(item.id, { defaultValue: null });
            if ((item as IMultipleInputOption).inputs) {
                (item as IMultipleInputOption).inputs.forEach((item) => {
                    resetField(item.id, { defaultValue: null });
                });
            }
        });
    };
    const handleClosePopup = () => {
        setOpenModal(false);
    };

    const toggleHasAgent = () => {
        if (hasAgent) {
            if (
                checkFieldsDirty(dirtyFields, AgentListLeftField) ||
                props.hasAgent
            ) {
                setOpenModal(true);
            } else {
                setHasAgent(false);
            }
        } else {
            setHasAgent(true);
        }
    };

    const onSubmit = (value: FieldValues) => {
        const customerData: Partial<INewCustomerParams> = {
            name: value.customerName,
            buildingCategory: value.buildingCategory,
            email: value.email,
            officePhoneCode: '+65',
            officePhoneNumber: value.officeTel,
            pubNumber: value.pubNumber,
            phoneCode: '+65',
            phoneNumber: value.contactPerson,
            nameBuilding: value.nameBuilding,
            agent: hasAgent
                ? {
                      name: value.contactName,
                      company: value.companyName,
                      inChargeCategory: value.inChargeCategory,
                      email: value.agentEmail,
                      designation: value.designation,
                      phoneCode: '+65',
                      phoneNumber: value.agentOfficeTel,
                      postalCode: value.postalCode,
                      blockNo: value.blockNo,
                      street: value.street,
                      building: value.buildingName,
                  }
                : undefined,
        };

        props.onSubmit?.(customerData, setError);
    };

    return (
        <>
            <form className={formStyle} onSubmit={handleSubmit(onSubmit)}>
                <ScrollView className={css({ pr: 4, pt: 4 })}>
                    <div
                        className={css({
                            maxWidth: '922px',
                        })}
                    >
                        <Typography
                            typography="subtitle_20"
                            color="primary_100"
                        >
                            Details of Customer
                        </Typography>
                        <div className={formPartsContainerStyle}>
                            <FormPart
                                control={control}
                                inputs={[
                                    {
                                        id: 'pubNumber',
                                        name: 'pubNumber',
                                        label: 'PUB Account number',
                                        type: EFormInputType.Text,
                                        variant: 'outline',
                                    },
                                    {
                                        id: 'nameBuilding',
                                        name: 'nameBuilding',
                                        label: 'Name Building',
                                        type: EFormInputType.Text,
                                        variant: 'outline',
                                    },
                                    {
                                        id: 'buildingCategory',
                                        name: 'buildingCategory',
                                        label: 'Building Category',
                                        type: EFormInputType.Select,
                                        options: DataBuildingCategories ?? [],
                                        variant: 'outline',
                                    },
                                    {
                                        id: 'customerName',
                                        name: 'customerName',
                                        label: 'Name of customer',
                                        type: EFormInputType.Text,
                                        variant: 'outline',
                                    },
                                ]}
                                errors={errors}
                                register={register}
                                setValue={setValue}
                                className={formPartStyle}
                            />
                            <FormPart
                                control={control}
                                inputs={[
                                    {
                                        id: 'officeTel',
                                        name: 'officeTel',
                                        label: 'Offict Tel/Ext',
                                        type: EFormInputType.Phone,
                                        phoneCode: '+65',
                                        variant: 'outline',
                                    },
                                    {
                                        id: 'email',
                                        name: 'email',
                                        label: 'Email',
                                        type: EFormInputType.Email,
                                        variant: 'outline',
                                    },
                                    {
                                        id: 'contactPerson',
                                        name: 'contactPerson',
                                        label: 'Contact person',
                                        type: EFormInputType.Phone,
                                        phoneCode: '+65',
                                        variant: 'outline',
                                    },
                                ]}
                                errors={errors}
                                register={register}
                                setValue={setValue}
                                className={formPartStyle}
                            />
                        </div>
                        <div className={flex()}>
                            <Typography
                                className={css({ flex: 1 })}
                                typography="subtitle_20"
                                color="primary_100"
                            >
                                Details of agent (IF ANY)
                            </Typography>

                            {
                                <button type="button" onClick={toggleHasAgent}>
                                    {hasAgent ? (
                                        <MinusCircle
                                            className={css({
                                                color: 'secondary.40',
                                            })}
                                        />
                                    ) : (
                                        <PlusCircle
                                            className={css({
                                                color: 'primary.100',
                                            })}
                                        />
                                    )}
                                </button>
                            }
                        </div>
                        {hasAgent ? (
                            <div className={formPartsContainerStyle}>
                                <FormPart
                                    control={control}
                                    inputs={AgentListLeftField}
                                    errors={errors}
                                    register={register}
                                    setValue={setValue}
                                    className={formPartStyle}
                                />
                                <FormPart
                                    control={control}
                                    inputs={AgentListRightField}
                                    errors={errors}
                                    register={register}
                                    setValue={setValue}
                                    className={formPartStyle}
                                />
                            </div>
                        ) : null}
                    </div>
                </ScrollView>
                <div className={bottomContainerStyle}>
                    <Button
                        type="reset"
                        onClick={() => {
                            router.back();
                        }}
                        visual="outline_secondary_60"
                    >
                        Cancel
                    </Button>
                    <Button disabled={props.disabled} type="submit">
                        {props.submitText ?? 'Create'}
                    </Button>
                </div>
            </form>
            <Modal
                isOpen={openModal}
                iconTitle={<AlertCircle />}
                onClose={handleClosePopup}
            >
                <>
                    <div
                        className={css({
                            textAlign: 'center',
                        })}
                    >
                        Do you really want to delete agent information?
                    </div>
                    <div
                        className={css({
                            display: 'flex',
                            justifyContent: 'center',
                            mt: '48px',
                        })}
                    >
                        <Button
                            size="small"
                            className={css({
                                mx: '6px',
                            })}
                            onClick={() => handleDeleteAgent()}
                        >
                            Delete
                        </Button>
                        <Button
                            size="small"
                            visual="outline_primary"
                            className={css({
                                mx: '6px',
                            })}
                            onClick={handleClosePopup}
                        >
                            Cancel
                        </Button>
                    </div>
                </>
            </Modal>
        </>
    );
}
