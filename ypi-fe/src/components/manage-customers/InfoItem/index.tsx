import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import joinClassName from '@/utilities/globals/function/className';
import { ColorName } from '@/utilities/globals/panda/colors';
import ETypographyTag from '@/utilities/types/enums/Typography';

type ItemProps = {
    label: string;
    value?: string | number | null;
    className?: string;
    renderValue?: () => React.ReactNode;
    valueColor?: ColorName;
    valueStyle?: string;
    labelClassName?: string;
};

const valueStyle = css({ mt: '6px' });

export function InfoItem(props: ItemProps) {
    return (
        <div className={props.className}>
            <Typography
                typography="body_14"
                color="body_1"
                className={joinClassName(
                    css({ wordBreak: 'break-word' }),
                    props?.labelClassName || ''
                )}
            >
                {props.label}
            </Typography>
            {props.renderValue ? (
                <div
                    className={joinClassName(
                        valueStyle,
                        props?.valueStyle || ''
                    )}
                >
                    {props.renderValue()}
                </div>
            ) : (
                <Typography
                    className={joinClassName(
                        valueStyle,
                        props?.valueStyle || '',
                        css({ wordBreak: 'break-word' })
                    )}
                    tag={ETypographyTag.div}
                    typography={props.value ? 'subtitle_15' : 'body_15'}
                    color={
                        props.value
                            ? props.valueColor ?? 'secondary_100'
                            : 'secondary_40'
                    }
                >
                    {props.value ? props.value : '<No data>'}
                </Typography>
            )}
        </div>
    );
}
