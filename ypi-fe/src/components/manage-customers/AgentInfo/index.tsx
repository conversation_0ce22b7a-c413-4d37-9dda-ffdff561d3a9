import { flex } from '@/styled-system/patterns';
import { ICustomer } from '@/utilities/types/entities/customer';
import { InfoItem } from '../InfoItem';

type Props = {
    customer?: ICustomer;
};

export default function AgentInfo(props: Props) {
    return (
        <div
            className={flex({
                direction: 'column',
                gap: 3,
                pt: 3,
                pb: 3,
            })}
        >
            <InfoItem
                label="Name of contact"
                value={props.customer?.agent?.name}
            />
            <InfoItem
                label="Company name"
                value={props.customer?.agent?.company}
            />
            <InfoItem
                label="In-charge Category"
                value={props?.customer?.agent?.inChargeCategory}
            />
            <InfoItem
                label="Designation"
                value={props.customer?.agent?.designation}
            />
            <InfoItem label="Email" value={props.customer?.agent?.email} />
            <InfoItem
                label="Office Tal/Ext"
                value={
                    props.customer?.agent?.phoneNumber
                        ? `${props.customer?.agent?.phoneCode} ${props.customer?.agent?.phoneNumber}`
                        : undefined
                }
            />
            <InfoItem
                label="Postal Code"
                value={props.customer?.agent?.postalCode}
            />
            <InfoItem label="Block no" value={props.customer?.agent?.blockNo} />
            <InfoItem
                label="Street name"
                value={props.customer?.agent?.street}
            />
            <InfoItem
                label="Building name"
                value={props.customer?.agent?.building}
            />
        </div>
    );
}
