'use client'
import InputForm from '@/components/globals/InputForm';
import { EFormInputType } from '@/utilities/types/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FieldValues } from 'react-hook-form';
import { createNewSchema } from './validation';
import {useParams} from "next/navigation";
import {mutate} from "swr";
import {EGeneralReport} from "@/utilities/types/enums/GeneralReport";
import toast from "react-hot-toast";

export default function DefectForm({setCreateDefect}:{setCreateDefect: (value: boolean) => void}) {
    const params = useParams();
    const taskId = params.id;

    const onSubmit = async (data: FieldValues) => {
        try {
            const loadingToast = toast.loading('Processing...');
            const payload = {
                assignTaskId: taskId,
                title: data.title,
            }
            const url = '/api/new-defect';
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });
            toast.dismiss(loadingToast);
            if (response.ok) {
                toast.success('Create Defect successfully!');
                setCreateDefect(false);
                await mutate(`${EGeneralReport.PRE_INSPECTION}/${taskId}`);
            }else {
                toast.error('Create Defect Failed');
            }
        }
        catch (e) {
            console.log(e);
            toast.error('Create Defect Failed');
        }
    };

    return (
        <InputForm
            inputs={[
                {
                    id: 'title',
                    name: 'title',
                    placeholder: 'Defect Title',
                    type: EFormInputType.Text,
                    variant: 'outline',
                    labelPosition: 'top',
                },
            ]}
            onSubmit={onSubmit}
            resolver={zodResolver(createNewSchema)}
            submitText={'Submit'}
        />
    );
}
