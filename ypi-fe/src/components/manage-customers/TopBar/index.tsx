import ETypographyTag from '@/utilities/types/enums/Typography';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import Typography from '@/components/globals/Typography';
import Button from '@/components/globals/Button';
import ChevronLeft from '@/components/globals/Icons/ChevronLeft';
import AddCircle from '@/components/globals/Icons/AddCircle';

type Props = {
    title?: string;
    subTitle?: string;
    onPrimaryClick?: () => void;
    secondaryButtonVisible?: boolean;
    primaryButtonVisible?: boolean;
    onSecondaryClick?: () => void;
    primaryText?: string;
    secondaryText?: string;
    onBack?: () => void;
    renderButtons?: () => void;
};

export default function TopBar(props: Props) {
    const { primaryButtonVisible = true } = props;

    return (
        <div className={flex()}>
            <div className={flex({ flex: 1, alignItems: 'center' })}>
                <button onClick={props.onBack}>
                    <ChevronLeft className={css({ mr: 3 })} />
                </button>
                <Typography
                    color="secondary_100"
                    typography="header_24"
                    tag={ETypographyTag.h1}
                >
                    <span
                        className={css({ textTransform: 'uppercase', mr: 3 })}
                    >
                        {props.title}
                    </span>
                </Typography>
                {props.subTitle && (
                    <Typography
                        color="primary_100"
                        typography="header_24"
                        tag={ETypographyTag.h1}
                    >
                        <span className={css({ textTransform: 'uppercase' })}>
                            {props.subTitle}
                        </span>
                    </Typography>
                )}
            </div>

            <div className={css({ display: 'flex', ml: 4, gap: '12px' })}>
                {props.renderButtons?.() ?? (
                    <>
                        {props.secondaryButtonVisible ? (
                            <Button
                                onClick={props.onSecondaryClick}
                                visual="outline_primary"
                            >
                                {props.secondaryText}
                            </Button>
                        ) : null}
                        {primaryButtonVisible ? (
                            <Button onClick={props.onPrimaryClick}>
                                <AddCircle /> {props.primaryText}
                            </Button>
                        ) : null}
                    </>
                )}
            </div>
        </div>
    );
}
