import { flex } from '@/styled-system/patterns';
import { ICustomer } from '@/utilities/types/entities/customer';
import { InfoItem } from '../InfoItem';
import dayjs from "dayjs";

type Props = {
    customer?: ICustomer;
};

export default function CustomerInfo(props: Props) {
    return (
        <div
            className={flex({
                direction: 'column',
                gap: 3,
                pt: 3,
                pb: 3,
            })}
        >
            <InfoItem
                label="PUB Account number"
                value={props?.customer?.pubNumber}
            />
            <InfoItem
                label="Name Building"
                value={props?.customer?.nameBuilding}
            />
            <InfoItem
                label="Building Category"
                value={props?.customer?.buildingCategory}
            />
            <InfoItem label="Name of customer" value={props.customer?.name} />
            <InfoItem
                label="Office Tel/Ext"
                value={
                    props.customer?.officePhoneNumber
                        ? `${props.customer.officePhoneCode} ${props.customer.officePhoneNumber}`
                        : undefined
                }
            />
            <InfoItem
                label="Contact person"
                value={
                    props.customer?.phoneNumber
                        ? `${props.customer.phoneCode} ${props.customer.phoneNumber}`
                        : undefined
                }
            />
            <InfoItem
                label="Email"
                value={props.customer?.email}
            />
        </div>
    );
}
