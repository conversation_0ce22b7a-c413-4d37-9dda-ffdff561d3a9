import InputForm from '@/components/globals/InputForm';
import { IMaterial } from '@/utilities/types/entities/material';
import {
    ITank,
    TANK_SHAPE_OPTIONS,
    TANK_TYPE_OPTIONS,
} from '@/utilities/types/entities/tank';
import { EFormInputType } from '@/utilities/types/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMemo } from 'react';
import { FieldValues } from 'react-hook-form';
import { dimensionFieldStyle, formFieldRowStyle } from './style';
import { createNewSchema } from './validation';
import { css } from '@/styled-system/css';
type Props = {
    isUpdate?: boolean;
    isLoading: boolean;
    onSubmit: (data: Partial<ITank>) => void;
    defaultValues?: Partial<ITank>;
    materialAllows?: IMaterial[];
};

export default function TankForm(props: Props) {
    const onSubmit = (data: FieldValues) => {
        props.onSubmit(data);
    };

    const materialOptions = useMemo(() => {
        return (
            props?.materialAllows?.map((item) => ({
                id: item?.id || '',
                name: item?.name || '',
            })) || []
        );
    }, [props?.materialAllows?.length]);

    return (
        <InputForm
            defaultValues={props.defaultValues}
            inputs={[
                {
                    id: 'airdentity',
                    name: 'airdentity',
                    label: 'Tank Airdentity',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    id: 'code',
                    name: 'code',
                    label: 'Tank ID',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    id: 'type',
                    name: 'type',
                    label: 'Tank type',
                    type: EFormInputType.Select,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                    // TODO: Tank type should be common data from BE
                    options: TANK_TYPE_OPTIONS,
                },
                {
                    id: 'materialId',
                    name: 'materialId',
                    label: 'Tank material',
                    type: EFormInputType.Select,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                    options: materialOptions,
                },
                {
                    id: 'shape',
                    name: 'shape',
                    label: 'Tank shape',
                    type: EFormInputType.Select,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                    // TODO: Tank shape should be common data from BE
                    options: TANK_SHAPE_OPTIONS,
                },
                {
                    id: 'waterSaved',
                    name: 'waterSaved',
                    label: 'Water Saved',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    type: EFormInputType.Multiple,
                    id: 'dimension',
                    label: 'Tank dimensions',
                    className: css({
                        marginTop: '16px',
                    }),

                    inputs: [
                        {
                            id: 'length',
                            name: 'length',
                            label: 'Length',
                            type: EFormInputType.Text,
                            variant: 'contained',
                            labelPosition: 'left',
                            className: dimensionFieldStyle,
                        },
                        {
                            id: 'width',
                            name: 'width',
                            label: 'Width',
                            type: EFormInputType.Text,
                            variant: 'contained',
                            labelPosition: 'left',
                            className: dimensionFieldStyle,
                        },
                        {
                            id: 'height',
                            name: 'height',
                            label: 'Height',
                            type: EFormInputType.Text,
                            variant: 'contained',
                            labelPosition: 'left',
                            className: dimensionFieldStyle,
                        },
                    ],
                },
                {
                    type: EFormInputType.Multiple,
                    id: 'info',
                    inputs: [
                        {
                            id: 'effectiveCap',
                            name: 'effectiveCap',
                            label: 'Effective cap',
                            type: EFormInputType.Text,
                            variant: 'contained',
                            labelPosition: 'left',
                            className: dimensionFieldStyle,
                        },
                        {
                            id: 'floorLevel',
                            name: 'floorLevel',
                            label: 'Floor level',
                            type: EFormInputType.Text,
                            variant: 'contained',
                            labelPosition: 'left',
                            className: dimensionFieldStyle,
                        },
                    ],
                },
            ]}
            onSubmit={onSubmit}
            disabled={props.isLoading}
            resolver={zodResolver(createNewSchema)}
            submitText={props.isUpdate ? 'Update' : 'Create'}
        />
    );
}
