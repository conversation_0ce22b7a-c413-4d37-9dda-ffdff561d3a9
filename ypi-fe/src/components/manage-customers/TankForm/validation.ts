import { z } from 'zod';

export const createNewSchema = z.object({
    airdentity: z.string().optional(),
    code: z.string().min(1, 'This field cannot be empty'),
    type: z.string().min(1, 'This field cannot be empty'),
    materialId: z.string().min(1, 'This field cannot be empty'),
    shape: z.string().min(1, 'This field cannot be empty'),
    length: z.string().min(1, 'Required'),
    width: z.string().min(1, 'Required'),
    height: z.string().min(1, 'Required'),
    effectiveCap: z.string().min(1, 'Required'),
    floorLevel: z.string().min(1, 'Required'),
    waterSaved: z.string().optional(),
});

// export const updateAdminSchema = z.object({
//     email: z.string().email('Invalid email address'),
//     fullName: z.string().min(1, 'This field cannot be empty'),
//     company: z.string().min(1, 'This field cannot be empty'),
//     phoneNumber: z.string().refine(phoneValidator, 'Invalid phone number'),
// });
