import { flex } from '@/styled-system/patterns';
import Typography from '@/components/globals/Typography';

export enum EJobProgressStep {
    PreInspectionList = 'Pre-Inspection List',
    RectifyNonCompliance = 'Rectify Non-Compliance',
    Complete = 'Complete',
}

export type TJobProgressStep = { label: string; date: string };

const DEFAULT_STEPS: Record<
    EJobProgressStep,
    { label: EJobProgressStep; date: string }
> = {
    [EJobProgressStep['PreInspectionList']]: {
        label: EJobProgressStep['PreInspectionList'],
        date: '',
    },
    [EJobProgressStep['RectifyNonCompliance']]: {
        label: EJobProgressStep['RectifyNonCompliance'],
        date: '',
    },
    [EJobProgressStep['Complete']]: {
        label: EJobProgressStep['Complete'],
        date: '',
    },
};

type Props = {
    activeStep: EJobProgressStep;
};

export default function JobProgress({ activeStep }: Props) {
    return (
        <div
            className={flex({
                flex: 1,
                gap: '48px',
                justifyContent: 'flex-end',
            })}
        >
            {Object.keys(DEFAULT_STEPS).map((stepKey, index) => (
                <div
                    key={stepKey}
                    className={flex({
                        width: 'fit-content',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        gap: '12px',
                    })}
                >
                    <div
                        className={flex({
                            width: '36px',
                            height: '36px',
                            borderRadius: '50%',
                            justifyContent: 'center',
                            alignItems: 'center',
                            border:
                                stepKey === activeStep
                                    ? 'unset'
                                    : '1.5px solid',
                            borderColor:
                                stepKey === activeStep
                                    ? 'secondary.40'
                                    : 'transparent',
                            backgroundColor:
                                stepKey === activeStep
                                    ? 'primary.100'
                                    : 'secondary.40',
                        })}
                    >
                        <Typography
                            color={
                                activeStep === stepKey
                                    ? 'white'
                                    : 'background_40'
                            }
                            typography="subtitle_16"
                        >
                            {`${index + 1}`.padStart(2, '0')}
                        </Typography>
                    </div>
                    <div
                        className={flex({
                            flex: 1,
                            flexDirection: 'column',
                            gap: '4px',
                        })}
                    >
                        <Typography
                            color={
                                activeStep === stepKey
                                    ? 'primary_100'
                                    : 'secondary_40'
                            }
                            typography="subtitle_16"
                        >
                            {
                                DEFAULT_STEPS?.[stepKey as EJobProgressStep]
                                    ?.label
                            }
                        </Typography>
                        <Typography
                            color={
                                activeStep === stepKey
                                    ? 'secondary_100'
                                    : 'secondary_40'
                            }
                            typography="body_12"
                        >
                            {DEFAULT_STEPS?.[stepKey as EJobProgressStep]?.date}
                        </Typography>
                    </div>
                </div>
            ))}
        </div>
    );
}
