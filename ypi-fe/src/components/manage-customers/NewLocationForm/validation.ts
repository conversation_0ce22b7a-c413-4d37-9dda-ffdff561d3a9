import { z } from 'zod';

export const createNewSchema = z.object({
    postalCode: z.string().min(1, 'This field cannot be empty'),
    blockNo: z.string().min(1, 'This field cannot be empty'),
    street: z.string().min(1, 'This field cannot be empty'),
    building: z.string().min(1, 'This field cannot be empty'),
    location: z
        .object({
            lat: z.number(),
            lng: z.number(),
        })
        .optional(),
});

// export const updateAdminSchema = z.object({
//     email: z.string().email('Invalid email address'),
//     fullName: z.string().min(1, 'This field cannot be empty'),
//     company: z.string().min(1, 'This field cannot be empty'),
//     phoneNumber: z.string().refine(phoneValidator, 'Invalid phone number'),
// });
