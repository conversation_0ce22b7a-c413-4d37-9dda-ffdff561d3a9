import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { FieldValues } from 'react-hook-form';
import { createNewSchema } from './validation';
import InputForm from '@/components/globals/InputForm';
import { INewProjectLocationParams } from '@/utilities/types/entities/project-location';
import { EFormInputType } from '@/utilities/types/form';
import { formFieldRowStyle } from './style';
import { css } from '@/styled-system/css';

interface ICreateFormProps {
    isUpdate?: boolean;
    isLoading: boolean;
    onSubmit: (data: INewProjectLocationParams) => void;
    defaultValues?: {
        postalCode?: string;
        blockNo?: string;
        street?: string;
        building?: string;
        location?: {
            lat: number;
            lng: number;
        };
    };
}

export default function NewLocationForm(props: ICreateFormProps) {
    const onSubmit = (data: FieldValues) => {
        props.onSubmit({
            postalCode: data.postalCode,
            blockNo: data.blockNo,
            street: data.street,
            building: data.building,
            lat: `${data?.location?.lat || 0}`,
            long: `${data?.location?.lng || 0}`,
        });
    };

    return (
        <InputForm
            defaultValues={props.defaultValues}
            inputs={[
                {
                    id: 'postalCode',
                    name: 'postalCode',
                    label: 'Postal code',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    id: 'blockNo',
                    name: 'blockNo',
                    label: 'Block no',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    id: 'street',
                    name: 'street',
                    label: 'Street name',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    id: 'building',
                    name: 'building',
                    label: 'Building name',
                    type: EFormInputType.Text,
                    variant: 'contained',
                    labelPosition: 'left',
                    className: formFieldRowStyle,
                },
                {
                    id: 'location',
                    name: 'location',
                    type: EFormInputType.Map,
                    className: css({
                        width: '100%',
                        height: '200px',
                        borderRadius: '12px',
                    }),
                },
            ]}
            onSubmit={onSubmit}
            disabled={props.isLoading}
            resolver={zodResolver(createNewSchema)}
            submitText={props.isUpdate ? 'Update' : 'Create'}
        />
    );
}
