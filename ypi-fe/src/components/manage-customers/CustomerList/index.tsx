import Table, { IColProps } from '@/components/globals/Table';

import Checked from '@/components/globals/Icons/Checked';
import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { ICustomer } from '@/utilities/types/entities/customer';
import { Slug } from '@/utilities/types/enums/Slug';
import { css } from '@/styled-system/css';
import { useRouter } from 'next/navigation';

interface ICustomerListProps {
    // onItemEdit: (item: ICustomer) => void;
    // onItemDelete: (item: ICustomer) => void;
    data: ICustomer[];
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
}

export default function CustomerList(props: ICustomerListProps) {
    const router = useRouter();
    const columns: IColProps<ICustomer>[] = [
        {
            dataKey: 'name',
            title: 'Name',
            key: 'name',
            align: 'left',
        },
        // {
        //     dataKey: 'building',
        //     title: 'Building',
        //     key: 'building',
        //     align: 'left',
        //     render: (data) => data.building?.name,
        // },

        {
            dataKey: 'office_tel',
            title: 'Office Tel/Ext',
            key: 'office_tel',
            align: 'left',
            render: (data) =>
                data.officePhoneNumber
                    ? `${data.officePhoneCode} ${data.officePhoneNumber}`
                    : '',
        },
        {
            dataKey: 'pubNumber',
            title: 'PUB account',
            key: 'pubNumber',
            align: 'left',
        },
        // {
        //     dataKey: 'pcNumber',
        //     title: 'PC number',
        //     key: 'pcNumber',
        //     align: 'left',
        // },
        {
            dataKey: 'agent',
            title: 'Agent',
            key: 'agent',
            align: 'left',
            render: (data) => {
                return data.agent ? (
                    <Checked
                        className={css({
                            color: 'primary.100',
                        })}
                    />
                ) : null;
            },
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: 64,
            render: (row) => {
                return <ChevronRight />;
            },
        },
    ];

    const handleClick = (rowData: any) => {
        router.push(`${Slug.MANAGE_CUSTOMERS}/${rowData.id}`);
    };

    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            onClick={handleClick}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
        />
    );
}
