import { IBuilding } from '@/utilities/types/entities/building';
import { containerStyle } from './style';
import Checkbox from '@/components/globals/Checkbox';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';

interface IFilterProps {
    buildings: IBuilding[];
    value: Record<string, boolean>;
    onChange?: (key: string, value: boolean) => void;
    valueKey: keyof IBuilding;
}

export default function Filter(props: IFilterProps) {
    return (
        <div className={containerStyle}>
            <Typography
                className={css({
                    mb: '12px',
                })}
                color="secondary_100"
                typography="header_16"
            >
                Building
            </Typography>
            {props.buildings.map((building) => {
                const k = building[props.valueKey];
                const isChecked =
                    typeof k === 'string' ? !!props.value[k] : false;
                return (
                    <Checkbox
                        isChecked={isChecked}
                        label={building?.name}
                        key={building.id}
                        onChange={(v) => {
                            if (typeof k === 'string') {
                                props.onChange?.(k, v);
                            }
                        }}
                        size="small"
                    />
                );
            })}
            <Checkbox
                className={css({
                    mt: '36px',
                })}
                isChecked={!!props.value.agent}
                label="Agent"
                onChange={(v) => props.onChange?.('agent', v)}
                size="small"
            />
        </div>
    );
}
