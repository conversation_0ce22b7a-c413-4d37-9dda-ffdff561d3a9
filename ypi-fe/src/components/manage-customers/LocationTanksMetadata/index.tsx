import Chip from '@/components/globals/Chip';
import SearchInput from '@/components/globals/SearchInput';
import { containerStyle } from './style';
import { css } from '@/styled-system/css';

type Props = {
    onChange: (value: string) => void;
    statisticType?: {
        high: string;
        intermediate: string;
        low: string;
    };
};

export default function LocationTanksMetadata(_props: Props) {
    const { statisticType, onChange } = _props;
    return (
        <div className={containerStyle}>
            <div
                className={css({
                    fontSize: '24px',
                    fontWeight: 600,
                    flex: 1,
                })}
                color="secondary_100"
            >
                Tank
            </div>
            <Chip
                label="Low Level"
                value={statisticType?.low || 0}
                colors="low"
            />
            <Chip
                label="Intermediate Level"
                value={statisticType?.intermediate || 0}
                colors="intermediate"
            />
            <Chip
                label="High Level"
                value={statisticType?.high || 0}
                colors="high"
            />
            <SearchInput onChange={onChange} />
        </div>
    );
}
