import { IProjectLocation } from '@/utilities/types/entities/project-location';
import { containerStyle } from './style';
import React from 'react';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { InfoItem } from '../InfoItem';
import { Edit } from '@/components/globals/Icons';
import Button from '@/components/globals/Button';
import Delete from '@/components/globals/Icons/Delete';
import { flex } from '@/styled-system/patterns';

type Props = {
    location?: IProjectLocation;
    onEdit?: () => void;
    onDelete?: () => void;
};

export default function LocationHeader({ location, onEdit, onDelete }: Props) {
    return (
        <div className={containerStyle}>
            <Typography
                className={css({ width: 143 })}
                color="primary_100"
                typography="subtitle_16"
            >
                Project location information
            </Typography>
            <InfoItem
                className={css({
                    minW: '98px',
                })}
                label="Postal code"
                value={location?.postalCode}
            />
            <InfoItem
                className={css({
                    minW: '98px',
                })}
                label="Block no"
                value={location?.blockNo}
            />
            <InfoItem
                className={css({
                    flexGrow: 1,
                })}
                label="Building name"
                value={location?.building}
            />
            <InfoItem
                className={css({
                    flexGrow: 1,
                })}
                label="Street name"
                value={location?.street}
            />
            <div className={flex({ gap: '18px' })}>
                <Button
                    onClick={onEdit}
                    size="icon_normal"
                    visual="outline_icon"
                >
                    <Edit />
                </Button>
                <Button
                    onClick={onDelete}
                    size="icon_normal"
                    visual="solid_icon"
                >
                    <Delete />
                </Button>
            </div>
        </div>
    );
}
