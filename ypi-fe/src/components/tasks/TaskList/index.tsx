'use client';

import * as React from 'react';

import Table, { IColProps } from '@/components/globals/Table';

import Checkbox from '@/components/globals/Checkbox';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { ITask } from '@/utilities/types/entities/task';
import ImageGrid from '@/components/globals/ImageGrid';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { ETaskStatus } from '@/utilities/types/enums/Task';

export interface TaskListProps {
    data: ITask[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
    selectedTaskIds: string[];
    handleClick?: (defectId: string) => void;
    disabledChecked?: boolean;
}

export default function TaskList(props: TaskListProps) {
    const { handleClick, disabledChecked } = props;
    const columns: IColProps<ITask>[] = [
        {
            dataKey: 'checkbox',
            title: '',
            key: 'checkbox',
            render: (data: ITask, index) => {
                return data?.status !== ETaskStatus['COMPLETED'] ? (
                    <Checkbox
                        isChecked={props?.selectedTaskIds?.includes(
                            data?.defect?.id || ''
                        )}
                        inputProps={{
                            disabled: disabledChecked,
                        }}
                        size="small"
                        onChange={() => handleClick?.(data?.defect?.id || '')}
                    />
                ) : (
                    `${index + 1}`.padStart(2, '0')
                );
            },
            width: 18,
        },

        {
            dataKey: 'title',
            title: 'Defects',
            key: 'title',
            align: 'left',
            width: 477,
            render: (data: ITask) => {
                return (
                    <Typography
                        color="body_1"
                        typography="body_15"
                        tag={ETypographyTag.h1}
                        className={css({
                            whiteSpace: 'pre-wrap',
                        })}
                    >
                        {data.title}
                    </Typography>
                );
            },
        },

        {
            dataKey: 'qty',
            title: 'Qty',
            key: 'qty',
            align: 'left',
        },
        {
            dataKey: 'size',
            title: 'Size',
            key: 'size',
            align: 'left',
        },
        {
            dataKey: 'staffRemark',
            title: 'Remark',
            key: 'staffRemark',
            align: 'left',
            width: 260,
            render: (data: ITask) => {
                return (
                    <Typography
                        color="body_1"
                        typography="body_15"
                        tag={ETypographyTag.h1}
                        className={css({
                            whiteSpace: 'pre-wrap',
                        })}
                    >
                        {data.staffRemark}
                    </Typography>
                );
            },
        },
        {
            dataKey: 'photo',
            title: 'Photo (Before)',
            key: 'photots',
            align: 'left',
            width: 160,

            render: (data: ITask) => {
                return (
                    <ImageGrid
                        photos={
                            data?.images?.map((image) => ({
                                id: image?.id || '',
                                url: image?.link || '',
                                key: image?.name || '',
                                link: image?.link || '',
                            })) || []
                        }
                        visual="inline"
                        maxItem={2}
                        mode="view-image-gallery"
                        className={css({ pt: 0 })}
                    />
                );
            },
        },
    ];

    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            margin="dense"
        />
    );
}
