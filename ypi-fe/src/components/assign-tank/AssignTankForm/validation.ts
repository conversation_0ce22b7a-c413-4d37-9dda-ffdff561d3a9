import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { dayjs } from '@/utilities/globals/function/dateHelper';

const newSchema = {
    timeLine: z.coerce.date().refine(
        (data) => {
            return dayjs(data).isSameOrAfter(dayjs());
        },
        {
            message: `Timeline should be after ${dayjs().format(
                'DD/MM/YYYY HH:mm'
            )}`,
        }
    ),
    staffs: z.array(z.string()).min(1),
};

export const assignTankResolver = zodResolver(z.object(newSchema));
