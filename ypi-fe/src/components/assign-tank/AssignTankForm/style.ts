import { flex } from '@/styled-system/patterns';

export const formStyle = flex({
    flex: 1,
    direction: 'column',
});
export const formPartsContainerStyle = flex({
    gap: '80px',
    mt: 3,
    mb: 3,
});

export const formPartStyle = flex({
    flex: 1,
    direction: 'row',
    gap: 3,
    '& > div': {
        flex: 3,
        alignItems: 'flex-start',
        gap: 5,
        '& > div': {
            flex: 1,
        },
    },
});

export const bottomContainerStyle = flex({
    justifyContent: 'flex-end',
    borderTop: '1px solid',
    borderColor: 'border.normal',
    gap: '12px',
    pt: 3,
    pb: 3,
    pl: 4,
    pr: 4,
});
