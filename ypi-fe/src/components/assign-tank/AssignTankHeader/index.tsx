import {
    containerStyle,
    dividerStyle,
    expandedStyle,
    headingTitle,
    projectInfoItem,
    wrapperStyle,
} from './style';
import React from 'react';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { Edit } from '@/components/globals/Icons';
import Button from '@/components/globals/Button';
import Delete from '@/components/globals/Icons/Delete';
import { flex } from '@/styled-system/patterns';
import { ITank } from '@/utilities/types/entities/tank';
import {
    getTankMaterialConfig,
    getTankShapeConfig,
    getTankTypeConfig,
} from '@/utilities/helpers/tank';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { InfoItem } from '@/components/manage-customers/InfoItem';
import { IProjectLocation } from '@/utilities/types/entities/project-location';

type Props = {
    tank?: ITank;
    location?: IProjectLocation;
};

export default function AssignTankHeader({ tank, location }: Props) {
    const tankTypeInfo = tank ? getTankTypeConfig(tank.type) : undefined;
    const meterialText =
        typeof tank?.material === 'string'
            ? tank?.material
            : tank?.material?.name;
    const tankMaterialInfo = tank
        ? getTankMaterialConfig(meterialText || '')
        : undefined;
    const tankShapeInfo = tank ? getTankShapeConfig(tank.shape) : undefined;

    return (
        <div className={wrapperStyle}>
            <div className={containerStyle}>
                <Typography
                    typography="header_16"
                    color="primary_100"
                    className={headingTitle}
                >
                    Project location information
                </Typography>
                <div className={flex({ gap: 6, flexDirection: 'row' })}>
                    <InfoItem
                        className={projectInfoItem}
                        label="Postal code"
                        value={location?.postalCode}
                        valueStyle={css({ w: '120px', mt: 0 })}
                        labelClassName={css({ w: '120px' })}
                    />
                    <InfoItem
                        className={projectInfoItem}
                        label="Address"
                        value={`${location?.street || ''} ${
                            location?.blockNo || ''
                        } ${location?.building || ''}`}
                        valueStyle={css({ mt: 0 })}
                        labelClassName={css({ w: '120px' })}
                    />
                </div>
            </div>
            <div className={dividerStyle} />

            <div className={containerStyle}>
                <Typography
                    typography="header_16"
                    color="primary_100"
                    className={headingTitle}
                >
                    Tank information
                </Typography>

                <InfoItem
                    className={css({ w: 98, whiteSpace: 'nowrap' })}
                    label="Tank type"
                    value={tankTypeInfo?.shortLabel ?? tank?.type}
                    valueColor={tankTypeInfo?.color}
                />
                <InfoItem
                    className={css({ w: 98 })}
                    label="Material"
                    value={tankMaterialInfo?.label ?? meterialText}
                />
                <InfoItem
                    label="Shape"
                    value={tankShapeInfo?.label ?? tank?.shape}
                    className={css({ whiteSpace: 'nowrap' })}
                />
                <InfoItem
                    className={css({ w: 226 })}
                    label="Tank dimensions"
                    renderValue={() => (
                        <Typography
                            className={flex({ gap: '6px' })}
                            tag={ETypographyTag.div}
                            typography="subtitle_15"
                            color="secondary_100"
                        >
                            <div className={expandedStyle}>
                                L: {tank?.length}
                            </div>
                            <div className={expandedStyle}>
                                W: {tank?.width}
                            </div>
                            <div className={expandedStyle}>
                                H: {tank?.height}
                            </div>
                        </Typography>
                    )}
                />
                <InfoItem
                    className={css({ flexGrow: 1 })}
                    label="EC"
                    value={tank?.effectiveCap}
                />
                <InfoItem
                    className={css({ flexGrow: 1 })}
                    label="Floor level"
                    value={tank?.floorLevel}
                />
            </div>
        </div>
    );
}
