import { css } from '@/styled-system/css';

export const containerStyle = css({
    display: 'flex',
    gap: 3,
    alignItems: 'flex-start',
    p: 3,
});

export const wrapperStyle = css({
    borderWidth: 1,
    borderColor: 'primary.100',
    borderStyle: 'solid',
    borderRadius: 10,
    gap: 3,
    mt: 4,
});

export const expandedStyle = css({ flex: 1 });

export const headingTitle = css({
    alignItems: 'self-start',
    minW: '190px',
    maxW: '190px',
});

export const projectInfoItem = css({ display: 'flex', gap: 6 });
export const dividerStyle = css({
    mx: 3,
    borderTop: 1,
    borderColor: 'border.field',
    borderStyle: 'solid',
});
