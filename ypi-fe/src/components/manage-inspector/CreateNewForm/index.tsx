import { zodResolver } from '@hookform/resolvers/zod';
import { FieldValues } from 'react-hook-form';
import { createNewInspectorSchema, updateInspectorSchema } from './validation';
import { INewUserParams, EUserRole } from '@/utilities/types/entities/user';
import InputForm, { IInputFormProps } from '@/components/globals/InputForm';
import { EFormInputType } from '@/utilities/types/form';

interface ICreateFormProps extends Partial<IInputFormProps> {
    isLoading: boolean;
    onSubmit: (data: Partial<INewUserParams>) => void;
    initValue?: Partial<INewUserParams>;
    isEdit?: boolean;
}

export default function CreateNewForm(props: ICreateFormProps) {
    const onSubmit = (data: FieldValues) => {
        props.onSubmit({
            email: data.email,
            // password: data.password,
            roleType: EUserRole.Inspector,
            fullName: data.fullName,
        });
    };

    return (
        <InputForm
            {...props}
            inputs={[
                {
                    id: 'fullName',
                    name: 'fullName',
                    label: 'Full name',
                    type: EFormInputType.Text,
                    defaultValue: props.initValue?.fullName ?? '',
                    variant: 'contained',
                    labelPosition: 'left',
                },
                {
                    id: 'email',
                    name: 'email',
                    label: 'Email',
                    readOnly: props.isEdit,
                    type: EFormInputType.Email,
                    defaultValue: props.initValue?.email ?? '',
                    variant: 'contained',
                    labelPosition: 'left',
                },
            ]}
            onSubmit={onSubmit}
            disabled={props.isLoading}
            resolver={zodResolver(
                props.isEdit ? createNewInspectorSchema : updateInspectorSchema
            )}
            submitText={props.isEdit ? 'Update' : 'Create'}
        />
    );
}
