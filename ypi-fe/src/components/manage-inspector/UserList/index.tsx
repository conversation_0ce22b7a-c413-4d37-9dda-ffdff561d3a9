import Button from '@/components/globals/Button';
import Table, { IColProps } from '@/components/globals/Table';
import { IUser } from '@/utilities/types/entities/user';

interface IUserListProps {
    onItemEdit: (item: IUser) => void;
    onItemDelete: (item: IUser) => void;
    data: IUser[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function UserList(props: IUserListProps) {
    const columns: IColProps<IUser>[] = [
        {
            dataKey: 'fullName',
            title: 'Name',
            key: 'fullName',
            align: 'left',
        },
        {
            dataKey: 'email',
            title: 'Email',
            key: 'email',
            align: 'left',
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            render: (row) => {
                //
                return (
                    <div>
                        <Button
                            styles="text"
                            visual="link_primary"
                            size="auto"
                            onClick={() => props.onItemEdit?.(row)}
                        >
                            Edit
                        </Button>
                        <Button
                            styles="text"
                            visual="link_error"
                            size="auto"
                            onClick={() => props.onItemDelete?.(row)}
                        >
                            Delete
                        </Button>
                    </div>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={props.data}
            loading={props.loading}
            columns={columns}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
        />
    );
}
