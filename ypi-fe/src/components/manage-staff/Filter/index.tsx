import Checkbox from '@/components/globals/Checkbox';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { IOption } from '@/utilities/types/form';
import { containerStyle } from './style';

interface IFilterProps {
    options: IOption[];
    value: Record<string, boolean>;
    onChange?: (key: string, value: boolean) => void;
    valueKey: keyof IOption;
}

export default function Filter(props: IFilterProps) {
    return (
        <div className={containerStyle}>
            <Typography
                className={css({
                    mb: '12px',
                })}
                color="secondary_100"
                typography="header_16"
            >
                Position
            </Typography>
            {props.options.map((option) => {
                const k = option[props.valueKey];
                const isChecked =
                    typeof k === 'string' ? !!props.value[k] : false;
                return (
                    <Checkbox
                        isChecked={isChecked}
                        label={option?.name || ''}
                        key={option.id}
                        onChange={(v) => {
                            if (typeof k === 'string') {
                                props.onChange?.(k, v);
                            }
                        }}
                        size="small"
                    />
                );
            })}
        </div>
    );
}
