import Table, { IColProps } from '@/components/globals/Table';

import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { IStaff } from '@/utilities/types/entities/staff';
import { Slug } from '@/utilities/types/enums/Slug';
import { css } from '@/styled-system/css';
import { useRouter } from 'next/navigation';

interface IStaffListProps {
    // onItemEdit: (item: ICustomer) => void;
    // onItemDelete: (item: ICustomer) => void;
    data: IStaff[];
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
}
const capitialize = css({ textTransform: 'capitalize' });
const maxWidthColumn = css({ maxWidth: '200px' });
export default function StaffList(props: IStaffListProps) {
    const router = useRouter();
    const columns: IColProps<IStaff>[] = [
        {
            dataKey: 'fullName',
            title: 'Name',
            key: 'fullName',
            align: 'left',
        },
        {
            dataKey: 'position',
            title: 'Position',
            key: 'position',
            align: 'left',
            className: capitialize,
        },
        {
            dataKey: 'phoneNumber',
            title: 'Phone',
            key: 'phoneNumber',
            align: 'left',
        },
        {
            dataKey: 'address',
            title: 'Address',
            key: 'address',
            align: 'left',
            className: maxWidthColumn,
        },
        {
            dataKey: 'code',
            title: 'ID',
            key: 'code',
            align: 'left',
        },
        {
            dataKey: 'email',
            title: 'Email   ',
            key: 'email',
            align: 'left',
            className: maxWidthColumn,
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: 64,
            render: (row) => <ChevronRight />,
        },
    ];
    const handleClick = (rowData: any) => {
        router.push(`${Slug.MANAGE_STAFF}/${rowData.id}`);
    };
    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
