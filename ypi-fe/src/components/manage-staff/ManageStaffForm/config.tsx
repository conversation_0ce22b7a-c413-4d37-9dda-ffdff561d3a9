import Upload from '@/components/globals/Icons/Upload';
import { staffOptions } from '@/utilities/types/entities/staff';
import { EFormInputType, TFormInputOption } from '@/utilities/types/form';
import { css } from '@/styled-system/css';

const UploadButton = () => (
    <>
        <Upload />
        Upload
    </>
);
export const formPartInputs: {
    left: TFormInputOption[];
    right: TFormInputOption[];
} = {
    left: [
        {
            id: 'avatar',
            name: 'avatar',
            type: EFormInputType.Avatar,
        },
        {
            id: 'fullName',
            name: 'fullName',
            label: 'Full name',
            type: EFormInputType.Text,
            variant: 'outline',
        },
        {
            id: 'phoneNumber',
            name: 'phoneNumber',
            label: 'Phone',
            type: EFormInputType.Phone,
            phoneCode: '+65',
            variant: 'outline',
        },
        {
            id: 'email',
            name: 'email',
            label: 'Email',
            type: EFormInputType.Email,
            variant: 'outline',
        },
        {
            id: 'address',
            name: 'address',
            label: 'Address',
            type: EFormInputType.Text,
            variant: 'outline',
        },
    ],
    right: [
        {
            id: 'birthday',
            name: 'birthday',
            label: 'Birthday',
            type: EFormInputType.DatePicker,
            variant: 'outline',
            showMonthDropdown: true,
            showYearDropdown: true,
            scrollableYearDropdown: true,
            yearDropdownItemNumber: 80,
        },
        {
            type: EFormInputType.Multiple,
            id: 'blockWoker',
            className: css({
                mt: 0,
                '& > div': {
                    flex: 1,
                },
            }),
            inputs: [
                {
                    id: 'code',
                    name: 'code',
                    label: 'ID',
                    type: EFormInputType.Text,
                    variant: 'outline',
                },
                {
                    id: 'position',
                    name: 'position',
                    label: 'Position',
                    type: EFormInputType.Select,
                    options: staffOptions,
                    variant: 'outline',
                },
            ],
        },

        {
            id: 'documents',
            name: 'documents',
            type: EFormInputType.InputFile,
            variant: 'withRenderLabel',
            withRenderProps: {
                label: 'Document',
                button: <UploadButton />,
            },
        },
        {
            id: 'certifications',
            name: 'certifications',
            type: EFormInputType.InputFile,
            variant: 'withRenderLabel',
            withRenderProps: {
                label: 'Certification',
                button: <UploadButton />,
            },
        },
    ],
};
