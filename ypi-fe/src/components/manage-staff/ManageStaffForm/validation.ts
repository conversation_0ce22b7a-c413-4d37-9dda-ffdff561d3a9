import { INewStaffParams } from '@/utilities/types/entities/staff';
import { phoneValidator } from '@/utilities/validations';
import { z } from 'zod';

const newSchema = {
    email: z.string().email('Invalid email address'),
    phoneNumber: z.string().min(1, 'This field cannot be empty'),
    fullName: z.string().min(1, 'This field cannot be empty'),
    buildingCategory: z.string().min(1, 'This field cannot be empty'),
    contactPerson: z.string().refine(phoneValidator, 'Invalid phone number'),
    officeTel: z.string().refine(phoneValidator, 'Invalid phone number'),
    address: z.string().min(1, 'This field cannot be empty'),
    birthday: z.string().min(1, 'This field cannot be empty'),
};

export const createNewStaffSchema: z.ZodType<Partial<INewStaffParams>> =
    z.object({
        code: z.string().min(1, 'This field cannot be empty'),
        // phone_code: z.string(),
        email: z.string().email('Invalid email address'),
        phoneNumber: z
            .string()
            .min(1, 'This field cannot be empty')
            .max(8, 'Invalid phone')
            .regex(/^[0-9]*$/, 'Invalid phone'),
        position: z.string().min(1, 'This field cannot be empty'),
        fullName: z.string().min(1, 'This field cannot be empty'),
        address: z.string().min(1, 'This field cannot be empty'),
        // birthday: z.string().min(1, 'This field cannot be empty'),
        birthday: z.any(),
        documents: z.any(),
        certifications: z.any(),
        avatar: z.string().optional(),
    });

export const createNewCustomerWithAgentSchema = z.object({
    ...newSchema,
    contactName: z.string().min(1, 'This field cannot be empty'),
    companyName: z.string().min(1, 'This field cannot be empty'),
    designation: z.string().min(1, 'This field cannot be empty'),
    inChargeCategory: z.string().min(1, 'This field cannot be empty'),
    postalCode: z.string().min(1, 'This field cannot be empty'),
    blockNo: z.string().min(1, 'This field cannot be empty'),
    street: z.string().min(1, 'This field cannot be empty'),
    buildingName: z.string().min(1, 'This field cannot be empty'),
    agentOfficeTel: z.string().refine(phoneValidator, 'Invalid phone number'),
    agentEmail: z.string().email('Invalid email address'),
});
