import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const formStyle = flex({
    flex: 1,
    direction: 'column',
});
export const formPartsContainerStyle = flex({
    gap: '80px',
    mt: 6,
    mb: 3,
});

export const formPartStyle = flex({
    flex: 1,
    direction: 'column',
    gap: 3,
});

export const bottomContainerStyle = flex({
    justifyContent: 'flex-end',
    borderTop: '1px solid',
    borderColor: 'border.normal',
    gap: '12px',
    pt: 3,
    pb: 3,
    pl: 4,
    pr: 4,
});

export const formWrapperStyles = css({
    maxWidth: '90%',
    position: 'relative',
});

export const scrollViewStyle = css({ pr: 4, pt: 3 });
