'use client';
import Button from '@/components/globals/Button';
import { FormPart } from '@/components/globals/InputForm/FormPart';
import ScrollView from '@/components/globals/ScrollView';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { INewStaffParams, IStaff } from '@/utilities/types/entities/staff';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import {
    FieldValues,
    UseFormHandleSubmit,
    UseFormSetError,
    useForm,
} from 'react-hook-form';
import { formPartInputs } from './config';
import {
    bottomContainerStyle,
    formPartStyle,
    formPartsContainerStyle,
    formStyle,
    formWrapperStyles,
} from './style';
import { createNewStaffSchema } from './validation';
import { IFileUpload } from '@/utilities/types/entities/upload';
import { EActionTypes } from '@/utilities/types/enums/Form';
import { box } from '@/styled-system/patterns';
import { DEFAULT_PHONE_CODE } from '@/constant/staff';

type Props = {
    disabled?: boolean;
    defaultValues?: Partial<IStaff>;
    hasAgent?: boolean;
    submitText?: string;
    onSubmit?: (
        data: INewStaffParams,
        setError: UseFormSetError<FieldValues>
    ) => void;
    mode?: 'edit' | 'default';
    renderHeader?: ({
        onUpdate,
        setError,
    }: {
        setError: UseFormSetError<FieldValues>;
        onUpdate: UseFormHandleSubmit<FieldValues>;
    }) => JSX.Element;
    isActive?: boolean;
};

export default function ManageStaffForm({
    defaultValues,
    onSubmit,
    renderHeader,
    disabled,
    submitText,
    mode,
    isActive,
}: Props) {
    const router = useRouter();

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        control,
        setError,
        reset,
    } = useForm({
        defaultValues: defaultValues,
        resolver: zodResolver(createNewStaffSchema),
    });

    const onSubmitForm = (value: FieldValues) => {
        const { documents, certifications, ...rest } = value || {};
        const initialFiles = [
            ...(defaultValues?.documents?.map((item) => item.id) || []),
            ...(defaultValues?.certifications?.map((item) => item.id) || []),
        ];
        const fileIds = [
            ...(documents?.map((item: IFileUpload) => item.id) || []),
            ...(certifications?.map((item: IFileUpload) => item.id) || []),
        ];

        const removeFileIds = initialFiles.filter(
            (item) => !fileIds.includes(item)
        );
        onSubmit?.(
            {
                ...rest,
                phoneCode: DEFAULT_PHONE_CODE,
                fileIds,
                removeFileIds,
                birthday: dayjs(value.birthday).toISOString(),
            },
            setError
        );
    };

    useEffect(() => {
        reset(defaultValues);
    }, [defaultValues?.id]);

    return (
        <form className={formStyle} onSubmit={handleSubmit(onSubmitForm)}>
            <ScrollView>
                {renderHeader?.({
                    onUpdate: handleSubmit(onSubmitForm) as any,
                    setError,
                })}
                <div className={formWrapperStyles}>
                    <div className={formPartsContainerStyle}>
                        <FormPart
                            control={control}
                            inputs={formPartInputs.left}
                            errors={errors}
                            register={register}
                            setValue={setValue}
                            className={formPartStyle}
                        />
                        <FormPart
                            control={control}
                            inputs={formPartInputs.right}
                            errors={errors}
                            register={register}
                            setValue={setValue}
                            className={formPartStyle}
                            staffId={defaultValues?.id || ''}
                        />
                    </div>
                    {!isActive && (
                        <div
                            className={box({
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                backgroundColor:
                                    'rgba(255, 255, 255, 0.5)' /* Semi-transparent black overlay */,
                                zIndex: 10 /* Ensure overlay is above other content */,
                            })}
                        />
                    )}
                </div>
            </ScrollView>
            {mode !== EActionTypes['EDIT'] && (
                <div className={bottomContainerStyle}>
                    <Button
                        type="reset"
                        onClick={() => {
                            router.back();
                        }}
                        visual="outline_secondary_60"
                    >
                        Cancel
                    </Button>
                    <Button disabled={disabled}>
                        {submitText ?? 'Create'}
                    </Button>
                </div>
            )}
        </form>
    );
}
