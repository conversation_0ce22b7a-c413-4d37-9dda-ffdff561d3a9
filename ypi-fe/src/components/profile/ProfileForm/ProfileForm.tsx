'use client';

import { FormPart } from '@/components/globals/InputForm/FormPart';
import { formPartStyles } from '@/components/globals/InputForm/style';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { EFormInputType } from '@/utilities/types/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { FieldValues, useForm } from 'react-hook-form';
import { profileScheman } from './config';

import Avatar from '@/components/globals/Avatar';
import Button from '@/components/globals/Button';
import { ButtonBack } from '@/components/globals/Button/ButtonBack';
import { useProfile } from '@/hooks/auth';
import { uploadAvatar, useUpdateProfile } from '@/hooks/user';
import {
    avatarHeader,
    avatarWrapper,
    bodyWrapper,
    container,
    headerText,
    headerWrapper,
    profileWrapper,
} from './styles';
import toast from 'react-hot-toast';
import CommonMessages from '@/utilities/messages/common';

export function ProfileForm() {
    const { data: user, mutate: revalidateUser } = useProfile();
    const { trigger: updateProfile, isMutating: isUpdating } =
        useUpdateProfile();

    const onSubmit = (data: FieldValues) => {
        const { isChangePassword, password, ...payload } = data;
        toast.promise(updateProfile(isChangePassword ? data : payload), {
            loading: CommonMessages.Updating,
            success: CommonMessages.UpdatedSuccessful,
            error: (err) => err.message,
        });
    };

    const onUpdateAvatar = async (avatar?: File) => {
        if (!avatar) return;
        await uploadAvatar({ file: avatar });
        revalidateUser();
    };

    const {
        register,
        formState: { errors },
        setValue,
        control,
        watch,
        reset,
        handleSubmit,
        clearErrors,
    } = useForm({
        defaultValues: { ...user, isChangePassword: false } as FieldValues,
        resolver: zodResolver(profileScheman),
    });

    useEffect(() => {
        reset(user);
    }, [user?.id]);

    useEffect(() => {
        const isChangePassword = watch('isChangePassword');
        if (isChangePassword) return;
        clearErrors('password');
    }, [watch('isChangePassword')]);

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <div className={headerWrapper}>
                <div className={css({ flex: 1 })}>
                    <ButtonBack label={'PROFILE'} />
                </div>
                <Button
                    size="medium"
                    type="submit"
                    visual="solid_primary"
                    disabled={isUpdating}
                >
                    UPDATE
                </Button>
            </div>
            <div className={bodyWrapper}>
                <div className={avatarWrapper}>
                    <Typography
                        className={avatarHeader}
                        color="primary_100"
                        typography="header_20"
                    >
                        Avatar Profile
                    </Typography>
                    <Avatar
                        onChangeAvatar={onUpdateAvatar}
                        isCanEdit
                        src={user?.avatar}
                        size="profile"
                        isClickable={false}
                    />
                </div>
                <div className={profileWrapper}>
                    <div className={container}>
                        <Typography
                            className={headerText}
                            color="primary_100"
                            typography="header_20"
                        >
                            Information
                        </Typography>
                        <FormPart
                            control={control}
                            inputs={[
                                {
                                    id: 'Full name',
                                    name: 'fullName',
                                    label: 'Full name',
                                    type: EFormInputType.Text,
                                    variant: 'transparent',
                                    labelPosition: 'left',
                                },
                                {
                                    id: 'company',
                                    name: 'company',
                                    label: 'Company',
                                    type: EFormInputType.Text,
                                    variant: 'transparent',
                                    labelPosition: 'left',
                                },
                                {
                                    id: 'phoneNumber',
                                    name: 'phoneNumber',
                                    label: 'Phone',
                                    type: EFormInputType.Text,
                                    variant: 'transparent',
                                    labelPosition: 'left',
                                },
                                {
                                    id: 'email',
                                    name: 'email',
                                    label: 'Email',
                                    type: EFormInputType.Text,
                                    variant: 'transparent',
                                    labelPosition: 'left',
                                },

                                {
                                    type: EFormInputType.Multiple,
                                    id: 'blockPassword',
                                    inputs: [
                                        {
                                            id: 'password',
                                            name: 'password',
                                            label: 'Password',
                                            type: EFormInputType.Password,
                                            variant: 'transparent',
                                            readOnly:
                                                !watch('isChangePassword'),
                                            labelPosition: 'left',
                                            className: css({ flex: 1 }),
                                        },
                                        {
                                            id: 'isChangePassword',
                                            name: 'isChangePassword',
                                            label: 'Change password',
                                            type: EFormInputType.Checkbox,
                                            isFormPartHideCheckbox: true,
                                        },
                                    ],
                                },
                            ]}
                            className={formPartStyles({
                                container: 'table',
                            })}
                            errors={errors}
                            register={register}
                            setValue={setValue}
                        />
                    </div>
                </div>
            </div>
        </form>
    );
}
