import { z } from 'zod';

export const profileScheman = z
    .object({
        email: z.string().email('Invalid email address'),
        phoneNumber: z
            .string()
            .min(1, 'This field cannot be empty')
            .max(8, 'Invalid phone')
            .regex(/^[0-9]*$/, 'Invalid phone'),
        company: z.string(),
        fullName: z.string().min(1, 'This field cannot be empty'),
        password: z.string().optional(),
        isChangePassword: z.boolean(),
    })
    .superRefine(({ isChangePassword, password }, ctx) => {
        if (isChangePassword && password && password?.length < 6) {
            ctx.addIssue({
                code: 'custom',
                message: 'Password must be at least 6 characters',
                path: ['password'],
            });
        }
    });
