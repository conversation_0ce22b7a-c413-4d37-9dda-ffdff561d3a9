import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const container = css({
    background: 'background.40',
    px: 6,
    py: 3,
    gap: '16px',
    flexDirection: 'column',
    display: 'flex',
    borderRadius: 10,
});
export const headerText = css({
    mb: 1,
});
export const formPartStyle = flex({
    flex: 1,
    direction: 'column',
    gap: 3,
});

export const avatarWrapper = css({
    background: 'background.40',
    px: 6,
    py: 3,
    gap: 3,
    flexDirection: 'column',
    display: 'inline-flex',
    borderRadius: 10,
});

export const avatarHeader = css({ textAlign: 'center' });

export const headerWrapper = flex({ gap: 3, alignItems: 'center', mb: 6 });

export const bodyWrapper = flex({
    gap: 3,
    alignItems: 'flex-start',
});

export const profileWrapper = css({ flex: 1 });
