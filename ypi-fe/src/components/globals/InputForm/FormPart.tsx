import {
    Control,
    Controller,
    FieldErrors,
    FieldValues,
    UseFormRegister,
    UseFormSetValue,
    UseFormWatch,
} from 'react-hook-form';
import {
    EFormInputType,
    IDatePicker,
    IInputFile,
    IMultipleInputOption,
    TAvatar,
    TFormCheckbox,
    TFormInputOption,
    TMap,
    TMultiSelect,
    TRadio,
    TTag,
} from '@/utilities/types/form';
import Checkbox from '../Checkbox';
import { DatePickerBase } from '../DatePickerBase';
import Input from '../Input';
import { InputFile } from '../InputFile';
import React, { ChangeEventHandler } from 'react';
import Reload from '../Icons/Reload';
import { SingleSelect } from '../Select';
import Typography from '../Typography';
import MultiSelect from '../MultiSelect/MultiSelect';
import FormRadio from '../Radio/FormRadio';
import { InputTag } from '../InputTag';
import Map from '../Map';
import joinClassName from '@/utilities/globals/function/className';
import { center, flex } from '@/styled-system/patterns';
import { css } from '@/styled-system/css';
import { generatePassword } from '@/utilities/globals/function/password';
import Avatar from '../Avatar';
import { uploadAvatar } from '@/hooks/user';
import { allowedImageType, beforeUpload } from '../InputFile/inputFileHelper';
import { uploadFiles, uploadImages } from '@/hooks/upload';

type FormPartProps = {
    className?: string;
    title?: React.ReactNode;
    inputs: TFormInputOption[];
    errors: FieldErrors<FieldValues>;
    setValue: UseFormSetValue<FieldValues>;
    watch?: UseFormWatch<FieldValues>;
    register: UseFormRegister<FieldValues>;
    control: Control<FieldValues, any>;
    staffId?: string;
};

export function FormPart(props: FormPartProps) {
    return (
        <div className={props.className}>
            {props.title ?? null}
            {props.inputs.map((option) =>
                getInputComponent(
                    option,
                    props.errors,
                    props.setValue,
                    props.register,
                    props.control,
                    props.watch,
                    props.staffId
                )
            )}
        </div>
    );
}

function getInputComponent(
    option: TFormInputOption,
    errors: FieldErrors<FieldValues>,
    setValue: UseFormSetValue<FieldValues>,
    register: UseFormRegister<FieldValues>,
    control: Control<FieldValues, any>,
    watch?: UseFormWatch<FieldValues>,
    staffId?: string,
    weight?: number
) {
    if (option.type === EFormInputType.Multiple) {
        return (
            <div key={option.id}>
                {option.label ? (
                    <Typography typography="subtitle_16" color="primary_100">
                        {option.label}
                    </Typography>
                ) : null}
                <div
                    className={joinClassName(
                        flex({
                            gap: 3,
                            alignItems: 'flex-start',
                        }),
                        option.className || ''
                    )}
                >
                    {option.inputs.map((inputOption) =>
                        getInputComponent(
                            inputOption,
                            errors,
                            setValue,
                            register,
                            control,
                            watch,
                            staffId,
                            inputOption?.weight
                        )
                    )}
                </div>
            </div>
        );
    }

    const errorText = errors[option.name]?.message as string | undefined;
    // TODO: refactor ts

    const inputOption = option as Exclude<
        TFormInputOption,
        | IDatePicker
        | IInputFile
        | IMultipleInputOption
        | TFormCheckbox
        | TMultiSelect
        | TRadio
        | TTag
        | TMap
        | TAvatar
    >;
    const inputProps = {
        placeholder: inputOption.placeholder,
        id: inputOption.id,
        readOnly: inputOption.readOnly,
        variant: inputOption.variant,
        color: inputOption.color,
        label: inputOption.label,
        helperText: errorText,
        error: !!errorText,
        defaultValue: inputOption.defaultValue,
        startAdornment: inputOption.startAdornment,
        endAdornment: inputOption.endAdornment,
        sizes: inputOption.sizes,
        labelPosition: inputOption.labelPosition,
        className:
            inputOption.className ??
            (weight ? css({ flex: weight }) : undefined),
    };

    switch (option.type) {
        case EFormInputType.Phone: {
            return (
                <Input
                    {...inputProps}
                    type="text"
                    key={option.id}
                    autoComplete="off"
                    startAdornment={option.phoneCode}
                    {...register(option.name)}
                />
            );
        }

        case EFormInputType.Number: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, ...field }, ...rest }) => {
                        const handleChange = (
                            e: React.ChangeEvent<HTMLInputElement>
                        ) => {
                            // Remove any non-numeric characters
                            let newValue = e.target.value.replace(/\D/g, '');

                            // Add commas
                            newValue = newValue.replace(
                                /\B(?=(\d{3})+(?!\d))/g,
                                '.'
                            );

                            // Update state
                            onChange(newValue);
                        };
                        return (
                            <Input
                                {...inputProps}
                                type="text"
                                key={option.id}
                                // value={value}
                                onChange={handleChange}
                                autoComplete="off"
                                {...field}
                                {...rest}
                            />
                        );
                    }}
                />
            );
        }

        case EFormInputType.Password: {
            return (
                <Input
                    {...inputProps}
                    key={option.id}
                    type={option.autoGeneratePassword ? 'text' : 'password'}
                    autoComplete="off"
                    endAdornment={
                        option.autoGeneratePassword ? (
                            <button
                                className={center({
                                    h: 24,
                                    w: 24,
                                })}
                                type="button"
                                onClick={() => {
                                    setValue(
                                        option.name,
                                        generatePassword({
                                            minLength: 10,
                                        })
                                    );
                                }}
                            >
                                <Reload />
                            </button>
                        ) : (
                            option.endAdornment
                        )
                    }
                    {...register(option.name)}
                />
            );
        }
        case EFormInputType.Select: {
            const { ref, ...registered } = register(option.name);
            return (
                <Controller
                    key={option.id}
                    control={control}
                    {...registered}
                    render={({ field, formState }) => {
                        return (
                            <SingleSelect
                                {...inputProps}
                                ref={ref}
                                defaultValue={
                                    formState.defaultValues?.[field.name]
                                }
                                onChange={(option) => {
                                    field.onChange(option?.id ?? '');
                                }}
                                name={field.name}
                                onBlur={field.onBlur}
                                value={field.value}
                                options={option.options}
                                type="text"
                                key={option.id}
                                autoComplete="off"
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.DatePicker: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <DatePickerBase
                                {...(option as IDatePicker)}
                                selected={value}
                                onChange={onChange}
                                helperText={errorText}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.InputFile: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <InputFile
                                {...(option as IInputFile)}
                                value={value}
                                onChange={onChange}
                                staffId={staffId}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.Checkbox: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <Checkbox
                                onChange={onChange}
                                isChecked={value}
                                {...(option as TFormCheckbox)}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.MultiSelect: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <MultiSelect
                                {...option}
                                options={option?.options || []}
                                valueSelected={value}
                                onChange={onChange}
                                helperText={errorText}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.Tag: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        let errorMessage = errorText;
                        if (Array.isArray(errors[option.name])) {
                            const newErrors = errors[
                                option.name
                            ] as FieldErrors<FieldValues>;
                            if (newErrors.length) {
                                errorMessage = newErrors[0]?.message as string;
                            }
                        }
                        return (
                            <InputTag
                                {...option}
                                values={value || []}
                                onChange={onChange}
                                helperText={errorMessage}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.Radio: {
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <FormRadio
                                {...option}
                                options={option?.options || []}
                                defaultValue={value}
                                onSelected={onChange}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.Map: {
            const postalCode = watch ? watch('postalCode') : '';
            const blockNo = watch ? watch('blockNo') : '';
            const street = watch ? watch('street') : '';
            const buildingName = watch ? watch('building') : '';
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <Map
                                onChange={onChange}
                                value={value}
                                address={{
                                    postalCode,
                                    blockNo,
                                    street,
                                    buildingName,
                                }}
                                onChangeAddress={(address) => {
                                    if (address.postalCode !== postalCode) {
                                        setValue(
                                            'postalCode',
                                            address.postalCode
                                        );
                                    }
                                }}
                                {...option}
                            />
                        );
                    }}
                />
            );
        }
        case EFormInputType.Avatar:
            return (
                <Controller
                    key={option.id}
                    name={option.name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <Avatar
                                onChangeAvatar={async (file) => {
                                    if (!file) return;
                                    const res = await uploadImages({
                                        files: [file],
                                        name: 'images',
                                    });
                                    onChange(res?.data?.[0]?.link || '');
                                }}
                                isCanEdit
                                src={value}
                                size="profile"
                                isClickable={false}
                                containerClass={flex({
                                    margin: '0 auto',
                                })}
                                {...option}
                            />
                        );
                    }}
                />
            );
        case EFormInputType.Email:
        case EFormInputType.Text:
        default: {
            return (
                <Input
                    {...inputProps}
                    type="text"
                    key={option.id}
                    autoComplete="off"
                    {...register(option.name)}
                />
            );
        }
    }
}
