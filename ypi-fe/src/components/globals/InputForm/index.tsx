import { css } from '@/styled-system/css';
import { TFormInputOption } from '@/utilities/types/form';
import {
    FieldValues,
    Resolver,
    UseFormSetError,
    useForm,
} from 'react-hook-form';
import Button from '../Button';
import { FormPart } from './FormPart';
import {
    TFormPartStyle,
    errorTextStyle,
    formPartStyles,
    formStyle,
} from './style';
import joinClassName from '@/utilities/globals/function/className';
import { useEffect } from 'react';

export interface IInputFormProps {
    inputs: TFormInputOption[];
    submitText?: string;
    onSubmit?: (
        data: FieldValues,
        setError: UseFormSetError<FieldValues>
    ) => void;
    resolver?: Resolver<FieldValues, any>;
    disabled?: boolean;
    formHeader?: React.ReactNode;
    formFooter?: React.ReactNode;
    formInfo?: React.ReactNode;
    defaultValues?: Record<string, any>;
    formContainerVariant?: TFormPartStyle;
    btnSubmitStyle?: string;
}

export default function InputForm(props: IInputFormProps) {
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        setError,
        control,
        reset,
    } = useForm({
        defaultValues: props.defaultValues,
        resolver: props.resolver,
    });

    const onSubmit = (data: FieldValues) => {
        props.onSubmit?.(data, setError);
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className={formStyle}>
            {props.formHeader ?? null}
            <FormPart
                control={control}
                className={formPartStyles(props?.formContainerVariant)}
                errors={errors}
                inputs={props.inputs}
                register={register}
                setValue={setValue}
                watch={watch}
            />
            {errors.default ? (
                <div className={errorTextStyle}>
                    {errors.default.message?.toString()}
                </div>
            ) : null}
            {props.formInfo ?? null}
            <Button
                className={joinClassName(
                    css({
                        alignSelf: 'center',
                        mt: 6,
                    }),
                    props?.btnSubmitStyle || ''
                )}
                type="submit"
                disabled={props.disabled}
            >
                {props.submitText ?? 'Submit'}
            </Button>
            {props.formFooter ?? null}
        </form>
    );
}
