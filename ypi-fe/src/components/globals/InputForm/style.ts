import { RecipeVariantProps, css, cva } from '@/styled-system/css';
import { center } from '@/styled-system/patterns';

export const formStyle = css({
    // pt: 3,
    display: 'flex',
    flexDirection: 'column',
});

export const formFieldRowStyle = css({
    display: 'flex',
});

export const errorTextStyle = center({
    color: 'warning.100 !important',
    padding: '8px 0',
});

export const formPartStyles = cva({
    variants: {
        container: {
            base: { gap: '16px', display: 'flex', flexDirection: 'column' },
            table: {
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
                '& .input-label': {
                    minWidth: '100px',
                },
            },
        },
    },
    defaultVariants: {
        container: 'base',
    },
});

export type TFormPartStyle = RecipeVariantProps<typeof formPartStyles>;
