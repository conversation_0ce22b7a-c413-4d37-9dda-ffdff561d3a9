import { RecipeVariantProps, sva } from '@/styled-system/css';

import { styled } from '@/styled-system/jsx';

export const styles = sva({
    slots: ['container', 'table', 'th', 'td', 'tr'],
    base: {
        container: {
            position: 'relative',
            flex: 1,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
                display: 'none',
            },
        },
        table: {
            width: '100%',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
        },
        th: {
            p: '8px 16px',
            pl: 2,
            pr: 2,
            fontWeight: 600,
            position: 'sticky',
            top: 0,
            background: 'white',
            zIndex: 1,
        },
        td: {
            p: '9px 0',
            pl: 2,
            pr: 2,
            minHeight: 48,
            fontSize: 'body.15',
            position: 'relative',
            _before: {
                content: '""',
                display: 'block',
                position: 'absolute',
                left: 0,
                right: 0,
                background: 'background.40',
                zIndex: -1,
            },
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            clear: 'both',
            whiteSpace: 'nowrap',
        },
        tr: {
            '&:not(.disabled):hover': {
                transition: '0.2s',
                '& td:before': {
                    background: 'background.100',
                },
            },
            '&.disabled': {
                opacity: 0.25,
            },
        },
    },
    variants: {
        margin: {
            dense: {
                td: {
                    pt: '15px',
                    pb: '15px',
                    _before: {
                        top: '6px',
                        bottom: '6px',
                    },
                    _first: {
                        _before: {
                            borderTopLeftRadius: 10,
                            borderBottomLeftRadius: 10,
                        },
                    },
                    _last: {
                        _before: {
                            borderTopRightRadius: 10,
                            borderBottomRightRadius: 10,
                        },
                    },
                },
            },
            normal: {
                td: {
                    pt: '17px',
                    pb: '17px',
                    _before: {
                        top: '9px',
                        bottom: '9px',
                    },
                    _first: {
                        _before: {
                            borderTopLeftRadius: 40,
                            borderBottomLeftRadius: 40,
                        },
                    },
                    _last: {
                        _before: {
                            borderTopRightRadius: 40,
                            borderBottomRightRadius: 40,
                        },
                    },
                },
            },
        },
        layoutFixed: {
            true: {
                table: {
                    tableLayout: 'fixed',
                },
            },
        },
        rowColor: {
            normal: {},
            transparent: {
                td: {
                    _before: {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        background: 'white',
                        zIndex: -1,
                    },
                },
                tr: {
                    '&:hover': {
                        transition: '0.2s',
                        '& td:before': {
                            background: 'background.100',
                        },
                    },
                },
            },
            transparentBorder: {
                table: {
                    position: 'unset',
                },
                td: {
                    _before: {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        background: 'white',
                        zIndex: -1,
                    },
                },
                tr: {
                    borderBottom: '1px solid ',
                    borderColor: 'background.0',
                    '&:last-child': {
                        borderBottom: 'none',
                    },
                    '&:hover': {
                        '& td:before': {
                            background: 'none',
                        },
                    },
                },
            },
        },
    },
    defaultVariants: {
        margin: 'normal',
        rowColor: 'normal',
    },
});

export const TableEmptyContainer = styled('div', {
    base: {
        display: 'flex',
        flexDir: 'column',
        alignItems: 'center',
        pl: 0,
        pr: 0,
        pt: 6,
        pb: 6,
    },
});

export const TableLoadingContainer = styled('div', {
    base: {
        display: 'flex',
        flexDir: 'column',
        pt: 2,
        pb: 2,
        gap: '12px',
    },
});

export type TableVariantType = RecipeVariantProps<typeof styles>;
