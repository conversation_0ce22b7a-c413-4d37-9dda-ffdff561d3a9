import { ITabItem } from '@/utilities/types/tab';
import Typography from '../Typography';
import { useEffect, useRef, useState } from 'react';
import joinClassName from '@/utilities/globals/function/className';
import { TabsVariants, tabStyle } from './style';

type Props = {
    className?: string;
    tabs: ITabItem[];
    selectedTabKey: string;
    onChange?: (key: string) => void;
} & TabsVariants;

export default function Tabs(props: Props) {
    const rootRef = useRef<HTMLDivElement>(null);
    const [indicatorStyle, setIndicatorStyle] = useState({
        width: 0,
        left: 0,
    });

    useEffect(() => {
        const currentTabElement = rootRef.current?.querySelector(
            `#tab-${props.selectedTabKey}`
        );
        const rootLeft = rootRef.current?.getBoundingClientRect()?.left ?? 0;
        const currentTabBounding = currentTabElement?.getBoundingClientRect();

        setIndicatorStyle({
            width: currentTabBounding?.width ?? 0,
            left: (currentTabBounding?.left ?? 0) - rootLeft,
        });
    }, [props.selectedTabKey, props.tabs.length]);

    const classNames = tabStyle({
        colors: props.colors,
        fullWidth: props.fullWidth,
    });

    return (
        <div
            ref={rootRef}
            className={joinClassName(classNames.tabs, props.className)}
        >
            {props.tabs.map((item) => {
                const isSelected = props.selectedTabKey === item.key;
                return (
                    <div
                        onClick={() => props.onChange?.(item.key)}
                        className={joinClassName(
                            classNames.tabItem,
                            isSelected ? 'selected' : undefined
                        )}
                        key={item.key}
                        id={`tab-${item.key}`}
                    >
                        {item.title}
                    </div>
                );
            })}
            <span className={classNames.indicator} style={indicatorStyle} />
        </div>
    );
}
