import { sva } from '@/styled-system/css';
import { RecipeVariantProps } from '@/styled-system/types';

export const tabStyle = sva({
    slots: ['tabs', 'tabItem', 'indicator'],
    base: {
        tabs: {
            gap: 3,
            position: 'relative',
            display: 'flex',
        },
        tabItem: {
            pb: 1,
            pt: 1,
            cursor: 'pointer',
            color: 'secondary.100',
            fontSize: 'body.15',
            '&.selected': {
                fontWeight: 'semiBold',
            },
        },
        indicator: {
            height: 2,
            position: 'absolute',
            bottom: 0,
            display: 'block',
            transition: 'all 300ms ease-in-out',
        },
    },
    variants: {
        fullWidth: {
            true: {
                tabItem: {
                    flexGrow: 1,
                },
            },
        },
        colors: {
            primary: {
                indicator: {
                    backgroundColor: 'primary.100',
                },
                tabItem: {
                    '&.selected': {
                        color: 'primary.100',
                    },
                },
            },
        },
    },
    defaultVariants: {
        colors: 'primary',
    },
});

export type TabsVariants = RecipeVariantProps<typeof tabStyle>;
