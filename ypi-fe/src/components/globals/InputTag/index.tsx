import { useId } from 'react';
import ChipClearable from '../ChipClearable/ChipClearable';
import joinClassName from '@/utilities/globals/function/className';
import { InputTagVariant, multiSelectStyle } from './style';
import TagsInput from 'react-tagsinput';
import { css } from '@/styled-system/css';

export type TInputTagProps = {
    values: string[];
    onChange: (value: string[]) => void;
    label?: string;
    className?: string;
    helperText?: string;
} & InputTagVariant;

export function InputTag({
    labelPosition,
    label,
    onChange,
    values,
    className,
    helperText,
}: TInputTagProps) {
    const id = useId();
    const classes = multiSelectStyle({ labelPosition });

    return (
        <div className={joinClassName(classes.container, className)}>
            {label && (
                <label
                    className={joinClassName(classes.label, 'input-label')}
                    htmlFor={id}
                >
                    {label}
                </label>
            )}
            <div className={css({ w: '90%' })}>
                <TagsInput
                    value={values}
                    onChange={onChange}
                    inputProps={{
                        placeholder: '',
                    }}
                    addOnBlur
                    renderTag={(props) => {
                        const { tag, key, onRemove } = props;
                        return (
                            <ChipClearable
                                key={key}
                                label={tag}
                                value={tag}
                                onRemove={() => onRemove(key)}
                                className={classes.chip}
                            />
                        );
                    }}
                />
                {helperText ? (
                    <span className={classes.helperText}>{helperText}</span>
                ) : null}
            </div>
        </div>
    );
}
