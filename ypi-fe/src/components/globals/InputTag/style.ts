import { RecipeVariantProps, sva } from '@/styled-system/css';

export const multiSelectStyle = sva({
    slots: ['container', 'label', 'chip', 'helperText'],
    base: {
        container: {
            gap: '12px',
            '& .react-tagsinput': {
                background: 'background.40 !important',
                border: 'none !important',
                borderRadius: '40px',
                padding: '0 18px !important',
            },
            '& .react-tagsinput-input': {
                outline: 'none',
                width: '300px',
                minHeight: '42px',
                margin: '0 !important',
                padding: '0 !important',
                background: 'unset !important',
            },
        },
        chip: {
            margin: '10px 5px 10px 0',
        },
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
            width: 100,
        },
        helperText: {
            fontFamily: 'inherit',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
            color: 'warning.100',
        },
    },
    variants: {
        labelPosition: {
            top: {},
            left: {
                container: {
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'row',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
    },
    defaultVariants: {
        labelPosition: 'left',
    },
});

export type InputTagVariant = RecipeVariantProps<typeof multiSelectStyle>;
