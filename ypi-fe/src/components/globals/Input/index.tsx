'use client';
import React from 'react';
import { inputStyle, InputVariants } from '@/components/globals/Input/style';
import { Eye, EyeOff } from '@/components/globals/Icons';
import { css } from '@/styled-system/css';
import joinClassName from '@/utilities/globals/function/className';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    helperText?: string;
    error?: boolean;
    label?: string;
    placeholder?: string;
    children?: React.ReactNode;
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
}

// eslint-disable-next-line react/display-name
const Input = React.forwardRef<HTMLInputElement, InputProps & InputVariants>(
    (props, ref) => {
        const {
            startAdornment,
            endAdornment,
            helperText,
            color,
            error,
            className,
            sizes,
            labelPosition,
            ...rest
        } = props;
        const classes = inputStyle({
            color: error ? 'error' : color,
            variant: props.variant,
            sizes,
            labelPosition,
        });

        const [hidePassword, setHidePassword] = React.useState<boolean>(true);
        const [types, setTypes] = React.useState<string>(props.type || 'text');

        return (
            <div className={joinClassName(classes.root, className)}>
                {props.label && (
                    <span
                        className={joinClassName(classes.label, 'input-label')}
                    >
                        {props.label}
                    </span>
                )}
                <div
                    className={css({
                        display: 'flex',
                        flexDirection: 'column',
                        flex: 1,
                    })}
                >
                    <label className={classes.input} htmlFor={props.id}>
                        {startAdornment ?? null}
                        <div className={classes.inputWrapper}>
                            <input
                                {...rest}
                                type={types}
                                id={props.id}
                                ref={ref}
                            />
                        </div>
                        {props.type === 'password' ? (
                            <span
                                className={css({
                                    cursor: 'default',
                                    zIndex: 10,
                                })}
                                onClick={() => {
                                    setTypes(
                                        hidePassword ? 'text' : 'password'
                                    );
                                    setHidePassword(!hidePassword);
                                }}
                            >
                                {hidePassword ? <EyeOff /> : <Eye />}
                            </span>
                        ) : null}
                        {props.children}
                        {endAdornment ?? null}
                    </label>
                    {helperText ? (
                        <span className={classes.helperText}>{helperText}</span>
                    ) : null}
                </div>
                {/*{props.errormessage && <span className={classes.error_message}>{props.errormessage}</span>}*/}
            </div>
        );
    }
);

export default Input;

export type { InputVariants };
