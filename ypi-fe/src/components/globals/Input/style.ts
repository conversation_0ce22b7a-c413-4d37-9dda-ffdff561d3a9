import { sva, RecipeVariantProps } from '@/styled-system/css';

export const inputStyle = sva({
    slots: ['root', 'input', 'helperText', 'label', 'inputWrapper'],
    base: {
        root: {
            display: 'flex',
            gap: '12px',
        },
        input: {
            width: 'auto',
            minHeight: '42px',
            fontFamily: 'inherit',
            cursor: 'text',
            display: 'flex',
            alignItems: 'center',
            padding: '0 18px',
            color: 'secondary.60',
            fontSize: 'body.15',
            fontWeight: 'regular',
            borderRadius: '40px',
            flex: 1,
            gap: 1,
        },
        helperText: {
            fontFamily: 'inherit',
            opacity: '0',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
            color: 'warning.100',
        },
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
        inputWrapper: {
            flex: 1,
            '& input': {
                width: '100%',
                minWidth: 0,
                padding: '0',
                background: 'transparent',
                border: 'none',
                outline: 'none',
                color: 'inherit',
                fontSize: 'inherit',
                fontWeight: 'inherit',
                '&::placeholder': {
                    color: 'secondary.60',
                },
                '&[readonly]': {
                    backgroundColor: 'white',
                    opacity: 0.25,
                },
            },
        },
    },
    variants: {
        labelPosition: {
            top: {
                root: {
                    flexDirection: 'column',
                },
            },
            left: {
                root: {
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: 'row',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
        variant: {
            contained: {
                input: {
                    bg: 'background.40',
                    color: 'secondary.100',
                    '&::placeholder': {
                        color: 'secondary.60',
                    },
                    '& input': {
                        border: 0,
                        bg: 'background.40',
                        lineHeight: 1,
                    },
                },
            },
            transparent: {
                input: {
                    color: 'secondary.100',
                    '&::placeholder': {
                        color: 'secondary.60',
                    },
                    background: 'white',
                },
            },
            outline: {
                input: {
                    borderStyle: 'solid',
                    borderWidth: 1,
                    color: 'secondary.100',
                    '&::placeholder': {
                        color: 'secondary.60',
                    },
                },
            },
            // filled_inline: {
            //     root: {
            //         display: 'flex',
            //         alignItems: 'center',
            //         flexDirection: 'row',
            //         gap: '12px',
            //     },
            //     input: {
            //         borderColor: 'transparent !important',
            //         background: 'background.40',
            //         // padding: '12px 18px',
            //         color: 'secondary.100',
            //         '&::placeholder': {
            //             color: 'secondary.60',
            //         },
            //     },
            //     label: {
            //         width: 90,
            //         alignSelf: 'flex-start',
            //         marginTop: 12,
            //     },
            //     helperText: {
            //         position: 'initial',
            //         marginTop: 1,
            //     },
            // },
            underline: {
                input: {
                    borderWidth: '0 !important',
                    borderRadius: 0,
                    borderColor: 'secondary.100 !important',
                    borderBottomWidth: '2px !important',
                    background: 'transparent',
                    padding: '12px',
                    color: 'secondary.100',
                    _placeholder: {
                        color: 'secondary.60',
                    },
                },
            },
        },
        color: {
            default: {
                input: {
                    borderColor: 'border.normal',
                },
                helperText: {
                    opacity: '1',
                    color: 'body.1',
                },
            },
            secondary: {
                input: {
                    borderColor: 'border.field',
                },
                helperText: {
                    opacity: '1',
                    color: 'body.1',
                },
            },
            error: {
                input: {
                    borderColor: 'warning.100 !important',
                },
                helperText: {
                    opacity: '1',
                    color: 'warning.100',
                },
            },
        },
        sizes: {
            xs: {
                input: { minW: '96px' },
            },
            sm: {
                input: { minW: '154px' },
            },
        },
    },
    defaultVariants: {
        color: 'default',
        variant: 'outline',
        labelPosition: 'top',
    },
});

export type InputVariants = RecipeVariantProps<typeof inputStyle>;
