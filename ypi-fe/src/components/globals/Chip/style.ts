import { cva } from '@/styled-system/css';
import { styled } from '@/styled-system/jsx';
import { RecipeVariantProps } from '@/styled-system/types';

const style = cva({
    base: {
        display: 'flex',
        width: 210,
        height: 34,
        alignItems: 'center',
        padding: '8px 12px',
        borderRadius: 40,
        overflow: 'hidden',
    },
    variants: {
        colors: {
            low: {
                backgroundColor: 'yellow.100',
            },
            intermediate: {
                backgroundColor: 'success.100',
            },
            high: {
                backgroundColor: 'primary.100',
            },
        },
    },
});

export const Root = styled('div', style);

export type ChipVariants = RecipeVariantProps<typeof style>;
