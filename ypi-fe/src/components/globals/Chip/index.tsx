import React from 'react';
import { Root, ChipVariants } from './style';
import Typography from '../Typography';
import { css } from '@/styled-system/css';

type Props = {
    label: React.ReactNode;
    value: React.ReactNode;
} & ChipVariants;

export default function Chip(props: Props) {
    return (
        <Root colors={props.colors}>
            <Typography
                className={css({ flex: 1 })}
                typography="body_15"
                color="white"
            >
                {props.label}
            </Typography>
            <Typography typography="header_18" color="white">
                {props.value}
            </Typography>
        </Root>
    );
}
