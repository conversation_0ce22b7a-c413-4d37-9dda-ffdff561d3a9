import { flex } from '@/styled-system/patterns';
import Button from '../Button';
import Modal from '../Modal';
import Typography from '../Typography';

type Props = {
    title?: string;
    message?: string;
    confirmText?: string;
    cancelText?: string;
    open: boolean;
    onCancel: () => void;
    onConfirm?: () => void;
};

export default function ConfirmModal(props: Props) {
    return (
        <Modal
            isOpen={props.open}
            onClose={props.onCancel}
            sizes="sm"
            title={props.title}
        >
            <Typography typography="body_16">{props.message}</Typography>
            <div
                className={flex({
                    justifyContent: 'flex-end',
                    gap: 1,
                    mt: 6,
                })}
            >
                <Button onClick={props.onCancel} visual="outline_primary">
                    {props.cancelText ?? 'Cancel'}
                </Button>
                <Button onClick={props.onConfirm} visual="solid_primary">
                    {props.confirmText ?? 'Confirm'}
                </Button>
            </div>
        </Modal>
    );
}
