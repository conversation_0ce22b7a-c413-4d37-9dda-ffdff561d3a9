import { cva } from '@/styled-system/css';

export const iconButtonStyle = cva({
    base: {
        borderRadius: '50%',
        display: 'inline-flex',
        justifyContent: 'center',
        alignItems: 'center',
        _hover: {
            backgroundColor:
                'color-mix(in srgb, currentColor 10%, transparent)',
        },
    },
    variants: {
        size: {
            small: {
                minW: 24,
                minH: 24,
                p: '4px',
            },
            normal: {
                minW: 32,
                minH: 32,
                p: '4px',
            },
            large: {
                minW: 48,
                minH: 48,
                p: 1,
            },
        },
    },
});
