'use client';
import React, { FC, useState } from 'react';
import radioStyle, { RadioVariant } from '@/components/globals/Radio/style';
import joinClassName from '@/utilities/globals/function/className';

export interface RadioOption {
    label: string;
    value: string;
}

export interface RadioButtonProps {
    options: RadioOption[];
    defaultValue?: string;
    onSelected?: (value: string) => void;
    className?: string;
}

const RadioButton: FC<RadioButtonProps & RadioVariant> = ({
    options,
    defaultValue = '',
    onSelected,
    className,
}) => {
    const classes = radioStyle();
    const [selectedValue, setSelectedValue] = useState(defaultValue);

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedValue(event.target.value);
        onSelected?.(event.target.value);
    };

    return (
        <div className={joinClassName(classes.root, className)}>
            {options.map((option) => (
                <label key={option.value} className={classes.radio}>
                    <input
                        type="radio"
                        value={option.value}
                        checked={selectedValue === option.value}
                        onChange={handleChange}
                    />
                    <span>{option.label}</span>
                </label>
            ))}
        </div>
    );
};

export default RadioButton;
