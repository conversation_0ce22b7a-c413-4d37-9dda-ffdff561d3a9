import * as React from 'react';
import RadioButton, { RadioButtonProps } from '.';
import { formRadioStyle, RadioFormVariant } from './style';
import joinClassName from '@/utilities/globals/function/className';

export interface FormRadioProps extends RadioButtonProps {
    label?: string;
    helperText?: string;
    classNameWrapper?: string;
}

export default function FormRadio({
    options,
    defaultValue,
    onSelected,
    label,
    helperText,
    className: classNameRadios,
    classNameWrapper,
    labelPosition,
}: FormRadioProps & RadioFormVariant) {
    const id = React.useId();
    const classes = formRadioStyle({ labelPosition });
    return (
        <div className={joinClassName(classes.root, classNameWrapper)}>
            {label && (
                <label className={classes.label} htmlFor={id}>
                    {label}
                </label>
            )}
            <RadioButton
                options={options}
                defaultValue={defaultValue}
                onSelected={onSelected}
                className={classNameRadios}
            />
            {helperText ? (
                <span className={classes.helperText}>{helperText}</span>
            ) : null}
        </div>
    );
}
