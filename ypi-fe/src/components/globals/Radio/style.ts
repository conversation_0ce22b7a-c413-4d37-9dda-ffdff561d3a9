import { sva, RecipeVariantProps } from '@/styled-system/css';

const radioStyle = sva({
    slots: ['root', 'radio'],
    base: {
        root: {
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
        },
        radio: {
            cursor: 'pointer',
            '& span': {
                display: 'flex',
                gap: '12px',
                userSelect: 'none',
                alignItems: 'center',
                fontFamily: 'inherit',
                position: 'relative',
                fontSize: 'body.16',
                fontWeight: 'regular',
            },
            '& input[type=radio]': {
                display: 'none',
            },
            '& span:before': {
                content: '""',
                display: 'block',
                borderRadius: '50%',
                position: 'relative',
                width: '20px',
                height: '20px',
                border: '1px solid',
                borderColor: 'secondary.60',
                transition: 'all 0.2s ease-in-out',
                zIndex: '0',
            },
            '& span:after': {
                top: '50%',
                left: '3.5px',
                position: 'absolute',
                transform: 'translateY(-50%)',
                content: '""',
                display: 'block',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                zIndex: '1',
                background: 'white',
                transition: 'all 0.2s ease-in-out',
            },
            '& input[type=radio]:checked + span:before': {
                borderColor: 'primary.100',
                transition: 'all 0.2s ease-in-out',
            },
            '& input[type=radio]:checked + span:after': {
                top: '50%',
                left: '4px',
                position: 'absolute',
                transform: 'translateY(-50%)',
                content: '""',
                display: 'block',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                zIndex: '1',
                background: 'primary.100',
            },
        },
    },
});

export const formRadioStyle = sva({
    slots: ['root', 'label', 'helperText'],
    base: {
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
        helperText: {
            fontFamily: 'inherit',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
            color: 'warning.100',
        },
    },
    variants: {
        labelPosition: {
            top: {},
            left: {
                root: {
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'row',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
    },
    defaultVariants: {
        labelPosition: 'top',
    },
});

export default radioStyle;
export type RadioFormVariant = RecipeVariantProps<typeof formRadioStyle>;

export type RadioVariant = RecipeVariantProps<typeof radioStyle>;
