import { sva } from '@/styled-system/css';

export const popoverStyles = sva({
    slots: ['root', 'container', 'backdrop'],
    base: {
        root: {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,

            '&.popover-enter .container': {
                opacity: 0,
                transform: 'translateY(-16px)',
            },
            '&.popover-enter-active .container': {
                opacity: 1,
                transform: 'translate(0)',
                transition: 'opacity 210ms, transform 210ms',
            },
            '&.popover-exit .container': {
                opacity: 1,
                transform: 'translate(0)',
            },
            '&.popover-exit-active .container': {
                opacity: 0,
                transform: 'translateY(-16px)',
                transition: 'opacity 210ms, transform 210ms',
            },
        },
        backdrop: {
            width: '100%',
            height: '100%',
        },
        container: {
            position: 'absolute',
            maxHeight: 350,
        },
    },
});
