import React, { useEffect, useRef, useState } from 'react';
import { CSSTransition } from 'react-transition-group';
import { popoverStyles } from './style';
import joinClassName from '@/utilities/globals/function/className';

type Props = {
    open: boolean;
    onClose?: () => void;
    children?: React.ReactNode;
    anchorEl?: HTMLElement | null;
};

enum EDirection {
    T2B = 0,
    B2T = 1,
}

export default function Popover(props: Props) {
    const rootRef = useRef<HTMLDivElement>(null);
    const classNames = popoverStyles();
    const [containerStyle, setContainerStyle] = useState<
        React.CSSProperties | undefined
    >();

    useEffect(() => {
        const rect = props.anchorEl?.getBoundingClientRect();
        if (props.open) {
            setContainerStyle(getContainerStyle(rect));
        }
    }, [props.open]);

    return (
        <CSSTransition
            classNames="popover"
            unmountOnExit
            timeout={{
                enter: 210,
                exit: 210,
            }}
            in={props.open}
            nodeRef={rootRef}
        >
            <div role="presentation" ref={rootRef} className={classNames.root}>
                <div
                    aria-hidden
                    onClick={() => props.onClose?.()}
                    className={classNames.backdrop}
                />
                <div
                    tabIndex={-1}
                    className={joinClassName(classNames.container, 'container')}
                    style={containerStyle}
                >
                    {props.children}
                </div>
            </div>
        </CSSTransition>
    );
}

function getDirection(anchorRect?: DOMRect) {
    if (!anchorRect) {
        return EDirection.T2B;
    }
    const mt = anchorRect.top;
    const mb = innerHeight - anchorRect.bottom;

    if (mb < 300 && mt > mb) {
        return EDirection.B2T;
    }
    return EDirection.T2B;
}

function getContainerStyle(anchorRect?: DOMRect) {
    const direction = getDirection(anchorRect);
    if (direction === EDirection.T2B) {
        return {
            top: anchorRect?.bottom,
            left: anchorRect?.left,
            width: anchorRect?.width,
            transformOrigin: 'top center',
        };
    }
    return {
        bottom: innerHeight - (anchorRect?.top ?? 0),
        left: anchorRect?.left,
        width: anchorRect?.width,
        transformOrigin: 'bottom center',
    };
}
