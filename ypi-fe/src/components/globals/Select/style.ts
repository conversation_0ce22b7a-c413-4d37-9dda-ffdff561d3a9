import { RecipeVariantProps, sva } from '@/styled-system/css';
import { styled } from '@/styled-system/jsx';

export const singleSelectStyles = sva({
    slots: [
        'root',
        'container',
        'select',
        'input',
        'indicator',
        'label',
        'helperText',
        'menu',
        'menuList',
        'option',
        'value',
        'valueContainer',
    ],
    base: {
        root: {
            fontSize: 'body.15',
            color: 'inherit',
            display: 'flex',
            gap: '12px',
        },
        container: {
            display: 'flex',
            flexDirection: 'column',
            flex: 1,
        },
        menu: {
            backgroundColor: 'background.40',
            borderRadius: 10,
            boxShadow: '1px 4px 4px 0 rgba(0,0,0,0.15)',
            fontSize: 'body.15',
            mt: 1,
            mb: 1,
            maxH: 334,
            pt: 1,
            pb: 1,
            display: 'flex',
        },
        menuList: {
            flex: 1,
            overflow: 'auto',
        },
        option: {
            '&.selected': {
                color: 'primary.100',
                fontWeight: 'semiBold',
                _hover: {
                    color: 'primary.80',
                },
            },
            '& .option-text': {
                flex: 1,
            },
            _focus: {
                outline: 'none',
                border: 'none',
                backgroundColor: 'primary.20',
            },
        },
        select: {
            minHeight: '42px',
            borderRadius: 40,
            borderStyle: 'solid',
            borderWidth: 1,
            borderColor: 'border.normal',
            cursor: 'pointer',
            display: 'flex',
        },
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
        value: {
            left: '12px',
            right: '12px',
            top: '12px',
            bottom: '12px',
            lineHeight: 1,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            position: 'absolute',
        },
        valueContainer: {
            position: 'relative',
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
        },
        input: {
            position: 'absolute',
            maxW: 0,
            maxH: 0,
            zIndex: -999,
            opacity: 0,
        },
        indicator: {
            w: 32,
            display: 'flex',
            alignItems: 'center',
            opacity: 0.5,
        },
        helperText: {
            fontFamily: 'inherit',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
        },
    },
    variants: {
        open: {
            true: {
                indicator: {
                    opacity: 1,
                },
            },
        },
        sizes: {
            xs: {
                select: {
                    minW: 96,
                },
            },
            sm: {
                select: {
                    minW: 154,
                },
            },
        },
        variant: {
            contained: {
                select: {
                    borderColor: 'background.40',
                    backgroundColor: 'background.40',
                },
            },
            outline: {
                select: {
                    borderStyle: 'solid',
                    borderWidth: 1,
                    borderColor: 'border.normal',
                },
            },
            transparent: {},
            underline: {},
        },
        labelPosition: {
            top: {
                root: {
                    flexDirection: 'column',
                },
            },
            left: {
                root: {
                    flexDir: 'row',
                    alignItems: 'center',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
        color: {
            default: {},
            error: {
                select: {
                    borderColor: 'warning.100',
                },
                helperText: {
                    color: 'warning.100',
                },
            },
            secondary: {},
            primary: {
                select: {
                    borderColor: 'primary.100',
                    color: 'primary.100',
                },
                value: {
                    color: 'primary.100',
                },
            },
        },
        strong: {
            true: {
                value: {
                    fontWeight: 'semiBold',
                    textAlign: 'center',
                },
            },
        },
    },
    defaultVariants: {
        labelPosition: 'top',
        variant: 'outline',
    },
});

export const SingleOption = styled('li', {
    base: {
        display: 'flex',
        alignItems: 'center',
        padding: '0 18px',
        color: 'body.1',
        height: 42,
        cursor: 'default',
        _hover: {
            color: 'primary.60',
        },
    },
    variants: {
        selected: {
            true: {
                color: 'primary.100',
                fontWeight: 'semiBold',
                _hover: {
                    color: 'primary.80',
                },
                '& .option-text': {
                    flex: 1,
                },
            },
        },
    },
});

export const multiSelectStyles = sva({
    slots: [
        'root',
        'container',
        'select',
        'input',
        'indicator',
        'label',
        'helperText',
        'menu',
        'menuList',
        'option',
        'value',
        'valueContainer',
    ],
    base: {
        root: {
            fontSize: 'body.15',
            color: 'inherit',
            display: 'flex',
            gap: '12px',
        },
        container: {
            display: 'flex',
            flexDirection: 'column',

            flex: 1,
        },
        menu: {
            backgroundColor: 'background.40',
            borderRadius: 10,
            boxShadow: '1px 4px 4px 0 rgba(0,0,0,0.15)',
            fontSize: 'body.15',
            mt: 1,
            mb: 1,
            maxH: 334,
            pt: 1,
            pb: 1,
            display: 'flex',
        },
        menuList: {
            flex: 1,
            overflow: 'auto',
        },
        option: {
            '&.selected': {
                color: 'primary.100',
                fontWeight: 'semiBold',
                _hover: {
                    color: 'primary.80',
                },
            },
            '& .option-text': {
                flex: 1,
            },
            _focus: {
                outline: 'none',
                border: 'none',
                backgroundColor: 'primary.20',
            },
        },
        select: {
            minHeight: '42px',
            borderRadius: 40,
            borderStyle: 'solid',
            borderWidth: 1,
            borderColor: 'border.normal',
            cursor: 'pointer',
            display: 'flex',
        },
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
        value: {
            left: '12px',
            right: '12px',
            top: '12px',
            bottom: '12px',
            lineHeight: 1,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            position: 'absolute',
        },
        valueContainer: {
            position: 'relative',
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            pl: '18px',
        },
        input: {
            position: 'absolute',
            maxW: 0,
            maxH: 0,
            zIndex: -999,
            opacity: 0,
        },
        indicator: {
            w: 32,
            display: 'flex',
            alignItems: 'center',
            opacity: 0.5,
        },
        helperText: {
            fontFamily: 'inherit',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
        },
    },
    variants: {
        open: {
            true: {
                indicator: {
                    opacity: 1,
                },
            },
        },
        sizes: {
            xs: {
                select: {
                    minW: 96,
                },
            },
            sm: {
                select: {
                    minW: 154,
                },
            },
        },
        variant: {
            contained: {
                select: {
                    borderColor: 'background.40',
                    backgroundColor: 'background.40',
                },
            },
            outline: {
                select: {
                    borderStyle: 'solid',
                    borderWidth: 1,
                    borderColor: 'border.normal',
                },
            },
            transparent: {},
            underline: {},
        },
        labelPosition: {
            top: {
                root: {
                    flexDirection: 'column',
                },
            },
            left: {
                root: {
                    flexDir: 'row',
                    alignItems: 'center',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
        color: {
            default: {},
            error: {
                select: {
                    borderColor: 'warning.100',
                },
                helperText: {
                    color: 'warning.100',
                },
            },
            secondary: {},
            primary: {
                select: {
                    borderColor: 'primary.100',
                    color: 'primary.100',
                },
                value: {
                    color: 'primary.100',
                },
            },
        },
        strong: {
            true: {
                value: {
                    fontWeight: 'semiBold',
                    textAlign: 'center',
                },
            },
        },
    },
    defaultVariants: {
        labelPosition: 'top',
        variant: 'outline',
    },
});

export const MultiOption = styled('li', {
    base: {
        display: 'flex',
        alignItems: 'center',
        padding: '0 18px',
        color: 'body.1',
        height: 42,
        cursor: 'pointer',
        gap: '1',
        _hover: {
            color: 'primary.60',
        },
    },
    variants: {
        selected: {
            true: {
                color: 'primary.100',
                fontWeight: 'semiBold',
                _hover: {
                    color: 'primary.80',
                },
                '& .option-text': {
                    flex: 1,
                },
            },
        },
    },
});

export type SingleVariantProps = RecipeVariantProps<typeof singleSelectStyles>;
