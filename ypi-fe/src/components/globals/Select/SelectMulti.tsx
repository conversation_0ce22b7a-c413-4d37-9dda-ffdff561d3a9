import { useRef, useState } from 'react';
import Popover from '../Popover';
import {
    MultiOption,
    SingleVariantProps,
    multiSelectStyles,
    singleSelectStyles,
} from './style';
import ChevronDown from '../Icons/ChevronDown';
import React from 'react';
import { FieldValues, UseFormSetValue } from 'react-hook-form';
import joinClassName from '@/utilities/globals/function/className';
import { IOption } from '@/utilities/types/form';
import Checkbox from '../Checkbox';
import SearchInput from '../SearchInput';
import ChipClearable from '../ChipClearable/ChipClearable';
import { flex } from '@/styled-system/patterns';
import PlusCircle from '../Icons/PlusCircle';
import PlusCircleOutline from '../Icons/PlusCircleOutline';

type Props = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> & {
    options?: IOption[];
    value?: string[] | null;
    defaultValue?: string[] | null;
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
    helperText?: string;
    error?: boolean;
    label?: string;
    setValue?: UseFormSetValue<FieldValues>;
    onChange?: (newValue?: string[]) => void;
    chipClearable?: boolean;
} & SingleVariantProps;

const SelectMulti = React.forwardRef<HTMLInputElement, Props>((props, ref) => {
    const anchorElRef = useRef<HTMLLabelElement | null>(null);
    const [open, setOpen] = useState(false);
    const {
        startAdornment: _startAdornment,
        endAdornment: _endAdornment = <PlusCircleOutline />,
        helperText,
        color,
        error,
        options,
        className,
        onChange,
        variant: _variant,
        labelPosition: _labelPosition,
        defaultValue,
        sizes: _sizes,
        strong: _strong,
        onFocus,
        onBlur,
        chipClearable = true,
        ...rest
    } = props;
    const classNames = multiSelectStyles({
        ...props,
        color: error ? 'error' : color,
        open,
    });

    const removeOption = (optionId: string) => {
        const newValues = props.value?.filter((opt) => opt !== optionId);
        onChange?.(newValues);
    };
    const optionsSelected = options?.filter((item) =>
        props.value?.includes(item.id)
    );
    const [searchOption, setSearchOption] = useState('');

    const close = () => {
        setOpen(false);
        setSearchOption('');
    };

    return (
        <>
            <div className={joinClassName(classNames.root, className)}>
                {props.label && (
                    <span
                        className={joinClassName(
                            classNames.label,
                            'input-label'
                        )}
                    >
                        {props.label}
                    </span>
                )}
                <div className={classNames.container}>
                    <label
                        ref={anchorElRef}
                        className={classNames.select}
                        htmlFor={props.id}
                    >
                        <div className={classNames.valueContainer}>
                            {optionsSelected?.map((item) => (
                                <ChipClearable
                                    key={item.id}
                                    label={item.name || ''}
                                    value={item.id}
                                    onRemove={(value) => removeOption(value)}
                                    clearable={chipClearable}
                                />
                            ))}
                        </div>
                        <div className={classNames.indicator}>
                            {_endAdornment}
                        </div>
                        <input
                            className={classNames.input}
                            value={props.value}
                            role="combobox"
                            tabIndex={-1}
                            aria-haspopup="listbox"
                            aria-expanded={true}
                            aria-controls={`${props.id}-listbox`}
                            aria-activedescendant={`${props.id}-item-0`}
                            onChange={() => {}}
                            // onBlur={(e) => {
                            //     onBlur?.(e);
                            //     if (e.defaultPrevented) {
                            //         return;
                            //     }
                            //     setOpen(false);
                            // }}
                            onFocus={(e) => {
                                onFocus?.(e);
                                if (e.defaultPrevented) {
                                    return;
                                }
                                setOpen(true);
                            }}
                            ref={ref}
                            {...rest}
                        />
                    </label>
                    {helperText ? (
                        <span className={classNames.helperText}>
                            {helperText}
                        </span>
                    ) : null}
                </div>
                <Popover
                    open={open}
                    anchorEl={anchorElRef.current}
                    onClose={close}
                >
                    <div className={classNames.menu}>
                        <ul
                            className={classNames.menuList}
                            id={`${props.id}-listbox`}
                            role="listbox"
                        >
                            <SearchInput onChange={(s) => setSearchOption(s)} />

                            {props.options
                                ?.filter((item) =>
                                    item.name
                                        ?.toLocaleLowerCase()
                                        ?.includes(
                                            searchOption.toLocaleLowerCase()
                                        )
                                )
                                ?.map((option, idx) => {
                                    const selected = props.value?.includes(
                                        option.id
                                    );
                                    return (
                                        <MultiOption
                                            key={option.id}
                                            id={`${props.id}-item-${idx}`}
                                            role="option"
                                            aria-selected={false}
                                            selected={selected}
                                            onMouseDown={() => {
                                                if (selected) {
                                                    removeOption(option.id);
                                                    return;
                                                }
                                                const newValues = [
                                                    ...(props?.value || []),
                                                    option.id,
                                                ];
                                                onChange?.(newValues);
                                            }}
                                        >
                                            <Checkbox
                                                size="small"
                                                isChecked={selected}
                                            />
                                            <div className="option-text">
                                                {option.name}
                                            </div>
                                        </MultiOption>
                                    );
                                })}
                        </ul>
                    </div>
                </Popover>
            </div>
        </>
    );
});

export default SelectMulti;
