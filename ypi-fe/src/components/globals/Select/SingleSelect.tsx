import { useRef, useState } from 'react';
import Popover from '../Popover';
import { SingleOption, SingleVariantProps, singleSelectStyles } from './style';
import CheckOutline from '../Icons/CheckOutline';
import ChevronDown from '../Icons/ChevronDown';
import React from 'react';
import { FieldValues, UseFormSetValue } from 'react-hook-form';
import joinClassName from '@/utilities/globals/function/className';
import { IOption } from '@/utilities/types/form';

type Props = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> & {
    options?: IOption[];
    value?: string | null;
    defaultValue?: string | null;
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
    helperText?: string;
    error?: boolean;
    label?: string;
    setValue?: UseFormSetValue<FieldValues>;
    onChange?: (newValue?: IOption) => void;
} & SingleVariantProps;

const SingleSelect = React.forwardRef<HTMLInputElement, Props>((props, ref) => {
    const anchorElRef = useRef<HTMLLabelElement | null>(null);
    const [open, setOpen] = useState(false);
    const {
        startAdornment: _startAdornment,
        endAdornment: _endAdornment,
        helperText,
        color,
        error,
        options,
        className,
        onChange,
        variant: _variant,
        labelPosition: _labelPosition,
        defaultValue,
        sizes: _sizes,
        strong: _strong,
        onFocus,
        onBlur,
        ...rest
    } = props;
    const classNames = singleSelectStyles({
        ...props,
        color: error ? 'error' : color,
        open,
    });

    const option = options?.find((o) =>
        props.value ? o.id === props.value : o.id === defaultValue
    );

    const close = () => {
        setOpen(false);
    };

    return (
        <>
            <div className={joinClassName(classNames.root, className)}>
                {props.label && (
                    <span
                        className={joinClassName(
                            classNames.label,
                            'input-label'
                        )}
                    >
                        {props.label}
                    </span>
                )}
                <div className={classNames.container}>
                    <label
                        ref={anchorElRef}
                        className={classNames.select}
                        htmlFor={props.id}
                    >
                        <div className={classNames.valueContainer}>
                            <div className={classNames.value}>
                                {option?.name}
                            </div>
                        </div>
                        <div className={classNames.indicator}>
                            <ChevronDown />
                        </div>
                        <input
                            className={classNames.input}
                            value={props.value}
                            role="combobox"
                            tabIndex={-1}
                            aria-haspopup="listbox"
                            aria-expanded={true}
                            aria-controls={`${props.id}-listbox`}
                            aria-activedescendant={`${props.id}-item-0`}
                            onChange={() => {}}
                            onBlur={(e) => {
                                onBlur?.(e);
                                if (e.defaultPrevented) {
                                    return;
                                }
                                setOpen(false);
                            }}
                            onFocus={(e) => {
                                onFocus?.(e);
                                if (e.defaultPrevented) {
                                    return;
                                }
                                setOpen(true);
                            }}
                            ref={ref}
                            {...rest}
                        />
                    </label>
                    {helperText ? (
                        <span className={classNames.helperText}>
                            {helperText}
                        </span>
                    ) : null}
                </div>
                <Popover
                    open={open}
                    anchorEl={anchorElRef.current}
                    onClose={close}
                >
                    <div className={classNames.menu}>
                        <ul
                            className={classNames.menuList}
                            id={`${props.id}-listbox`}
                            role="listbox"
                        >
                            {props.options?.map((option, idx) => {
                                const selected = option.id === props.value;
                                return (
                                    <SingleOption
                                        key={option.id}
                                        id={`${props.id}-item-${idx}`}
                                        role="option"
                                        aria-selected={false}
                                        selected={selected}
                                        onMouseDown={() => {
                                            onChange?.(option);
                                        }}
                                    >
                                        <div className="option-text">
                                            {option.name}
                                        </div>
                                        {selected ? <CheckOutline /> : null}
                                    </SingleOption>
                                );
                            })}
                        </ul>
                    </div>
                </Popover>
            </div>
        </>
    );
});

export default SingleSelect;
