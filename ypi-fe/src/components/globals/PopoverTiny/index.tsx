import { css } from '@/styled-system/css';
import * as React from 'react';
import { Popover, PopoverPosition } from 'react-tiny-popover';

type TriggerAction = 'hover' | 'click';

export interface PopoverTinyProps {
    message: string;
    positions?: PopoverPosition | PopoverPosition[];
    children: React.ReactNode;
    trigger: TriggerAction[];
    disabled?: boolean;
}

export default function PopoverTiny({
    message,
    positions,
    disabled,
    trigger,
    children,
}: PopoverTinyProps) {
    const [open, setOpen] = React.useState<boolean>(false);

    const onOpen = () => {
        setOpen(true);
    };

    const onClose = () => {
        setOpen(false);
    };
    const actions = React.useMemo(() => {
        if (disabled) return {};
        if (!trigger?.length) return {};

        const actionTrigger: Record<string, any> = {};

        trigger.forEach((action) => {
            if (action === 'click') {
                actionTrigger.onClick = onOpen;
            }

            if (action === 'hover') {
                actionTrigger.onMouseOver = onOpen;
                actionTrigger.onMouseLeave = onClose;
            }
        });

        return actionTrigger;
    }, [trigger, disabled]);

    return (
        <Popover
            isOpen={open}
            positions={positions} // preferred positions by priority
            content={
                <div
                    className={css({
                        h: 'fit-content',
                        overflowX: 'hidden',
                        flex: 1,
                        my: 1,
                        backgroundColor: 'white',
                        borderRadius: '10px',
                        padding: '12px',
                        right: '40px',
                        boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.1)',
                    })}
                >
                    {message}
                </div>
            }
            onClickOutside={onClose}
        >
            <div {...actions}>{children}</div>
        </Popover>
    );
}
