import { css } from '@/styled-system/css';
import joinClassName from '@/utilities/globals/function/className';
import React, { ComponentPropsWithoutRef } from 'react';
import { Close } from '../Icons';
import Typography from '../Typography';

const containerStyle = css({
    display: 'inline-flex',
    gap: '10px',
    background: 'white',
    px: 1,
    py: '2px',
    borderRadius: 20,
    alignItems: 'center',
});

const closeStyle = css({
    w: 14,
    height: 14,
    cursor: 'pointer',
});

type TChipClearableProps = {
    label: string;
    onRemove: (value: string) => void;
    value?: string;
    clearable?: boolean;
} & ComponentPropsWithoutRef<'div'>;

function ChipClearable({
    label,
    className,
    onRemove,
    value,
    clearable = true,
    ...rest
}: TChipClearableProps) {
    return (
        <div className={joinClassName(containerStyle, className)} {...rest}>
            <Typography typography="body_15" color="secondary_100">
                {label}
            </Typography>

            {clearable && (
                <Close
                    onClick={(event) => {
                        event?.preventDefault();
                        event?.stopPropagation();
                        onRemove(value ?? label);
                    }}
                    className={closeStyle}
                />
            )}
        </div>
    );
}

export default ChipClearable;
