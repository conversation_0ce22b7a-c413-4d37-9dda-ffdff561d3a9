import { SVGProps } from 'react';

const EditSquare = (p: SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <path
                d="M3.04581 15.1623L3.3397 14.7578H3.3397L3.04581 15.1623ZM2.21619 14.3327L1.81168 14.6266H1.81168L2.21619 14.3327ZM13.4542 15.1623L13.1603 14.7578L13.4542 15.1623ZM14.2838 14.3327L13.8793 14.0388V14.0388L14.2838 14.3327ZM2.21619 3.92429L1.81168 3.6304H1.81168L2.21619 3.92429ZM3.04581 3.09467L3.3397 3.49918L3.04581 3.09467ZM6.75599 2.88385C7.03212 2.88054 7.25327 2.65402 7.24996 2.37789C7.24665 2.10177 7.02013 1.88061 6.74401 1.88392L6.75599 2.88385ZM15.4946 10.6345C15.4979 10.3584 15.2767 10.1318 15.0006 10.1285C14.7245 10.1252 14.4979 10.3464 14.4946 10.6225L15.4946 10.6345ZM8.84817 12.1318L8.79295 11.6349L8.84817 12.1318ZM5.75833 11.9087L5.40478 12.2623H5.40478L5.75833 11.9087ZM5.53522 8.81888L6.03216 8.87409L5.53522 8.81888ZM6.39555 7.02885L6.042 6.6753L6.39555 7.02885ZM5.7322 7.81179L5.28155 7.59519H5.28155L5.7322 7.81179ZM10.6382 11.2715L10.9917 11.625L10.6382 11.2715ZM9.85526 11.9348L10.0719 12.3855H10.0719L9.85526 11.9348ZM8.25 15.3785C6.83271 15.3785 5.80991 15.3778 5.0168 15.2919C4.23305 15.207 3.73313 15.0436 3.3397 14.7578L2.75191 15.5668C3.34423 15.9971 4.04025 16.1919 4.90909 16.2861C5.76857 16.3792 6.85499 16.3785 8.25 16.3785V15.3785ZM1 9.12848C1 10.5235 0.999314 11.6099 1.09243 12.4694C1.18656 13.3382 1.38134 14.0343 1.81168 14.6266L2.62069 14.0388C2.33485 13.6454 2.17153 13.1454 2.08661 12.3617C2.00069 11.5686 2 10.5458 2 9.12848H1ZM3.3397 14.7578C3.06379 14.5573 2.82115 14.3147 2.62069 14.0388L1.81168 14.6266C2.07382 14.9874 2.39111 15.3047 2.75191 15.5668L3.3397 14.7578ZM8.25 16.3785C9.64501 16.3785 10.7314 16.3792 11.5909 16.2861C12.4597 16.1919 13.1558 15.9971 13.7481 15.5668L13.1603 14.7578C12.7669 15.0436 12.2669 15.207 11.4832 15.2919C10.6901 15.3778 9.66729 15.3785 8.25 15.3785V16.3785ZM13.8793 14.0388C13.6788 14.3147 13.4362 14.5573 13.1603 14.7578L13.7481 15.5668C14.1089 15.3047 14.4262 14.9874 14.6883 14.6266L13.8793 14.0388ZM2 9.12848C2 7.7112 2.00069 6.68839 2.08661 5.89529C2.17153 5.11153 2.33485 4.61161 2.62069 4.21818L1.81168 3.6304C1.38134 4.22271 1.18656 4.91874 1.09243 5.78757C0.999314 6.64705 1 7.73347 1 9.12848H2ZM2.75191 2.69016C2.39111 2.9523 2.07382 3.26959 1.81168 3.6304L2.62069 4.21818C2.82115 3.94227 3.06379 3.69964 3.3397 3.49918L2.75191 2.69016ZM6.74401 1.88392C4.92102 1.90578 3.69243 2.00684 2.75191 2.69016L3.3397 3.49918C3.98398 3.03108 4.88349 2.9063 6.75599 2.88385L6.74401 1.88392ZM14.4946 10.6225C14.4722 12.495 14.3474 13.3945 13.8793 14.0388L14.6883 14.6266C15.3716 13.6861 15.4727 12.4575 15.4946 10.6345L14.4946 10.6225ZM14.3135 6.88909L10.2846 10.9179L10.9917 11.625L15.0206 7.59619L14.3135 6.88909ZM6.74911 7.3824L10.778 3.35355L10.0709 2.64645L6.042 6.6753L6.74911 7.3824ZM8.79295 11.6349C7.91741 11.7322 7.31486 11.7979 6.86554 11.7834C6.42963 11.7693 6.23774 11.681 6.11188 11.5552L5.40478 12.2623C5.78869 12.6462 6.28171 12.765 6.83326 12.7828C7.37141 12.8002 8.05889 12.7226 8.90338 12.6288L8.79295 11.6349ZM5.03828 8.76366C4.94445 9.60815 4.86682 10.2956 4.8842 10.8338C4.90201 11.3853 5.02086 11.8784 5.40478 12.2623L6.11188 11.5552C5.98602 11.4293 5.89776 11.2374 5.88368 10.8015C5.86917 10.3522 5.93488 9.74964 6.03216 8.87409L5.03828 8.76366ZM6.042 6.6753C5.69764 7.01965 5.43761 7.27048 5.28155 7.59519L6.18285 8.02838C6.24811 7.89261 6.35654 7.77497 6.74911 7.3824L6.042 6.6753ZM6.03216 8.87409C6.09347 8.32231 6.1176 8.16415 6.18285 8.02838L5.28155 7.59519C5.12548 7.91991 5.09206 8.27965 5.03828 8.76366L6.03216 8.87409ZM10.2846 10.9179C9.89207 11.3105 9.77443 11.4189 9.63866 11.4842L10.0719 12.3855C10.3966 12.2294 10.6474 11.9694 10.9917 11.625L10.2846 10.9179ZM8.90338 12.6288C9.38739 12.575 9.74714 12.5416 10.0719 12.3855L9.63866 11.4842C9.5029 11.5494 9.34474 11.5736 8.79295 11.6349L8.90338 12.6288ZM14.3135 3.35355C14.8235 3.86355 15.1697 4.21123 15.3945 4.50595C15.6106 4.78914 15.667 4.96394 15.667 5.12132H16.667C16.667 4.65738 16.4735 4.27152 16.1896 3.89937C15.9144 3.53877 15.5106 3.13645 15.0206 2.64645L14.3135 3.35355ZM15.0206 7.59619C15.5106 7.10619 15.9144 6.70387 16.1896 6.34327C16.4735 5.97112 16.667 5.58526 16.667 5.12132H15.667C15.667 5.2787 15.6106 5.4535 15.3945 5.73668C15.1697 6.03141 14.8235 6.37909 14.3135 6.88909L15.0206 7.59619ZM15.0206 2.64645C14.5306 2.15644 14.1283 1.75262 13.7677 1.47749C13.3955 1.19355 13.0097 1 12.5457 1V2C12.7031 2 12.8779 2.05644 13.1611 2.27251C13.4558 2.49738 13.8035 2.84356 14.3135 3.35355L15.0206 2.64645ZM10.778 3.35355C11.288 2.84356 11.6356 2.49738 11.9304 2.27251C12.2135 2.05644 12.3883 2 12.5457 2V1C12.0818 1 11.6959 1.19355 11.3238 1.47749C10.9632 1.75262 10.5609 2.15644 10.0709 2.64645L10.778 3.35355ZM15.0206 6.88909L10.778 2.64645L10.0709 3.35355L14.3135 7.59619L15.0206 6.88909Z"
                fill="#70747D"
            />
        </svg>
    );
};

export default EditSquare;
