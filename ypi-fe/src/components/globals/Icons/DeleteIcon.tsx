import { SVGProps } from 'react';

export default function DeleteIcon(p: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <path
                d="M12.3449 9.01982L12.9392 9.10253H12.9392L12.3449 9.01982ZM12.17 10.2765L12.7643 10.3592L12.17 10.2765ZM3.83017 10.2765L4.42445 10.1938L3.83017 10.2765ZM3.65528 9.01983L3.06101 9.10253L3.65528 9.01983ZM6.12251 14.9914L5.88924 15.5442H5.88924L6.12251 14.9914ZM4.3168 12.8737L4.87999 12.6668L4.3168 12.8737ZM11.6834 12.8737L12.2466 13.0806L11.6834 12.8737ZM9.87765 14.9914L9.64438 14.4386H9.64438L9.87765 14.9914ZM3.93072 6.44341C3.89938 6.11353 3.60655 5.87151 3.27666 5.90285C2.94678 5.9342 2.70476 6.22703 2.7361 6.55692L3.93072 6.44341ZM13.2641 6.55692C13.2954 6.22703 13.0534 5.9342 12.7235 5.90285C12.3936 5.87151 12.1008 6.11353 12.0694 6.44341L13.2641 6.55692ZM13.3334 5.76683C13.6648 5.76683 13.9334 5.4982 13.9334 5.16683C13.9334 4.83546 13.6648 4.56683 13.3334 4.56683V5.76683ZM2.66675 4.56683C2.33538 4.56683 2.06675 4.83546 2.06675 5.16683C2.06675 5.4982 2.33538 5.76683 2.66675 5.76683V4.56683ZM6.06675 12.5002C6.06675 12.8315 6.33538 13.1002 6.66675 13.1002C6.99812 13.1002 7.26675 12.8315 7.26675 12.5002H6.06675ZM7.26675 7.16683C7.26675 6.83546 6.99812 6.56683 6.66675 6.56683C6.33538 6.56683 6.06675 6.83546 6.06675 7.16683H7.26675ZM8.73341 12.5002C8.73341 12.8315 9.00204 13.1002 9.33342 13.1002C9.66479 13.1002 9.93341 12.8315 9.93341 12.5002H8.73341ZM9.93341 7.16683C9.93341 6.83546 9.66479 6.56683 9.33342 6.56683C9.00204 6.56683 8.73341 6.83546 8.73341 7.16683H9.93341ZM10.6667 5.16683V5.76683H11.2667V5.16683H10.6667ZM5.33341 5.16683H4.73341V5.76683H5.33341V5.16683ZM11.7506 8.93712L11.5757 10.1938L12.7643 10.3592L12.9392 9.10253L11.7506 8.93712ZM4.42445 10.1938L4.24955 8.93713L3.06101 9.10253L3.2359 10.3592L4.42445 10.1938ZM8.00008 14.5668C6.97165 14.5668 6.63279 14.5555 6.35578 14.4386L5.88924 15.5442C6.44376 15.7782 7.07945 15.7668 8.00008 15.7668V14.5668ZM3.2359 10.3592C3.42165 11.6939 3.52429 12.4565 3.75361 13.0806L4.87999 12.6668C4.70629 12.194 4.61885 11.5907 4.42445 10.1938L3.2359 10.3592ZM6.35578 14.4386C5.77333 14.1928 5.21734 13.585 4.87999 12.6668L3.75361 13.0806C4.16993 14.2138 4.91673 15.1338 5.88924 15.5442L6.35578 14.4386ZM11.5757 10.1938C11.3813 11.5907 11.2939 12.194 11.1202 12.6668L12.2466 13.0806C12.4759 12.4565 12.5785 11.6939 12.7643 10.3592L11.5757 10.1938ZM8.00008 15.7668C8.92071 15.7668 9.55641 15.7782 10.1109 15.5442L9.64438 14.4386C9.36737 14.5555 9.02851 14.5668 8.00008 14.5668V15.7668ZM11.1202 12.6668C10.7828 13.585 10.2268 14.1928 9.64438 14.4386L10.1109 15.5442C11.0834 15.1338 11.8302 14.2138 12.2466 13.0806L11.1202 12.6668ZM4.24955 8.93713C4.10135 7.87222 3.99115 7.07938 3.93072 6.44341L2.7361 6.55692C2.79958 7.22494 2.91423 8.04783 3.06101 9.10253L4.24955 8.93713ZM12.9392 9.10253C13.0859 8.04783 13.2006 7.22494 13.2641 6.55692L12.0694 6.44341C12.009 7.07938 11.8988 7.87221 11.7506 8.93712L12.9392 9.10253ZM13.3334 4.56683H2.66675V5.76683H13.3334V4.56683ZM7.26675 12.5002V7.16683H6.06675V12.5002H7.26675ZM9.93341 12.5002V7.16683H8.73341V12.5002H9.93341ZM10.0667 4.50016V5.16683H11.2667V4.50016H10.0667ZM10.6667 4.56683H5.33341V5.76683H10.6667V4.56683ZM5.93341 5.16683V4.50016H4.73341V5.16683H5.93341ZM8.00008 2.4335C9.14147 2.4335 10.0667 3.35877 10.0667 4.50016H11.2667C11.2667 2.69603 9.80421 1.2335 8.00008 1.2335V2.4335ZM8.00008 1.2335C6.19595 1.2335 4.73341 2.69603 4.73341 4.50016H5.93341C5.93341 3.35877 6.85869 2.4335 8.00008 2.4335V1.2335Z"
                fill="currentColor"
            />
        </svg>
    );
}
