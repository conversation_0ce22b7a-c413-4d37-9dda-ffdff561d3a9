import { SVGProps } from 'react';

const Edit = (p: SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width={20}
            height={20}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <path
                d="M9.60932 15.85L9.33639 15.1515L9.60932 15.85ZM6.22899 16.4992L5.85399 17.1487H5.85399L6.22899 16.4992ZM5.10097 13.2472L4.35952 13.3601L5.10097 13.2472ZM5.50955 11.0786L6.15907 11.4536L5.50955 11.0786ZM5.02276 12.1096L4.28573 11.9708H4.28573L5.02276 12.1096ZM11.2831 14.4119L11.9326 14.7869L11.2831 14.4119ZM10.6335 15.349L11.1223 15.9179H11.1223L10.6335 15.349ZM8.67492 5.59602L8.0254 5.22102L8.67492 5.59602ZM13.7989 8.55435L10.6335 14.0369L11.9326 14.7869L15.0979 9.30435L13.7989 8.55435ZM6.15907 11.4536L9.32444 5.97102L8.0254 5.22102L4.86003 10.7036L6.15907 11.4536ZM9.33639 15.1515C8.41903 15.5099 7.80659 15.7472 7.33701 15.8569C6.88704 15.962 6.71381 15.9131 6.60399 15.8496L5.85399 17.1487C6.43789 17.4858 7.05929 17.4622 7.67828 17.3175C8.27767 17.1775 9.00854 16.89 9.88225 16.5486L9.33639 15.1515ZM4.35952 13.3601C4.50076 14.2874 4.61719 15.0641 4.7956 15.6532C4.97986 16.2616 5.27009 16.8116 5.85399 17.1487L6.60399 15.8496C6.49416 15.7862 6.36515 15.6607 6.23121 15.2184C6.09143 14.7569 5.99071 14.1079 5.84242 13.1342L4.35952 13.3601ZM4.86003 10.7036C4.59611 11.1607 4.36739 11.5373 4.28573 11.9708L5.75979 12.2485C5.78324 12.1241 5.84401 11.9993 6.15907 11.4536L4.86003 10.7036ZM5.84242 13.1342C5.74754 12.5113 5.73635 12.3729 5.75979 12.2485L4.28573 11.9708C4.20406 12.4042 4.28004 12.8382 4.35952 13.3601L5.84242 13.1342ZM10.6335 14.0369C10.3185 14.5826 10.2408 14.6977 10.1448 14.7802L11.1223 15.9179C11.4568 15.6304 11.6687 15.2441 11.9326 14.7869L10.6335 14.0369ZM9.88225 16.5486C10.3739 16.3565 10.7878 16.2053 11.1223 15.9179L10.1448 14.7802C10.0487 14.8627 9.92331 14.9222 9.33639 15.1515L9.88225 16.5486ZM12.8533 5.02545C13.5521 5.42889 14.0101 5.69513 14.3234 5.93693C14.6195 6.16544 14.694 6.30406 14.7238 6.41539L16.1727 6.02716C16.0239 5.47166 15.6688 5.08045 15.2398 4.74941C14.8281 4.43167 14.2654 4.10865 13.6033 3.72642L12.8533 5.02545ZM15.0979 9.30435C15.4802 8.6423 15.806 8.08122 16.0037 7.60018C16.2096 7.099 16.3215 6.58266 16.1727 6.02716L14.7238 6.41539C14.7536 6.52673 14.7585 6.68401 14.6163 7.02996C14.4658 7.39604 14.2023 7.85558 13.7989 8.55435L15.0979 9.30435ZM13.6033 3.72642C12.9413 3.34418 12.3802 3.0184 11.8992 2.82069C11.398 2.61471 10.8816 2.50281 10.3261 2.65166L10.7144 4.10055C10.8257 4.07071 10.983 4.0659 11.3289 4.20808C11.695 4.35854 12.1546 4.62202 12.8533 5.02545L13.6033 3.72642ZM9.32444 5.97102C9.72787 5.27224 9.99411 4.8143 10.2359 4.50096C10.4644 4.20485 10.603 4.13038 10.7144 4.10055L10.3261 2.65166C9.77065 2.8005 9.37944 3.15558 9.0484 3.58455C8.73066 3.99629 8.40763 4.55897 8.0254 5.22102L9.32444 5.97102ZM14.8234 8.27983L9.04992 4.9465L8.29992 6.24554L14.0734 9.57887L14.8234 8.27983Z"
                fill="currentColor"
            />
        </svg>
    );
};

export default Edit;
