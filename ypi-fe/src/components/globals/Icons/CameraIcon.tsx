import { SVGProps } from 'react';

export function CameraIcon(p: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="60"
            height="60"
            viewBox="0 0 60 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <circle cx="30" cy="30" r="30" fill="#4F46E5" />
            <path
                d="M25.5092 17.9912L25.0109 17.4306L25.0109 17.4306L25.5092 17.9912ZM34.4895 17.9912L34.9878 17.4306L34.4895 17.9912ZM34.2018 17.7355L33.7036 18.296L34.2018 17.7355ZM25.7969 17.7355L26.2951 18.296V18.296L25.7969 17.7355ZM19.4141 42.06L19.855 41.4533L19.4141 42.06ZM17.9392 40.5852L18.546 40.1443L17.9392 40.5852ZM42.0595 40.5852L41.4527 40.1443L42.0595 40.5852ZM40.5846 42.06L40.1437 41.4533L40.5846 42.06ZM42.5827 24.6465V31.3333H44.0827V24.6465H42.5827ZM31.3327 42.5833H28.666V44.0833H31.3327V42.5833ZM17.416 31.3333V24.6465H15.916V31.3333H17.416ZM33.7036 18.296L33.9912 18.5517L34.9878 17.4306L34.7001 17.1749L33.7036 18.296ZM26.0075 18.5517L26.2951 18.296L25.2986 17.1749L25.0109 17.4306L26.0075 18.5517ZM21.9793 20.0833C23.4642 20.0833 24.8976 19.5383 26.0075 18.5517L25.0109 17.4306C24.1756 18.1731 23.0969 18.5833 21.9793 18.5833V20.0833ZM33.9912 18.5517C35.1011 19.5383 36.5345 20.0833 38.0194 20.0833V18.5833C36.9018 18.5833 35.8231 18.1731 34.9878 17.4306L33.9912 18.5517ZM34.7001 17.1749C32.0193 14.792 27.9794 14.792 25.2986 17.1749L26.2951 18.296C28.4076 16.4182 31.5911 16.4182 33.7036 18.296L34.7001 17.1749ZM28.666 42.5833C26.1495 42.5833 24.3223 42.5822 22.9031 42.4285C21.498 42.2762 20.582 41.9815 19.855 41.4533L18.9733 42.6668C19.9987 43.4118 21.2088 43.7537 22.7416 43.9198C24.2603 44.0843 26.1829 44.0833 28.666 44.0833V42.5833ZM15.916 31.3333C15.916 33.8164 15.915 35.739 16.0795 37.2577C16.2456 38.7905 16.5875 40.0006 17.3325 41.026L18.546 40.1443C18.0178 39.4173 17.723 38.5013 17.5708 37.0961C17.417 35.6769 17.416 33.8498 17.416 31.3333H15.916ZM19.855 41.4533C19.3527 41.0883 18.9109 40.6466 18.546 40.1443L17.3325 41.026C17.7899 41.6556 18.3436 42.2094 18.9733 42.6668L19.855 41.4533ZM42.5827 31.3333C42.5827 33.8498 42.5817 35.6769 42.4279 37.0961C42.2757 38.5013 41.9809 39.4173 41.4527 40.1443L42.6662 41.026C43.4112 40.0006 43.7531 38.7905 43.9192 37.2577C44.0837 35.739 44.0827 33.8164 44.0827 31.3333H42.5827ZM31.3327 44.0833C33.8158 44.0833 35.7384 44.0843 37.2571 43.9198C38.7899 43.7537 40 43.4118 41.0254 42.6668L40.1437 41.4533C39.4167 41.9815 38.5007 42.2762 37.0956 42.4285C35.6764 42.5822 33.8492 42.5833 31.3327 42.5833V44.0833ZM41.4527 40.1443C41.0878 40.6466 40.646 41.0883 40.1437 41.4533L41.0254 42.6668C41.6551 42.2094 42.2088 41.6556 42.6662 41.026L41.4527 40.1443ZM44.0827 24.6465C44.0827 21.2979 41.3681 18.5833 38.0194 18.5833V20.0833C40.5396 20.0833 42.5827 22.1263 42.5827 24.6465H44.0827ZM17.416 24.6465C17.416 22.1263 19.459 20.0833 21.9793 20.0833V18.5833C18.6306 18.5833 15.916 21.2979 15.916 24.6465H17.416ZM23.916 31.3333C23.916 34.693 26.6396 37.4166 29.9993 37.4166V35.9166C27.468 35.9166 25.416 33.8646 25.416 31.3333H23.916ZM29.9993 37.4166C33.3591 37.4166 36.0827 34.693 36.0827 31.3333H34.5827C34.5827 33.8646 32.5307 35.9166 29.9993 35.9166V37.4166ZM36.0827 31.3333C36.0827 27.9735 33.3591 25.2499 29.9993 25.2499V26.7499C32.5307 26.7499 34.5827 28.802 34.5827 31.3333H36.0827ZM29.9993 25.2499C26.6396 25.2499 23.916 27.9735 23.916 31.3333H25.416C25.416 28.802 27.468 26.7499 29.9993 26.7499V25.2499Z"
                fill="white"
            />
            <path
                d="M36.666 24.6667C36.666 25.4031 37.263 26 37.9993 26C38.7357 26 39.3327 25.4031 39.3327 24.6667C39.3327 23.9303 38.7357 23.3334 37.9993 23.3334C37.263 23.3334 36.666 23.9303 36.666 24.6667Z"
                fill="white"
            />
        </svg>
    );
}
