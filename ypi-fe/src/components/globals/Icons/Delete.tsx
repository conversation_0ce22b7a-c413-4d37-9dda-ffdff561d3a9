import { SVGProps } from 'react';

export default function Delete(p: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <path
                d="M15.4312 11.1494L16.0254 11.2321L15.4312 11.1494ZM15.2126 12.7203L15.8068 12.803H15.8068L15.2126 12.7203ZM4.78778 12.7203L5.38205 12.6376L4.78778 12.7203ZM4.56916 11.1494L3.97489 11.2321L4.56916 11.1494ZM7.6532 18.6139L7.41993 19.1667L7.6532 18.6139ZM5.39606 15.9668L5.95926 15.7599L5.39606 15.9668ZM14.6043 15.9668L15.1675 16.1737L14.6043 15.9668ZM12.3471 18.6139L12.1139 18.0611L12.3471 18.6139ZM4.76414 7.94308C4.7328 7.6132 4.43996 7.37118 4.11008 7.40253C3.78019 7.43387 3.53818 7.7267 3.56952 8.05659L4.76414 7.94308ZM16.4308 8.05659C16.4622 7.7267 16.2201 7.43387 15.8903 7.40253C15.5604 7.37118 15.2675 7.6132 15.2362 7.94308L16.4308 8.05659ZM16.6668 6.93317C16.9982 6.93317 17.2668 6.66454 17.2668 6.33317C17.2668 6.0018 16.9982 5.73317 16.6668 5.73317V6.93317ZM3.3335 5.73317C3.00213 5.73317 2.7335 6.0018 2.7335 6.33317C2.7335 6.66454 3.00213 6.93317 3.3335 6.93317V5.73317ZM7.7335 15.4998C7.7335 15.8312 8.00213 16.0998 8.3335 16.0998C8.66487 16.0998 8.9335 15.8312 8.9335 15.4998H7.7335ZM8.9335 8.83317C8.9335 8.5018 8.66487 8.23317 8.3335 8.23317C8.00213 8.23317 7.7335 8.5018 7.7335 8.83317H8.9335ZM11.0668 15.4998C11.0668 15.8312 11.3355 16.0998 11.6668 16.0998C11.9982 16.0998 12.2668 15.8312 12.2668 15.4998H11.0668ZM12.2668 8.83317C12.2668 8.5018 11.9982 8.23317 11.6668 8.23317C11.3355 8.23317 11.0668 8.5018 11.0668 8.83317H12.2668ZM13.3335 6.33317V6.93317H13.9335V6.33317H13.3335ZM6.66683 6.33317H6.06683V6.93317H6.66683V6.33317ZM14.8369 11.0667L14.6183 12.6376L15.8068 12.803L16.0254 11.2321L14.8369 11.0667ZM5.38205 12.6376L5.16344 11.0667L3.97489 11.2321L4.1935 12.803L5.38205 12.6376ZM10.0002 18.2332C8.7281 18.2332 8.26742 18.2218 7.88647 18.0611L7.41993 19.1667C8.07839 19.4445 8.8359 19.4332 10.0002 19.4332V18.2332ZM4.1935 12.803C4.42677 14.4791 4.55317 15.4124 4.83287 16.1737L5.95926 15.7599C5.73517 15.1499 5.62398 14.376 5.38205 12.6376L4.1935 12.803ZM7.88647 18.0611C7.10965 17.7333 6.39081 16.9344 5.95926 15.7599L4.83287 16.1737C5.3434 17.5632 6.25305 18.6743 7.41993 19.1667L7.88647 18.0611ZM14.6183 12.6376C14.3764 14.3759 14.2652 15.1499 14.0411 15.7599L15.1675 16.1737C15.4472 15.4124 15.5736 14.4791 15.8068 12.803L14.6183 12.6376ZM10.0002 19.4332C11.1644 19.4332 11.9219 19.4445 12.5804 19.1667L12.1139 18.0611C11.7329 18.2218 11.2722 18.2332 10.0002 18.2332V19.4332ZM14.0411 15.7599C13.6095 16.9344 12.8907 17.7333 12.1139 18.0611L12.5804 19.1667C13.7473 18.6743 14.6569 17.5632 15.1675 16.1737L14.0411 15.7599ZM5.16344 11.0667C4.97836 9.73685 4.84005 8.74206 4.76414 7.94308L3.56952 8.05659C3.64848 8.88761 3.79124 9.91247 3.97489 11.2321L5.16344 11.0667ZM16.0254 11.2321C16.2091 9.91247 16.3518 8.88761 16.4308 8.05659L15.2362 7.94308C15.1603 8.74206 15.022 9.73685 14.8369 11.0667L16.0254 11.2321ZM16.6668 5.73317H3.3335V6.93317H16.6668V5.73317ZM8.9335 15.4998V8.83317H7.7335V15.4998H8.9335ZM12.2668 15.4998V8.83317H11.0668V15.4998H12.2668ZM12.7335 5.49984V6.33317H13.9335V5.49984H12.7335ZM13.3335 5.73317H6.66683V6.93317H13.3335V5.73317ZM7.26683 6.33317V5.49984H6.06683V6.33317H7.26683ZM10.0002 2.7665C11.5097 2.7665 12.7335 3.99026 12.7335 5.49984H13.9335C13.9335 3.32752 12.1725 1.5665 10.0002 1.5665V2.7665ZM10.0002 1.5665C7.82784 1.5665 6.06683 3.32752 6.06683 5.49984H7.26683C7.26683 3.99026 8.49058 2.7665 10.0002 2.7665V1.5665Z"
                fill="currentColor"
            />
        </svg>
    );
}
