import { SVGProps } from 'react';

export default function PagerIcon(p: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="17"
            height="16"
            viewBox="0 0 17 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.26677 2.00033C4.53039 2.00033 3.93343 2.59728 3.93343 3.33366V11.467C3.93343 12.038 3.93395 12.4262 3.95847 12.7263C3.98235 13.0186 4.02563 13.168 4.07876 13.2723C4.20659 13.5232 4.41056 13.7272 4.66144 13.855C4.76571 13.9081 4.91518 13.9514 5.20746 13.9753C5.50752 13.9998 5.89571 14.0003 6.46677 14.0003H10.7334C11.3045 14.0003 11.6927 13.9998 11.9927 13.9753C12.285 13.9514 12.4345 13.9081 12.5388 13.855C12.7896 13.7272 12.9936 13.5232 13.1214 13.2723C13.1746 13.168 13.2179 13.0186 13.2417 12.7263C13.2662 12.4262 13.2668 12.038 13.2668 11.467V8.66699C13.2668 7.19423 12.0729 6.00033 10.6001 6.00033H10.1001C8.90348 6.00033 7.93343 5.03028 7.93343 3.83366C7.93343 2.82114 7.11262 2.00033 6.1001 2.00033H5.26677ZM7.26677 0.666992C11.3169 0.666992 14.6001 3.95024 14.6001 8.00033V11.4945C14.6001 12.0312 14.6001 12.4741 14.5706 12.8349C14.54 13.2096 14.4743 13.5541 14.3094 13.8776C14.0538 14.3794 13.6458 14.7873 13.1441 15.043C12.8205 15.2079 12.476 15.2736 12.1013 15.3042C11.7405 15.3337 11.2976 15.3337 10.761 15.3337H6.43922C5.90258 15.3337 5.45967 15.3337 5.09888 15.3042C4.72415 15.2736 4.37968 15.2079 4.05612 15.043C3.55436 14.7873 3.14641 14.3794 2.89075 13.8776C2.72589 13.5541 2.66018 13.2096 2.62956 12.8349C2.60008 12.4741 2.60009 12.0312 2.6001 11.4945L2.6001 3.33366C2.6001 1.8609 3.79401 0.666992 5.26677 0.666992H7.26677ZM12.565 5.18208C11.7912 3.73037 10.4383 2.63415 8.8145 2.20185C9.10161 2.67841 9.26677 3.23675 9.26677 3.83366C9.26677 4.2939 9.63986 4.66699 10.1001 4.66699H10.6001C11.3142 4.66699 11.9847 4.85414 12.565 5.18208Z"
                fill="currentColor"
            />
        </svg>
    );
}
