import { SVGProps } from 'react';

export default function PlusCircle(p: SVGProps<SVGSVGElement>) {
    return (
        <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...p}
        >
            <circle cx="14" cy="14" r="14" fill="currentColor" />
            <path
                d="M14.0001 7.19482V20.8059"
                stroke="white"
                strokeWidth="1.75"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M7.19458 14H20.8057"
                stroke="white"
                strokeWidth="1.75"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
}
