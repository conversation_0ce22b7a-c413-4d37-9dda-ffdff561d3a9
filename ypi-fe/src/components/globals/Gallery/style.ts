import { css } from '@/styled-system/css';

export const mainImageOverlay = css({
    color: '#f2f2f2',
    fontSize: '12px',
    padding: '8px 12px',
    position: 'absolute',
    top: 0,
});
export const imageListStyle = css({
    display: 'flex',
    mt: '20px',
    mx: '40px',
    overflow: 'hidden',
    gap: '16px',
});

export const btnNext = css({
    cursor: 'pointer',
    position: 'absolute',
    top: '50%',
    right: '-26px',
    transform: 'translate(50%, -50%)',
});
export const btnPrev = css({
    cursor: 'pointer',
    position: 'absolute',
    top: '50%',
    left: '-26px',
    transform: 'translate(-50%, -50%)',
});

export const controlStyle = css({
    width: '24px',
    height: '24px',
    '&:hover path': {
        transition: '0.2s',
        stroke: 'background.200',
    },
});

export const smallImageOverlay = css({
    background: '#00000099',
    position: 'absolute',
    inset: 0,
});
export const smallImageWrap = css({
    overflow: 'hidden',
    position: 'relative',
    borderRadius: '12px',
    cursor: 'pointer',
    flex: 1,
    maxWidth: '300px',
});
export const mainImageStyle = css({
    width: '100%',
    borderRadius: '12px',
    transition: '0.2s',
});

export const mainImageWrapper = css({ position: 'relative', margin: '0 40px' });
