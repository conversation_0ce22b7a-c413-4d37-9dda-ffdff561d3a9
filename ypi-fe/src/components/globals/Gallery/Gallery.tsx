import React, { useState } from 'react';
import {
    btnNext,
    btnPrev,
    controlStyle,
    imageListStyle,
    mainImageOverlay,
    mainImageStyle,
    mainImageWrapper,
    smallImageOverlay,
    smallImageWrap,
} from './style';
import ChevronLeft from '../Icons/ChevronLeft';
import ChevronRight from '../Icons/ChevronRight';
import { SlideNextIcon, SlidePrevIcon } from '../Icons';

export interface SlideGallery {
    url: string;
    key: string;
    id: string;
    link?: string;
}

interface GalleryProps {
    slides: SlideGallery[];
    activeIndex?: number | null;
}

export const Gallery: React.FC<GalleryProps> = ({
    slides,
    activeIndex = 0,
}) => {
    const [slideIndex, setSlideIndex] = useState<number>(activeIndex || 0);

    const plusSlides = (n: number): void => {
        setSlideIndex((prevIndex) => {
            let newIndex = prevIndex + n;
            if (newIndex >= slides.length) newIndex = 0;
            if (newIndex < 0) newIndex = slides.length - 1;
            return newIndex;
        });
    };

    const slideTo = (n: number): void => {
        setSlideIndex(n);
    };

    return (
        <div>
            <div className={mainImageWrapper}>
                {slides?.map((slide, index) => (
                    <div
                        key={index}
                        style={{
                            display: index === slideIndex ? 'block' : 'none',
                        }}
                    >
                        <div className={mainImageOverlay}>
                            {index + 1} / {slides.length}
                        </div>
                        <img
                            src={slide?.url || slide?.link}
                            className={mainImageStyle}
                            alt={slide.key}
                        />
                    </div>
                ))}

                <button className={btnPrev} onClick={() => plusSlides(-1)}>
                    <SlidePrevIcon className={controlStyle} />
                </button>
                <button className={btnNext} onClick={() => plusSlides(1)}>
                    <SlideNextIcon className={controlStyle} />
                </button>
            </div>

            <div className={imageListStyle}>
                {slides?.map((photo, idx) => {
                    const isActive = idx === slideIndex;
                    return (
                        <div
                            key={photo.key}
                            className={smallImageWrap}
                            onClick={() => slideTo(idx)}
                        >
                            <div
                                className={isActive ? '' : smallImageOverlay}
                            />
                            <img
                                src={photo.url || photo?.link}
                                alt={photo.key}
                            />
                        </div>
                    );
                })}
            </div>
        </div>
    );
};
