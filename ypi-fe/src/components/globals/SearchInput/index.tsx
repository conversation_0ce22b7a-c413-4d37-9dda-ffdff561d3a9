import { css } from '@/styled-system/css';
import { useState } from 'react';
import { Close } from '../Icons';
import Search from '../Icons/Search';
import Input from '../Input';
import { ClearKeywordButton, inputStyle } from './style';

type Props = {
    keyword?: string;
    onChange?: (keyword: string) => void;
};

export default function SearchInput(props: Props) {
    const [value, setValue] = useState(props.keyword ?? '');
    return (
        <Input
            onChange={(e) => {
                const text = e.currentTarget.value;
                setValue(text);
                props.onChange?.(text);
            }}
            value={value}
            variant="underline"
            className={inputStyle}
            startAdornment={<Search className={css({ mr: 1 })} />}
            endAdornment={
                <ClearKeywordButton
                    onClick={() => {
                        setValue('');
                        props.onChange?.('');
                    }}
                    visibility={value.length > 0 ? 'visible' : 'hidden'}
                >
                    <Close className={css({ w: 20, height: 20 })} />
                </ClearKeywordButton>
            }
        />
    );
}
