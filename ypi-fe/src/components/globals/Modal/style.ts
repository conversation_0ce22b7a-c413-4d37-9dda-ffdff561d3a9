import { RecipeVariantProps, sva } from '@/styled-system/css';

const modalStyle = sva({
    slots: ['root', 'overlay', 'contain', 'content', 'close'],
    base: {
        root: {
            overflowY: 'auto',
            position: 'fixed',
            top: '0',
            right: '0',
            bottom: '0',
            left: '0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
        },
        overlay: {
            position: 'absolute',
            width: '100vw',
            height: '100vh',
            backgroundColor: 'secondary.100',
            opacity: '0.6',
            zIndex: 1,
        },
        contain: {
            padding: '24px',
            minWidth: '464px',
            // minHeight: "433px",
            bg: 'white',
            borderRadius: '20px',
            position: 'relative',
            zIndex: 2,
        },
        content: {
            marginTop: '48px',
            maxHeight: '70vh',
            overflow: 'auto',
        },
        close: {
            position: 'absolute',
            top: '26.5px',
            right: '24px',
            cursor: 'pointer',
            zIndex: 3,
        },
    },
    variants: {
        sizes: {
            sm: {
                contain: {
                    maxWidth: '464px',
                },
            },
            md: {
                contain: {
                    maxWidth: '656px',
                },
            },
            lg: {
                contain: {
                    width: '1180px',
                },
            },
        },
        fullWidth: {
            true: {
                contain: {
                    width: '100%',
                },
            },
        },
        gallery: {
            true: {
                content: {
                    marginTop: '0',
                    maxHeight: 'unset',
                },
                overlay: {
                    backgroundColor: 'secondary.100',
                    opacity: 0.9,
                },
                contain: {
                    width: '60%',
                    height: '100%',
                    overflow: 'hidden',
                    bg: 'transparent',
                    // minHeight: "433px",
                    zIndex: 2,
                },
                close: {
                    display: 'none',
                },
            },
        },
    },
});

export default modalStyle;

export type ModalVariants = RecipeVariantProps<typeof modalStyle>;
