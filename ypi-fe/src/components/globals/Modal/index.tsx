'use client';

import { Dialog, Transition } from '@headlessui/react';
import { Fragment, ReactNode } from 'react';
import modalStyle, { ModalVariants } from '@/components/globals/Modal/style';

import { Close } from '@/components/globals/Icons';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import headingTag from '@/utilities/types/enums/Typography';

interface ModalProps {
    title?: string;
    iconTitle?: ReactNode;
    isOpen: boolean;
    onClose: () => void;
    children: ReactNode;
}

const Modal = ({
    isOpen,
    title,
    iconTitle,
    children,
    onClose,
    sizes,
    fullWidth,
    gallery,
}: ModalProps & ModalVariants) => {
    const classes = modalStyle({ sizes, fullWidth, gallery });

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className={classes.root} onClose={onClose}>
                <Dialog.Overlay className={classes.overlay} />

                <Dialog.Panel className={classes.contain}>
                    {title || iconTitle ? (
                        <Dialog.Title>
                            {title ? (
                                <Typography
                                    tag={headingTag.div}
                                    typography="header_24"
                                    color="primary_100"
                                >
                                    {title}
                                </Typography>
                            ) : null}
                            {iconTitle ? (
                                <div
                                    className={css({
                                        display: 'flex',
                                        justifyContent: 'center',
                                    })}
                                >
                                    {iconTitle}
                                </div>
                            ) : null}
                        </Dialog.Title>
                    ) : null}
                    <div className={classes.close} onClick={onClose}>
                        <Close />
                    </div>

                    <div className={classes.content}>{children}</div>
                </Dialog.Panel>
            </Dialog>
        </Transition>
    );
};

export default Modal;
