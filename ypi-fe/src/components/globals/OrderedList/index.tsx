import { RecipeVariantProps, css, sva } from '@/styled-system/css';

type Props = {
    items: string[];
} & OrderedListVariantType;

export default function OrderedList(props: Props) {
    const classNames = olStyle(props);
    return (
        <ol className={classNames.ol}>
            {props.items.map((item, i) => (
                <li key={i} className={classNames.li}>
                    {item}
                </li>
            ))}
        </ol>
    );
}

const olStyle = sva({
    slots: ['ol', 'li'],
    base: {
        ol: {
            listStyle: 'inside decimal',
        },
    },
    variants: {
        overflow: {
            ellipsis: {
                li: {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                },
            },
            clip: {
                li: {
                    overflow: 'hidden',
                    textOverflow: 'clip',
                    whiteSpace: 'nowrap',
                },
            },
        },
    },
});

type OrderedListVariantType = RecipeVariantProps<typeof olStyle>;
