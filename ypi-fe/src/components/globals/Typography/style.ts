// import { colors } from '@/utilities/globals/panda';
import { cva, RecipeVariantProps } from '@/styled-system/css';
const colors = {
    success_100: {
        color: 'success.100',
    },
    success_20: {
        color: 'success.20',
    },
    primary_100: {
        color: 'primary.100',
    },
    primary_80: {
        color: 'primary.80',
    },
    primary_60: {
        color: 'primary.60',
    },
    primary_40: {
        color: 'primary.40',
    },
    primary_20: {
        color: 'primary.20',
    },
    primary_5: {
        color: 'primary.5',
    },
    secondary_100: {
        color: 'secondary.100',
    },
    secondary_80: {
        color: 'secondary.80',
    },
    secondary_60: {
        color: 'secondary.60',
    },
    secondary_40: {
        color: 'secondary.40',
    },
    secondary_20: {
        color: 'secondary.20',
    },
    secondary_5: {
        color: 'secondary.5',
    },
    background_100: {
        color: 'background.100',
    },
    background_40: {
        color: 'background.40',
    },
    background_0: {
        color: 'background.0',
    },

    warning_100: {
        color: 'warning.100',
    },
    bg_warning_100: {
        bg: 'warning.100',
    },
    warning_20: {
        color: 'warning.20',
    },
    yellow_100: {
        color: 'yellow.100',
    },
    yellow_20: {
        color: 'yellow.20',
    },
    body_1: {
        color: 'body.1',
    },
    white: {
        color: 'white',
    },
    neutrals_10: {
        color: 'neutrals.10',
    },
    neutrals_20: {
        color: 'neutrals.20',
    },
};

/**
 * headingStyle represents the style configuration for headings in a UI component.
 *
 * The configuration is defined using the cva() function, which stands for "Component Variant API".
 *
 */
export const headingStyle = cva({
    base: {
        fontFamily: 'inherit',
    },
    variants: {
        typography: {
            header_40: {
                fontSize: 'header.40',
                fontWeight: 'bold',
            },
            header_32: {
                fontSize: 'header.32',
                fontWeight: 'bold',
            },
            header_24: {
                fontSize: 'header.24',
                fontWeight: 'bold',
            },
            header_20: {
                fontSize: 'header.20',
                fontWeight: 'bold',
            },
            header_18: {
                fontSize: 'header.18',
                fontWeight: 'bold',
            },
            header_16: {
                fontSize: 'header.16',
                fontWeight: 'bold',
            },
            header_14: {
                fontSize: 'header.14',
                fontWeight: 'bold',
            },
            header_15: {
                fontSize: 'header.15',
                fontWeight: 'bold',
            },
            header_12: {
                fontSize: 'header.12',
                fontWeight: 'bold',
            },
            subtitle_24: {
                fontSize: 'subtitle.24',
                fontWeight: 'semiBold',
            },
            subtitle_20: {
                fontSize: 'subtitle.20',
                fontWeight: 'semiBold',
            },
            subtitle_16: {
                fontSize: 'subtitle.16',
                fontWeight: 'semiBold',
            },
            subtitle_15: {
                fontSize: 'subtitle.15',
                fontWeight: 'semiBold',
            },
            subtitle_14: {
                fontSize: 'subtitle.14',
                fontWeight: 'semiBold',
            },
            subtitle_12: {
                fontSize: 'subtitle.12',
                fontWeight: 'semiBold',
            },
            subtitle_10: {
                fontSize: 'subtitle.10',
                fontWeight: 'semiBold',
            },
            body_16: {
                fontSize: 'body.16',
                fontWeight: 'regular',
            },
            body_15: {
                fontSize: 'body.15',
                fontWeight: 'regular',
            },
            body_14: {
                fontSize: 'body.14',
                fontWeight: 'regular',
            },
            body_12: {
                fontSize: 'body.12',
                fontWeight: 'regular',
            },
        },
        color: colors,
    },
    defaultVariants: {
        typography: 'body_16',
        color: 'body_1',
    },
});

/**
 * Represents the different variants of a heading.
 */
export type HeadingVariants = RecipeVariantProps<typeof headingStyle>;

export type HeadingColors = keyof typeof colors;
