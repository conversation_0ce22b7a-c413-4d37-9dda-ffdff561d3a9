import { FC, ReactNode } from 'react';
import ETypographyTag from '@/utilities/types/enums/Typography';
import {
    headingStyle,
    HeadingVariants,
} from '@/components/globals/Typography/style';
import joinClassName from '@/utilities/globals/function/className';

// Define the props for the component
type TTypographyProps = {
    // The children to display within the heading
    children: ReactNode;
    // The HTML tag to apply to the element
    tag?: ETypographyTag;
    className?: string;
} & HeadingVariants;

// The heading component, which can take various heading tags, and styles based on typography and color
const Typography: FC<TTypographyProps> = ({
    children,
    tag = ETypographyTag.p,
    typography,
    color,
    className,
}) => {
    // Convert the HeadingTag enum to a JSX element type
    const Tag = tag as keyof JSX.IntrinsicElements;
    return (
        <Tag
            className={joinClassName(
                headingStyle({ typography, color }),
                className
            )}
        >
            {children}
        </Tag>
    );
};

export default Typography;
