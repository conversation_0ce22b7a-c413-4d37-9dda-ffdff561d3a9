"use client"
import Step from './Step';
import {StepsComponent, stepsVariants} from "@/components/globals/Steps/style";


type StepType = {
    title: string;
    subtitle: string;
    active: boolean;
}

interface StepsProps {
    steps: StepType[];
    onClick?: (index: number) => void;
}

const Steps = ({ steps, onClick,...props }:StepsProps & stepsVariants) => {

    return (
      <StepsComponent>
          {
                steps.map((step, index) => {
                    return (
                        <Step
                             key={index}
                             title={step.title}
                             subtitle={step.subtitle}
                             number={index + 1}
                             active={step.active}
                             onClick={onClick}
                             {...props}
                        />
                    );
                })
          }
      </StepsComponent>
    );
}

export default Steps;

export {Step};
export type { StepType };

