import React from 'react';
import stepStyle, {stepVariants} from "@/components/globals/Steps/Step/style";

interface IStepProps {
    number: number;
    title: string;
    subtitle: string;
    onClick?: (index: number) => void;
}

const Step: React.FC<IStepProps & stepVariants> = ({ number, title, subtitle,active,onClick }) => {
    const classes = stepStyle({active});
    if (!title  || !number) {
        return null
    }
    return (
      <div className={classes.root} onClick={() => onClick?.(number)}>
          <div className={classes.step_number}>
              <span>0{number}</span>
          </div>
          <div className={classes.step}>
              <h2 className={classes.step_title}>{title}</h2>
              {
                  subtitle && <p className={classes.step_subtitle}>{subtitle}</p>
              }
          </div>
      </div>
    );
};

export default Step;