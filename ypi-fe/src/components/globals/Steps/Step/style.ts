import { sva, RecipeVariantProps } from '@/styled-system/css';

const stepStyle = sva({
    slots: ['root', 'step', 'step_number', 'step_title', 'step_subtitle'],
    base: {
        root: {
            display: 'flex',
            alignItems: 'stretch',
            gap: '12px',
            userSelect: 'none',
        },
        step_number: {
            width: '36px',
            height: '36px',
            borderRadius: '50%',
            border: '1px solid',
            borderColor: 'secondary.40',
            background: 'white',
            color: 'secondary.40',
            fontSize: 'subtitle.16',
            fontWeight: 'semiBold',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            alignSelf: 'center',
        },
        step: {
            display: 'flex',
            flexDirection: 'column',
        },
        step_title: {
            fontSize: 'subtitle.16',
            fontWeight: 'semiBold',
            color: 'secondary.40',
        },
        step_subtitle: {
            fontSize: 'body.12',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
    },
    variants: {
        active: {
            true: {
                step_number: {
                    background: 'primary.100',
                    borderColor: 'primary.100',
                    color: 'white',
                },
                step_title: {
                    color: 'primary.100',
                },
                step_subtitle: {
                    color: 'secondary.100',
                },
            },
        },
    },
});

export default stepStyle;

export type stepVariants = RecipeVariantProps<typeof stepStyle>;
