'use client';
import { FC, useEffect, useRef, useState } from 'react';
import { GoogleMap, Marker, useLoadScript } from '@react-google-maps/api';
import { css } from '@/styled-system/css';
import useDebounce from '@/hooks/useDebounce';
import Crosshair from '../Icons/Crosshair';

export type TPoint = { lat: number; lng: number };
export type TAddress = {
    postalCode?: string;
    blockNo?: string;
    street?: string;
    buildingName?: string;
};

export type MapProps = {
    address: TAddress;
    value: TPoint;
    className?: string;
    onChange: (value: TPoint) => void;
    onChangeAddress: (value: TAddress) => void;
};

const ZOOM = 18;

const Map: FC<MapProps> = ({
    onChange,
    value,
    className = '',
    address: { street = '', postalCode = '' },
}) => {
    const [currentLocation, setCurrentLocation] = useState<TPoint>({
        lat: 0,
        lng: 0,
    });
    const currentLocationButtonRef = useRef<HTMLButtonElement>(null);
    const mapRef = useRef<google.maps.Map | null>(null);
    console.log(process.env.GOOGLE_MAPS_API_KEY);

    const { isLoaded } = useLoadScript({
        googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || '',
        libraries: ['places'],
    });
    const debouncedSearch = useDebounce(
        street && postalCode ? `${street}, ${postalCode}` : '',
        1000
    );

    const handleMapClick = (event: google.maps.MapMouseEvent) => {
        const lat = event?.latLng?.lat() || 0;
        const lng = event?.latLng?.lng() || 0;
        onChange({ lat, lng });
        // const geocoder = new google.maps.Geocoder();
        // geocoder.geocode({ location: { lat, lng } }, (results, status) => {
        //     if (status === 'OK') {
        //         if (results?.[0]) {
        //             const addressComponents = results?.[0]?.address_components;
        //             const postalCodeComponent = addressComponents?.find(
        //                 (component) => component.types.includes('postal_code')
        //             );
        //             const postalCode = postalCodeComponent
        //                 ? postalCodeComponent?.short_name
        //                 : '';
        //         }
        //     } else {
        //         console.error('Geocoder failed due to: ' + status);
        //     }
        // });
    };

    const handleSearch = (data: { searchQuery: string }) => {
        if (!mapRef.current) return;

        const placesService = new google.maps.places.PlacesService(
            mapRef.current
        );

        placesService.findPlaceFromQuery(
            {
                query: data.searchQuery,
                fields: ['geometry'],
            },
            (results, status) => {
                console.info('Search results:', results, status);
                if (
                    status === google.maps.places.PlacesServiceStatus.OK &&
                    results &&
                    results.length > 0 &&
                    mapRef.current
                ) {
                    const location = results?.[0]?.geometry?.location;
                    onChange({
                        lat: location?.lat() || 0,
                        lng: location?.lng() || 0,
                    });
                } else {
                    console.error('Error searching for place:', status);
                }
            }
        );
    };

    useEffect(() => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const { latitude, longitude } = position.coords;
                    if (!value?.lat || !value?.lng) {
                        onChange({ lat: latitude, lng: longitude });
                    }
                    setCurrentLocation({ lat: latitude, lng: longitude });
                },
                (error) => {
                    console.error('Error getting user location:', error);
                }
            );
        } else {
            console.error('Geolocation is not supported by this browser.');
        }
    }, [value]);

    // useEffect(() => {
    //     if (debouncedSearch && isLoaded) {
    //         handleSearch({
    //             searchQuery: debouncedSearch,
    //         });
    //     }
    // }, [debouncedSearch, isLoaded]);

    if (!isLoaded) return <>Loading ...</>;

    return (
        <>
            <GoogleMap
                mapContainerClassName={className}
                center={{
                    lat: value?.lat || 0,
                    lng: value?.lng || 0,
                }}
                zoom={ZOOM}
                onClick={handleMapClick}
                onLoad={(map) => {
                    mapRef.current = map;
                    if (currentLocationButtonRef.current) {
                        map.controls[
                            google.maps.ControlPosition.BOTTOM_LEFT
                        ].push(currentLocationButtonRef.current);
                    }
                }}
                options={{
                    streetViewControl: false,
                }}
            >
                {value?.lat && value?.lng && (
                    <Marker
                        position={{
                            lat: value?.lat || 0,
                            lng: value?.lng || 0,
                        }}
                    />
                )}
            </GoogleMap>
            <button
                ref={currentLocationButtonRef}
                type="button"
                onClick={() => {
                    if (currentLocation?.lat && currentLocation?.lng) {
                        onChange(currentLocation);
                    }
                }}
                className={css({
                    width: '40px',
                    height: '40px',
                    backgroundColor: 'white',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '2px',
                    left: '0px !important',
                    boxShadow: 'rgba(0, 0, 0, 0.3) 0px 1px 4px -1px',
                    userSelect: 'none',
                    appearance: 'none',
                    cursor: 'pointer',
                    margin: '10px',
                    color: '#666',
                    display:
                        !!currentLocation?.lat && !!currentLocation?.lng
                            ? 'flex'
                            : 'none',
                    '&:hover': {
                        color: 'black',
                    },
                })}
            >
                <Crosshair />
            </button>
        </>
    );
};

export default Map;
