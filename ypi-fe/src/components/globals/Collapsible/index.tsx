import { Root } from './style';
import React from 'react';

type Props = {
    children: React.ReactNode;
    collapsed: boolean;
    collapsedHeight?: number;
    onCollapsed?: () => void;
    onExpanded?: () => void;
};

type State = {
    height: string | number;
};

export default class Collapsible extends React.Component<Props, State> {
    contentRef = React.createRef<HTMLDivElement>();
    state = Object.freeze<State>({
        height: this.props.collapsed ? this.props.collapsedHeight ?? 0 : 'auto',
    });

    handleTransitionEnd = () => {
        if (this.props.collapsed) {
            this.props.onCollapsed?.();
            return;
        }
        this.props.onExpanded?.();
        this.setState({ height: 'auto' });
    };

    componentDidUpdate(prevProps: Readonly<Props>): void {
        if (
            prevProps.collapsed !== this.props.collapsed ||
            (this.props.collapsed &&
                prevProps.collapsedHeight !== this.props.collapsedHeight)
        ) {
            const contentHeight =
                this.contentRef.current?.getBoundingClientRect().height ?? 0;
            if (!this.props.collapsed) {
                this.setState({
                    height: contentHeight,
                });
                return;
            }

            if (this.state.height === 'auto') {
                this.setState({ height: contentHeight }, () => {
                    setTimeout(() => this.collapse());
                });
                return;
            }
            this.collapse();
        }
    }

    collapse() {
        this.setState({ height: this.props.collapsedHeight ?? 0 });
    }

    render(): React.ReactNode {
        const { height } = this.state;

        return (
            <Root style={{ height }} onTransitionEnd={this.handleTransitionEnd}>
                <div ref={this.contentRef}>{this.props.children}</div>
            </Root>
        );
    }
}
