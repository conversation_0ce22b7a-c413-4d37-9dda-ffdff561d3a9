import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Typography from '../Typography';
import { containerStyle } from './style';
import { ReactNode } from 'react';

type Props = {
    title?: string;
    children?: ReactNode;
};

export default function TopbarWrapper({ title, children }: Props) {
    return (
        <div className={containerStyle}>
            <div className={css({ flex: 1 })}>
                <Typography
                    color="primary_100"
                    typography="header_24"
                    tag={ETypographyTag.h1}
                    className={css({ textTransform: 'uppercase' })}
                >
                    {title}
                </Typography>
            </div>
            {children}
        </div>
    );
}
