import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Button from '../Button';
import AddCircle from '../Icons/AddCircle';
import Filter from '../Icons/Filter';
import SearchInput from '../SearchInput';
import { debounce } from 'lodash';
import Typography from '../Typography';
import { containerStyle } from './style';

type Props = {
    title?: string;
    onCreateClick?: () => void;
    filterButtonVisible?: boolean;
    onFilterClick?: () => void;
    onSearch?: (text: string) => void;
};

export default function TopBar(props: Props) {
    return (
        <div className={containerStyle}>
            <div className={css({ flex: 1 })}>
                <Typography
                    color="primary_100"
                    typography="header_24"
                    tag={ETypographyTag.h1}
                    className={css({ textTransform: 'uppercase' })}
                >
                    {props.title}
                </Typography>
            </div>
            <div>
                <SearchInput
                    onChange={debounce((s) => {
                        props.onSearch?.(s);
                    }, 500)}
                />
            </div>
            <div className={css({ display: 'flex', ml: 4, gap: '12px' })}>
                {props.filterButtonVisible ? (
                    <Button
                        onClick={props.onFilterClick}
                        visual="outline_primary"
                    >
                        <Filter /> Filter
                    </Button>
                ) : null}
                <Button onClick={props.onCreateClick}>
                    <AddCircle /> Create new
                </Button>
            </div>
        </div>
    );
}
