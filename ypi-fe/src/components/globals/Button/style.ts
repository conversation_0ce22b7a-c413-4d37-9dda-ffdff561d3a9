import { RecipeVariantProps, cva } from '@/styled-system/css';

import Link from 'next/link';
import { styled } from '@/styled-system/jsx';

const buttonStyle = cva({
    base: {
        fontFamily: 'inherit',
        cursor: 'pointer',
        display: 'inline-flex',
        alignItems: 'center',
        gap: '10px',
        justifyContent: 'center',
        fontSize: 'subtitle.15',
        fontWeight: 'semiBold',
        lineHeight: '18.29px',
        transition: 'all 0.2s ease-in-out',
        _disabled: {
            '&:hover': {
                bg: 'inherit',
            },
            cursor: 'not-allowed',
        },
    },
    variants: {
        styles: {
            text: {
                borderRadius: '40px',
                '& svg': {
                    width: '18px',
                    height: '18px',
                },
            },
            icon: {
                borderRadius: '50%',
                width: '32px',
                height: '32px',
                '& svg': {
                    width: '20px',
                    height: '20px',
                },
            },
        },
        size: {
            auto: {
                padding: '4px 12px',
            },
            tiny: {
                width: '118px',
                height: '30px',
            },
            small: {
                width: '118px',
                height: '42px',
            },
            medium: {
                width: '154px',
                height: '42px',
            },
            large: {
                width: '200px',
                height: '42px',
            },
            icon_normal: {
                width: 32,
                height: 32,
            },
        },
        visual: {
            solid_primary: {
                color: 'white',
                bg: 'primary.100',
                '&:hover': {
                    bg: 'primary.80',
                },
                '&:disabled': {
                    background: 'background.100',
                    color: 'secondary.60',
                    '&:hover': {
                        bg: 'background.200',
                    },
                },
            },
            solid_secondary: {
                color: 'white',
                bg: 'secondary.100',
                '&:hover': {
                    bg: 'secondary.80',
                },
            },
            outline_primary: {
                color: 'primary.100',
                bg: 'white',
                border: '1px solid',
                borderColor: 'primary.100',
                '&:hover': {
                    bg: 'primary.20',
                },
            },
            outline_secondary: {
                color: 'secondary.100',
                bg: 'white',
                border: '1px solid',
                borderColor: 'secondary.100',
                '&:hover': {
                    bg: 'secondary.20',
                },
            },
            outline_secondary_60: {
                color: 'secondary.60',
                bg: 'white',
                border: '1px solid',
                borderColor: 'secondary.60',
                '&:hover': {
                    bg: 'secondary.20',
                },
            },
            solid_icon: {
                color: 'white',
                bg: 'secondary.100',
                '&:hover': {
                    bg: 'primary.100',
                },
            },
            outline_icon: {
                color: 'primary.100',
                bg: 'transparent',
                borderWidth: 1,
                borderStyle: 'solid',
                borderColor: 'primary.100',
                '&:hover': {
                    bg: 'primary.20',
                },
            },
            link: {
                _hover: {
                    textDecoration: 'underline',
                },
            },
            link_primary: {
                color: 'primary.100',
                _hover: {
                    textDecoration: 'underline',
                },
            },
            link_error: {
                color: 'warning.100',
                _hover: {
                    textDecoration: 'underline',
                },
            },
            solid_active: {
                color: 'white',
                bg: 'primary.100',
                '&:hover': {
                    bg: 'primary.80',
                },
            },
            solid_inactive: {
                color: 'secondary.60',
                bg: 'background.100',
                '&:hover': {
                    bg: 'background.200',
                },
                '&:disabled': {
                    '&:hover': {
                        bg: 'background.100',
                    },
                },
            },
        },
    },
    defaultVariants: {
        size: 'medium',
        visual: 'solid_primary',
        styles: 'text',
    },
});

export type buttonVariants = RecipeVariantProps<typeof buttonStyle>;

export const ButtonAction = styled('button', buttonStyle);
export const ButtonLink = styled(Link, buttonStyle);
export const ButtonLabel = styled('label', buttonStyle);
