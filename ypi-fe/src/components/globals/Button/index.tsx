import { ButtonHTMLAttributes } from 'react';
import { ReactNode } from 'react';
import {
    ButtonAction,
    ButtonLabel,
    ButtonLink,
    buttonVariants,
} from '@/components/globals/Button/style';

// ButtonProps interface extends ButtonHTMLAttributes which bring HTMLButtonElement properties,
// and adds href and children properties specific to this Button component.
interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
    href?: string;
    htmlFor?: string;
    children: ReactNode;
}

// The Button component which either renders a ButtonLink or a ButtonAction based on the presence of 'href'.
const Button = (props: ButtonProps & buttonVariants) => {
    // Destructuring properties from the props object.
    const { href, htmlFor, children, ...additionalProps } = props;

    if (htmlFor) {
        return (
            <ButtonLabel
                htmlFor={htmlFor}
                className={additionalProps.className}
                visual={additionalProps.visual}
                size={additionalProps.size}
            >
                {children}
            </ButtonLabel>
        );
    }
    // Check if href is provided, rendering the Button as a ButtonLink.
    if (href) {
        return (
            // href is provided, thus a ButtonLink is rendered with the visual and size props applied
            <ButtonLink
                visual={additionalProps.visual}
                size={additionalProps.size}
                className={additionalProps.className}
                href={href}
            >
                {children}
            </ButtonLink>
        );
    }

    // href is not provided rendering Button as a standard Button with the visual and size props applied.
    return <ButtonAction {...additionalProps}>{children}</ButtonAction>;
};

// Export Button as a default export.
export default Button;
