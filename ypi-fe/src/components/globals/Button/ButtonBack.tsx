import React from 'react';
import Button from '.';
import { css } from '@/styled-system/css';
import { useRouter } from 'next/navigation';
import ChevronLeft from '../Icons/ChevronLeft';
import Typography from '../Typography';

const buttonContainer = css({
    display: 'inline-flex',
    gap: 3,
    alignItems: 'center',
    cursor: 'pointer',
});
const labelStyle = css({
    textTransform: 'uppercase',
    flex: 1,
});
export function ButtonBack({ label }: { label: string }) {
    const router = useRouter();
    return (
        <div className={buttonContainer} onClick={() => router.back()}>
            <ChevronLeft />
            <Typography
                className={labelStyle}
                color="secondary_100"
                typography="header_24"
            >
                {label}
            </Typography>
        </div>
    );
}
