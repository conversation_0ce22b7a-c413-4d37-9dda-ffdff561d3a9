import { InputFileBase } from './InputFileBase';
import { TInputFileProps } from './types';
import { InputFileWithRenderLabel } from './WithInputFile';

export function InputFile(props: TInputFileProps) {
    switch (props.variant) {
        case 'withRenderLabel': {
            return <InputFileWithRenderLabel {...props} />;
        }

        default:
            return <InputFileBase {...props} />;
    }
}
