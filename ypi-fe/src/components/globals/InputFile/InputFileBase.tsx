import { uploadFiles } from '@/hooks/upload';
import { flex } from '@/styled-system/patterns';
import { IFileUpload } from '@/utilities/types/entities/upload';
import { ChangeEventHandler, useId, useRef } from 'react';
import { FileItem } from '../FileItem';
import { allowedAll, beforeUpload } from './inputFileHelper';
import { IInputFileProps } from './types';
import toast from 'react-hot-toast';
import CommonMessages from '@/utilities/messages/common';

export function InputFileBase(props: Partial<IInputFileProps>) {
    const {
        name,
        allow = allowedAll,
        accept = '*',
        multiple = true,
        onChange,
        onInvalidFile,
        label,
        renderLabel,
        value = [],
        staffId,
    } = props;

    const id = useId();
    const inputRef = useRef<HTMLInputElement>(null);
    const onClick = () => {
        inputRef.current?.click();
    };

    const handleResetInput = () => {
        if (!!inputRef.current?.value) {
            inputRef.current.value = '';
        }
    };

    const handleChange = async (event: any) => {
        const files = event.target.files;
        if (!files || files?.length == 0) return;
        const isValid = beforeUpload(files, allow);
        if (!isValid) return onInvalidFile?.();
        const res = await uploadFiles({ files, name, staffId });
        const newFiles = res?.data || [];
        onChange?.([...value, ...newFiles], event);
    };

    const handleDelete = (file?: IFileUpload) => {
        const newFiles = value?.filter((item) => item?.id !== file?.id) || [];
        onChange?.(newFiles);
        handleResetInput();
    };

    return (
        <div>
            {renderLabel && <div>{renderLabel({ onClick })}</div>}
            {!!label && <label htmlFor={id}>{label}</label>}
            <input
                ref={inputRef}
                id={id}
                type="file"
                accept={accept}
                onChange={(e: any) => {
                    toast.promise(handleChange(e), {
                        loading: CommonMessages.UpLoadFile,
                        success: CommonMessages.UpLoadFileSuccessful,
                        error: (err) => err.message,
                    });
                }}
                style={{ display: 'none' }}
                multiple={multiple}
            />

            <div
                className={flex({
                    gap: 3,
                    flexDirection: 'column',
                    pt: '12px',
                })}
            >
                {[...(value || [])]?.map((item) => {
                    return (
                        <FileItem
                            file={item}
                            key={`${item?.id}${item.link}`}
                            onDelete={handleDelete}
                            preview={item.link}
                        />
                    );
                })}
            </div>
        </div>
    );
}
