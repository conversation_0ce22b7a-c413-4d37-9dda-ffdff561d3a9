export const beforeUpload = (
    files: FileList | null,
    allowType: string[] | '*'
) => {
    let valid: string | boolean = true;
    if (allowType === '*') return valid;
    if (files) {
        for (const file of files) {
            if (!allowType.includes(file.type)) {
                valid =
                    'Please upload a .jpeg or .png or gif or svg pr webp file!';
            }
        }
    }
    return valid;
};

export const beforeUploadAvatar = (
    files: FileList | null,
    allowType: string[]
) => {
    let valid: string | boolean = true;
    if (files) {
        for (const file of files) {
            if (!allowType.includes(file.type)) {
                valid =
                    'Please upload a .jpeg or .png or gif or svg pr webp file!';
            }
        }
    }
    return valid;
};

export const previewBlobUrl = (files: FileList | null) => {
    // make sure all files isValid before preview
    if (!files || files.length === 0) {
        return [];
    }

    const blobsUrl = Array.from(files).map((file) => {
        return {
            name: file.name,
            url: URL.createObjectURL(file),
        };
    });

    return blobsUrl;
};

export const allowedAll = '*';
export const allowedImageType = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/svg+xml',
    'image/webp',
];
