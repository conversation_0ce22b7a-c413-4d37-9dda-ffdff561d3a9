import { IFileUpload } from '@/utilities/types/entities/upload';
import { ChangeEvent, ReactNode } from 'react';
type TFiles = File[] | FileList | null;
export interface IInputFileProps {
    onChange: (
        files: IFileUpload[],
        e?: ChangeEvent<HTMLInputElement>
    ) => void | Promise<void>;
    accept?: string;
    multiple?: boolean;
    allow?: string[];
    onInvalidFile?: () => void;
    label?: ReactNode;
    renderLabel?: ({ onClick }: { onClick: () => void }) => ReactNode;
    variant?: 'default' | 'withRenderLabel';
    value?: IFileUpload[];
    name?: string;
    staffId?: string;
}

export interface IInputFileWithRenderLabelProps extends IInputFileProps {
    variant: 'withRenderLabel';
    withRenderProps: {
        label: string;
        button: ReactNode;
    };
}

export type TInputFileProps =
    | ({ variant: 'default' } & Partial<IInputFileProps>)
    | (Partial<IInputFileWithRenderLabelProps> & {
          variant: 'withRenderLabel';
      });
