import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import Button from '../Button';
import Typography from '../Typography';
import { InputFileBase } from './InputFileBase';
import { IInputFileWithRenderLabelProps, TInputFileProps } from './types';

export function withRenderLabel(
    Component: (props: Partial<TInputFileProps>) => JSX.Element
) {
    return ({
        withRenderProps,
        ...rest
    }: Partial<IInputFileWithRenderLabelProps>) => {
        const renderLable = ({ onClick }: { onClick: () => void }) => {
            return (
                <div
                    className={flex({
                        gap: '12px',
                        alignItems: 'center',
                    })}
                >
                    <Typography
                        className={css({ flex: 1 })}
                        typography="subtitle_15"
                        color="primary_100"
                    >
                        {withRenderProps?.label}
                    </Typography>

                    <Button
                        onClick={onClick}
                        size="medium"
                        type="button"
                        visual="solid_primary"
                    >
                        {withRenderProps?.button}
                    </Button>
                </div>
            );
        };
        return <Component {...rest} renderLabel={renderLable} />;
    };
}

export const InputFileWithRenderLabel = withRenderLabel(InputFileBase);
