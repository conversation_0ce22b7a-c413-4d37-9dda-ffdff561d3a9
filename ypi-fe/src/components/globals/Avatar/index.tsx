import { ChangeEventHandler, useId } from 'react';
import { CameraIcon } from '../Icons';
import { AvatarVariantProps, styles } from './style';
import {
    allowedImageType,
    beforeUpload,
    beforeUploadAvatar,
} from '../InputFile/inputFileHelper';
import { AVATAR_PLACEHOLDER } from '@/constant';
import Link from 'next/link';
import { Slug } from '@/utilities/types/enums/Slug';

type Props = AvatarVariantProps & {
    src?: string;
    isCanEdit?: boolean;
    onChangeAvatar?: (file?: File) => void;
    onInvalidFile?: () => void;
    isClickable?: boolean;
    containerClass?: string;
    className?: string; // Use this one for AvatarForm
};

export default function Avatar({
    size,
    isCanEdit = false,
    src,
    onChangeAvatar,
    onInvalidFile,
    isClickable = true,
    containerClass = '',
    className,
}: Props) {
    const id = useId();
    const classes = styles({
        size: size,
        showPlaceholder: !src,
    });

    const handleChange: ChangeEventHandler<HTMLInputElement> = (event) => {
        const files = event.target.files;
        if (files?.length == 0) return;
        const isValid = beforeUploadAvatar(files, allowedImageType);
        if (isValid) return onChangeAvatar?.(event.target.files?.[0]);
        return onInvalidFile?.();
    };

    return (
        <div
            className={`${
                classes.container || ''
            } ${containerClass} ${className}`}
        >
            <div className={classes.image}>
                <img src={src || AVATAR_PLACEHOLDER} alt="profile-avatar" />
            </div>
            {isClickable && (
                <Link href={Slug.PROFILE_ME} className={classes.link} />
            )}

            {isCanEdit && (
                <>
                    <label htmlFor={id}>
                        <CameraIcon className={classes.camera} />
                    </label>
                    <input
                        multiple
                        className={classes.input}
                        name={id}
                        id={id}
                        type="file"
                        onChange={handleChange}
                    />
                </>
            )}
        </div>
    );
}
