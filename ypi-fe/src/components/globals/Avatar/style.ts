import { sva } from '@/styled-system/css';
import { RecipeVariantProps } from '@/styled-system/types';

export const styles = sva({
    slots: ['container', 'image', 'camera', 'input', 'link'],
    base: {
        container: {
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            position: 'relative',
        },
        image: {
            borderRadius: '50%',
            height: '100%',
            width: '100%',
        },
        camera: {
            position: 'absolute',
            cursor: 'pointer',
            zIndex: 10,
        },
        input: {
            display: 'none',
        },
        link: {
            borderRadius: '50%',
            position: 'absolute',
            zIndex: 1,
            inset: 0,
            cursor: 'pointer',
        },
    },
    variants: {
        size: {
            tiny: {
                container: {
                    width: '16px',
                    height: '16px',
                },
            },
            small: {
                container: {
                    width: '24px',
                    height: '24px',
                },
            },
            normal: {
                container: {
                    width: '32px',
                    height: '32px',
                },
            },
            large: {
                container: {
                    width: '40px',
                    height: '40px',
                    '& img': {
                        width: '22px',
                        height: '22px',
                    },
                },
            },
            huge: {
                container: {
                    width: '48px',
                    height: '48px',
                },
            },
            profile: {
                container: {
                    width: '220px',
                    height: '220px',
                },
                camera: {
                    bottom: '10%',
                    right: '0',
                },
            },
        },
        showPlaceholder: {
            true: {
                image: {
                    background: 'background.0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                },
            },
            false: {
                image: {
                    '& img': {
                        borderRadius: '50%',
                        height: '100%',
                        width: '100%',
                    },
                },
            },
        },
    },
    defaultVariants: {
        size: 'normal',
        showPlaceholder: true,
    },
});

export type AvatarVariantProps = RecipeVariantProps<typeof styles>;
