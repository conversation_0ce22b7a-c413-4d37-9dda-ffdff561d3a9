import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { IFileUpload } from '@/utilities/types/entities/upload';
import { ReactNode, useState } from 'react';
import { FileIcon } from '../Icons';
import DownloadIcon from '../Icons/DownloadIcon';
import { IDocument } from '@cyntler/react-doc-viewer';
import PreviewFileModal from '../PreviewFileModal';
import { handleDownload } from '@/utilities/globals/function/download';
type TFileBaseProps = {
    icon?: ReactNode;
    onDelete?: (file?: IFileUpload) => void;
    file?: IFileUpload;
    preview?: string;
};

export function FileItem({
    icon = <FileIcon />,
    onDelete,
    file,
    preview,
}: TFileBaseProps) {
    const previewFile: IDocument[] = [
        {
            uri: file?.link || '',
            fileName: file?.name,
            fileType: file?.mineType,
        },
    ];

    const [openPreview, setOpenPreview] = useState(false);

    return (
        <div
            className={flex({
                alignItems: 'center',
                gap: 2,
                px: 0,
                py: '12px',
                rounded: 10,
                width: '100%',
                justifyContent: 'space-between',
            })}
        >
            <button
                type="button"
                rel="noopener noreferrer"
                className={flex({
                    alignItems: 'center',
                    bg: 'background.40',
                    gap: 2,
                    px: 1,
                    py: '12px',
                    rounded: 10,
                    width: '100%',
                })}
                onClick={() => {
                    setOpenPreview(true);
                }}
            >
                {icon && <div>{icon}</div>}

                <div
                    className={css({
                        flex: 1,
                        color: 'main.value',
                        fontSize: 'header_15',
                        lineHeight: '18px',
                    })}
                >
                    {file?.name}
                </div>
            </button>
            {!!preview && (
                <button
                    type="button"
                    className={css({
                        cursor: 'pointer',
                        color: 'primary.100',
                        fontSize: '15px',
                        fontWeight: 600,
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px',
                    })}
                    onClick={async () => {
                        await handleDownload(file?.link, file?.name);
                    }}
                >
                    <DownloadIcon />
                    <p>Download</p>
                </button>
            )}

            {!!onDelete && (
                <div
                    onClick={() => onDelete(file)}
                    className={css({
                        cursor: 'pointer',
                        color: 'warning.100',
                        fontSize: '15px',
                        fontWeight: 600,
                    })}
                >
                    Delete
                </div>
            )}

            <PreviewFileModal
                onClose={() => setOpenPreview(false)}
                documents={previewFile}
                open={openPreview}
            />
        </div>
    );
}
