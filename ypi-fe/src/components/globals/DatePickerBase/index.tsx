'use client';

import { RecipeVariantProps, css, sva } from '@/styled-system/css';
import { IFormFieldBase } from '@/utilities/types/form';
import { useId } from 'react';
import DatePicker, { ReactDatePickerProps } from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { CalendarIcon } from '../Icons';

const dateStyle = sva({
    slots: [
        'container',
        'wrapper',
        'input',
        'label',
        'helperText',
        'suffixIcon',
    ],
    base: {
        container: {
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
        },
        wrapper: {
            position: 'relative',
            '& > div:first-child': {
                width: '100%',
            },
            '& .react-datepicker-popper': {
                zIndex: '50!important',
            },
        },
        input: {
            minHeight: '42px',
            fontFamily: 'inherit',
            cursor: 'text',
            display: 'flex',
            alignItems: 'center',
            padding: '0 18px',
            fontSize: 'body.15',
            fontWeight: 'regular',
            borderRadius: '40px',
            flex: 1,
            gap: 1,
            borderStyle: 'solid',
            borderWidth: 1,
            px: 3,
            rounded: 40,
            w: '100%',
            outline: 'none',
            color: 'secondary.100',
            '&::placeholder': {
                color: 'secondary.60',
            },
        },
        helperText: {
            fontFamily: 'inherit',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
            color: 'warning.100',
        },
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
            width: 'fit-content',
        },
        suffixIcon: {
            position: 'absolute',
            top: '50%',
            right: '20px',
            transform: 'translateY(-50%)',
            cursor: 'pointer',
        },
    },
    variants: {
        labelPosition: {
            top: {},
            left: {
                container: {
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: 'row',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
        variant: {
            contained: {
                input: {
                    borderColor: 'background.40',
                    backgroundColor: 'background.40',
                },
            },
            outline: {
                input: {
                    borderStyle: 'solid',
                    borderWidth: 1,
                    borderColor: 'border.normal',
                },
            },
            ghost: {
                input: {
                    border: 'none',
                    borderWidth: 0,
                    borderColor: 'transparent',
                },
            },
        },
    },
    defaultVariants: {
        labelPosition: 'top',
        variant: 'outline',
    },
});

export type DatePickerVariant = RecipeVariantProps<typeof dateStyle>;

export type DatePickerProps = Omit<IFormFieldBase, 'id' | 'type'> &
    ReactDatePickerProps &
    DatePickerVariant & {
        helperText?: string;
        suffixIcon?: boolean;
        suffixIconClassName?: string;
    };
export const DatePickerBase = ({
    selected,
    onChange,
    label,
    placeholderText = 'DD/MM/YYYY',
    labelPosition = 'top',
    variant = 'contained',
    helperText,
    suffixIcon = true,
    suffixIconClassName,
    ...rest
}: DatePickerProps) => {
    const id = useId();
    const classes = dateStyle({ labelPosition, variant });

    const selectedDate =
        typeof selected !== 'string' ? selected : dayjs(selected).toDate();

    return (
        <div className={classes.container}>
            {label && (
                <label className={classes.label} htmlFor={id}>
                    {label}
                </label>
            )}

            <div>
                <div className={classes.wrapper}>
                    <DatePicker
                        className={classes.input}
                        placeholderText={placeholderText}
                        dateFormat="dd/MM/yyyy"
                        selected={selectedDate}
                        onChange={onChange}
                        autoComplete="off"
                        popperPlacement={'bottom-end'}
                        {...rest}
                        id={id}
                    />
                    {suffixIcon && (
                        <label htmlFor={id} className={classes.suffixIcon}>
                            <CalendarIcon
                                width={20}
                                height={20}
                                className={suffixIconClassName}
                            />
                        </label>
                    )}
                </div>
                {helperText ? (
                    <span className={classes.helperText}>{helperText}</span>
                ) : null}
            </div>
        </div>
    );
};
