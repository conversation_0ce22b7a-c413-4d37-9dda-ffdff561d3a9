.image-gallery-content .image-gallery-slide .image-gallery-image {
    width: fit-content;
    max-height: calc(100vh - 160px);
    display: block;
    object-fit: contain;
    border-radius: 12px;
    margin: auto;
}
.image-gallery-thumbnails-container{
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 16px;
}
.image-gallery-thumbnail-image {
    height: 120px;
    object-fit: cover;
}
.image-gallery-thumbnail{
    display: block;
    border-radius: 12px;
    border: 1px solid transparent;
    overflow: hidden;
    opacity: .6;
}

.image-gallery-thumbnail.active, .image-gallery-thumbnail:focus{
    border: 1px solid var(--ypi-colors-neutrals-10);
    opacity: 1;
}

.image-gallery-thumbnail:hover{
    border: 1px solid var(--ypi-colors-neutrals-10);
}
.image-gallery-left-nav .image-gallery-svg, .image-gallery-right-nav .image-gallery-svg{
    width: 32px;
    height: 32px;
}

.image-gallery-icon:focus{
    outline: none;
}