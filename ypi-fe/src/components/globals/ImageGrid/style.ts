import { RecipeVariantProps, sva } from '@/styled-system/css';

export const styles = sva({
    slots: ['root', 'image', 'imageContainer', 'hiddenItemCount'],
    base: {
        root: {
            display: 'flex',
            gap: 1,
            pt: '10px',
        },
        imageContainer: {
            w: 60,
            h: 36,
            display: 'block',
        },
        image: {
            w: '100%',
            h: '100%',
            objectFit: 'cover',
        },
    },
    variants: {
        visual: {
            grid: {
                root: {
                    flexWrap: 'wrap',
                },
            },
            inline: {
                root: {
                    flexWrap: 'nowrap',
                },
                imageContainer: {
                    '&:last-child': {
                        position: 'relative',
                    },
                },
                hiddenItemCount: {
                    display: 'flex',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0,0,0,0.4)',
                    color: 'white',
                    justifyContent: 'center',
                    alignItems: 'center',
                },
            },
        },
    },
    defaultVariants: {
        visual: 'grid',
    },
});

export type ImageGridVariants = RecipeVariantProps<typeof styles>;
