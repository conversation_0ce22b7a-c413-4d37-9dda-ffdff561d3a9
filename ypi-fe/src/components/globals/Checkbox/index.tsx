'use client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FC, InputHTMLAttributes, useState } from 'react';
import {
    CheckBox,
    CheckboxVariants,
} from '@/components/globals/Checkbox/style';

/**
 * Represents the properties for a Checkbox component.
 *
 * @typedef {object} CheckboxProps
 * @property {boolean} isChecked - Specifies whether the checkbox is checked.
 * @property {function} toggleCheckbox - Event handler function called when the checkbox is toggled.
 * @param {object} event - The event object generated by the checkbox toggle action.
 * @returns {void}
 */
export type CheckboxProps = {
    label?: string;
    isChecked?: boolean;
    onChange?: (value: boolean) => void;
    className?: string;
    inputProps?: Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'checked' | 'type'
    >;
} & CheckboxVariants;

/**
 * @typedef CheckboxProps
 * @type {object}
 * @property {boolean} isChecked - Indicates whether the checkbox is checked.
 * @property {function} toggleCheckbox - Callback function to toggle the checkbox.
 */
const Checkbox: FC<CheckboxProps> = ({
    isChecked,
    onChange,
    label,
    size,
    className,
    inputProps,
    ...props
}) => {
    const [checked, setChecked] = useState(isChecked);
    const isControlled = typeof isChecked !== 'undefined';

    const toggleCheckbox: ChangeEventHandler<HTMLInputElement> = (e) => {
        if (!isControlled) {
            const value = e.currentTarget.checked;
            setChecked(value);
            onChange?.(value);
            return;
        }
        onChange?.(!isChecked);
    };

    return (
        <CheckBox
            size={size}
            className={className}
            checked={isControlled ? isChecked : checked}
            {...props}
        >
            <input
                type="checkbox"
                checked={isChecked}
                onChange={toggleCheckbox}
                {...inputProps}
            />
            <span>{label}</span>
        </CheckBox>
    );
};

export default Checkbox;
