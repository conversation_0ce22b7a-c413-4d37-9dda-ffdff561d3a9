import { cva, RecipeVariantProps } from '@/styled-system/css';
import { styled } from '@/styled-system/jsx';

export const checkboxStyle = cva({
    base: {
        cursor: 'pointer',
        '& span': {
            display: 'flex',
            userSelect: 'none',
            alignItems: 'center',
            fontFamily: 'inherit',
        },
        '& input[type=checkbox]': {
            display: 'none',
        },
        '& span:before': {
            content: '""',
            display: 'block',
            borderRadius: '4px',
            border: '1px solid',
            borderColor: 'secondary.60',
            transition: 'all 0.2s ease-in-out',
        },
    },
    variants: {
        size: {
            small: {
                '& span': {
                    fontSize: 'body.14',
                    fontWeight: 'normal',
                    color: 'secondary.60',
                    gap: '12px',
                },
                '& span:before': {
                    width: '18px',
                    height: '18px',
                },
            },
            medium: {
                '& span': {
                    fontSize: 'body.15',
                    fontWeight: 'normal',
                    color: 'secondary.100',
                    gap: '24px',
                },
                '& span:before': {
                    width: '24px',
                    height: '24px',
                },
            },
        },
        checked: {
            true: {
                '& span:before': {
                    borderColor: 'primary.100',
                    background: 'primary.100',
                    transition: 'all 0.2s ease-in-out',
                },
                '& input[type=checkbox]:checked + span:before': {
                    fontFamily: 'system-ui',
                    content: '"✓"',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'primary.100',
                },
            },
        },
        isFormPartHideCheckbox: {
            true: {
                '& input': {
                    display: 'none !important',
                },

                '& span:before': {
                    display: 'none !important',
                },
                '& input[type=checkbox]:checked + span': {
                    // TODO: check why cannt using theme
                    color: '#4F46E5',
                    fontSize: '15px',
                    fontWeight: 'bold',
                },
                base: {
                    height: '42px',
                    display: 'flex',
                    fontSize: '15px',
                    alignItems: 'center',
                },
            },
        },
    },
});

export const CheckBox = styled('label', checkboxStyle);

export type CheckboxVariants = RecipeVariantProps<typeof checkboxStyle>;
