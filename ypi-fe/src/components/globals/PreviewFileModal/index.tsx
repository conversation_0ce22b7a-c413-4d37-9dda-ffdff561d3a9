import * as React from 'react';
import Modal from '../Modal';
import <PERSON><PERSON><PERSON><PERSON>, {
    DocViewerRenderers,
    IDocument,
} from '@cyntler/react-doc-viewer';
import { previewDoc } from './styles';

export interface PreviewFileModalProps {
    open: boolean;
    onClose: () => void;
    documents: IDocument[];
}

export default function PreviewFileModal({
    open,
    onClose,
    documents,
}: PreviewFileModalProps) {
    return (
        <Modal
            isOpen={open}
            onClose={() => {
                onClose();
            }}
            sizes="lg"
            title="File Preview"
        >
            <DocViewer
                documents={documents}
                className={previewDoc}
                pluginRenderers={DocViewerRenderers}
                style={{
                    minHeight: '70vh',
                }}
                config={{
                    header: {
                        disableHeader: true,
                        disableFileName: true,
                    },
                }}
            />
        </Modal>
    );
}
