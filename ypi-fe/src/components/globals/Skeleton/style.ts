import { css } from '@/styled-system/css';

export const loadingStyle = css({
    display: 'block',
    height: 48,
    borderRadius: 100,
    backgroundColor: 'secondary.5',
    position: 'relative',
    overflow: 'hidden',
    _after: {
        content: '""',
        display: 'block',
        position: 'absolute',
        background:
            'linear-gradient(138deg, rgba(255,255,255,0) 0%, rgb(228, 228, 228) 50%, rgba(255, 255, 255, 0) 100%)',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        animation: 'skeletonLoading 1s infinite',
    },
});
