import { css, RecipeVariantProps, sva } from '@/styled-system/css';

export const multiSelectStyle = sva({
    slots: ['container', 'label', 'multiSelect', 'helperText'],
    base: {
        container: {
            '& > div:first-child': {
                width: '100%',
            },
        },
        multiSelect: {
            height: '42px',
            '& div.dropdown-container': {
                height: '100%',
                backgroundColor: 'background.40 !important',
                borderRadius: '40px !important',
                borderColor: 'transparent !important',
            },
            '& div.dropdown-container:focus-within': {
                borderColor: 'border.normal !important',
                boxShadow: 'none !important',
            },
            '& input[type=checkbox]:checked': {
                accentColor: 'primary.100',
                border: 'transparent',
                appearance: 'auto',
            },
            '& input[type=checkbox]': {
                width: '18px',
                height: '18px',
                mr: '0 !important',
                borderRadius: '10px',
            },
            '& .select-item.selected, &  .select-item.selected:hover': {
                bg: 'background.40 !important',
            },
            '& .select-item': {
                // pl: '42px !important',
            },

            '& .dropdown-container .dropdown-content .panel-content': {
                boxShadow: '0px 4px 6px 0px #3A3A3A26 !important',
                bg: 'background.40 !important',
                borderRadius: '10px !important',
                '& .item-renderer': {
                    display: 'flex',
                    gap: '10px',
                    alignItems: 'center !important',
                },
            },
            '& .dropdown-container div.dropdown-content': {
                zIndex: '50!important',
            },
        },
        label: {
            fontFamily: 'inherit',
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
        helperText: {
            fontFamily: 'inherit',
            transition: 'all 0.2s ease-in-out',
            mt: 1,
            textAlign: 'left',
            fontSize: 'body.12',
            fontWeight: 'regular',
            color: 'warning.100',
        },
    },
    variants: {
        labelPosition: {
            top: {},
            left: {
                container: {
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: 'row',
                },
                label: {
                    lineHeight: '42px',
                },
            },
        },
        variant: {
            contained: {
                multiSelect: {
                    '& div.dropdown-container, & div.dropdown-container[aria-expanded="true"]:focus-within':
                        {
                            border: 'unset !important',
                        },
                },
            },
            outline: {
                multiSelect: {
                    '& div.dropdown-container': {
                        borderStyle: 'solid',
                        borderWidth: 1,
                        borderColor: 'border.normal',
                    },
                },
            },
        },
    },
    defaultVariants: {
        labelPosition: 'top',
        variant: 'outline',
    },
});

export type MultiSelectVariant = RecipeVariantProps<typeof multiSelectStyle>;

export const renderChipType = css({ display: 'flex', gap: 2, pt: '4px' });
