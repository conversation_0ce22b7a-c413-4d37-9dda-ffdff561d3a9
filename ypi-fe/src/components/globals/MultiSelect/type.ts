import { Option } from 'react-multi-select-component';
import { MultiSelectVariant } from './style';

export type TMultiSelectOption = Option;
export type TMultiSelectProps = {
    options: TMultiSelectOption[];
    valueSelected: string[];
    onChange: (value: string[]) => void;
    valueRenderType?: 'chip' | 'other';
    limit?: number;
    placeholder?: string;
    label?: string;
    helperText?: string;
    classNames?: string;
} & MultiSelectVariant;
