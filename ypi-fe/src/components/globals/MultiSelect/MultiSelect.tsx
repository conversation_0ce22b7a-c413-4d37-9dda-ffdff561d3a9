import { css } from '@/styled-system/css';
import Typography from '../Typography';
import { ReactNode, useId, useRef } from 'react';
import { MultiSelect as BaseMultiSelect } from 'react-multi-select-component';
import ChipClearable from '../ChipClearable/ChipClearable';
import { multiSelectStyle, renderChipType } from './style';
import { TMultiSelectOption, TMultiSelectProps } from './type';
import joinClassName from '@/utilities/globals/function/className';
import PlusCircleOutline from '../Icons/PlusCircleOutline';

const RenderChip = ({
    selected,
    _options,
    onClick,
    limit,
    placeholder = '',
}: {
    selected: TMultiSelectOption[];
    _options: TMultiSelectOption[];
    onClick: (value: string) => void;
    limit: number;
    placeholder?: string;
}): ReactNode => {
    const numberOfItem = limit + 1;
    return (
        <div className={renderChipType}>
            {selected.length
                ? selected
                      .slice(0, numberOfItem)
                      .map(({ label, value }, index) => {
                          const isLastItem = limit === index;
                          const restCount = selected.length - limit;
                          if (isLastItem && restCount > 0)
                              return (
                                  <Typography
                                      className={css({ py: '2px' })}
                                      key={restCount}
                                      typography="body_15"
                                      color="secondary_100"
                                  >
                                      + {restCount}
                                  </Typography>
                              );
                          return (
                              <ChipClearable
                                  key={`${value}${label}`}
                                  label={label}
                                  value={value}
                                  onRemove={onClick}
                              />
                          );
                      })
                : placeholder}
        </div>
    );
};

const MultiSelect = ({
    options,
    valueSelected,
    onChange,
    valueRenderType = 'chip',
    limit = 2,
    placeholder,
    label,
    labelPosition,
    variant,
    helperText,
    classNames,
}: TMultiSelectProps) => {
    const id = useId();
    const selectRef = useRef(null);
    const classes = multiSelectStyle({ labelPosition, variant });

    const value = options?.filter((item) =>
        valueSelected?.includes(item?.value)
    );

    const handleRemoveItem = (value: string) => {
        const newSelected = valueSelected?.filter((item) => item !== value);
        onChange(newSelected);
    };

    return (
        <div className={classes.container}>
            {label && (
                <label className={classes.label} htmlFor={id}>
                    {label}
                </label>
            )}
            <div>
                <BaseMultiSelect
                    className={joinClassName(classes.multiSelect, classNames)}
                    options={options}
                    value={value}
                    onChange={(options: TMultiSelectOption[]) => {
                        const selectedValue = options?.map(
                            (item) => item?.value
                        );
                        onChange(selectedValue);
                    }}
                    labelledBy={'Select'}
                    ArrowRenderer={() => <PlusCircleOutline />}
                    valueRenderer={(
                        selected: TMultiSelectOption[],
                        _options: TMultiSelectOption[]
                    ) => (
                        <RenderChip
                            selected={selected}
                            _options={_options}
                            onClick={handleRemoveItem}
                            limit={limit}
                            placeholder={placeholder}
                        />
                    )}
                />

                {helperText ? (
                    <span className={classes.helperText}>{helperText}</span>
                ) : null}
            </div>
        </div>
    );
};

export default MultiSelect;
