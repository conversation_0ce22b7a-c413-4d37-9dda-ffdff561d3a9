import { sva, RecipeVariantProps } from '@/styled-system/css';

const dropdownStyle = sva({
    slots: [
        'root',
        'label',
        'dropdown',
        'dropdown_result',
        'dropdown_arrow',
        'dropdown_list',
        'dropdown_item',
    ],
    base: {
        root: {
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
        },
        label: {
            fontSize: 'body.15',
            fontWeight: 'regular',
            color: 'secondary.60',
        },
        dropdown: {
            position: 'relative',
        },
        dropdown_arrow: {
            top: '50%',
            right: '18px',
            position: 'absolute',
            transform: 'translateY(-50%)',
            transformOrigin: '50% 50%',
            cursor: 'pointer',
            transition: 'all 0.2s ease-in-out',
            color: 'secondary.60',
        },
        dropdown_result: {
            userSelect: 'none',
            cursor: 'pointer',
            borderColor: 'secondary.60',
            borderRadius: '40px',
            padding: '12px 18px',
            color: 'secondary.100',
            fontSize: 'body.15',
            fontWeight: 'regular',
            border: '1px solid',
        },
        dropdown_list: {
            display: 'none',
            position: 'absolute',
            top: 'calc(100% + 5px)',
            left: '0',
            width: '100%',
            borderRadius: '10px',
            background: 'background.40',
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
            zIndex: '1',
            paddingTop: '12px',
            paddingBottom: '12px',
            paddingLeft: '24px',
            paddingRight: '24px',
            '& .container': {
                maxHeight: '294px',
                overflow: 'auto',
                marginRight: '30px',
                '&::-webkit-scrollbar': {
                    width: '5px',
                },
                '&::-webkit-scrollbar-track': {
                    background: 'white',
                },
                '&::-webkit-scrollbar-thumb': {
                    background: '#BABABA',
                    borderRadius: '5px',
                },
            },
        },
        dropdown_item: {
            userSelect: 'none',
            paddingTop: '12px',
            paddingBottom: '12px',
            paddingLeft: '18px',
            paddingRight: '44px',
            fontSize: 'body.15',
            color: 'secondary.100',
            cursor: 'pointer',
            transition: 'all 0.2s ease-in-out',
            position: 'relative',
            '&:hover,&.selected': {
                fontSize: 'subtitle.15',
                fontWeight: 'semiBold',
                color: 'primary.100',
            },
            '&.selected:after': {
                content: '"✓"',
                fontFamily: 'system-ui',
                display: 'block',
                position: 'absolute',
                top: '50%',
                right: '18px',
                transform: 'translateY(-50%)',
            },
        },
    },
    variants: {
        visual: {
            true: {
                dropdown_result: {
                    borderColor: 'primary.100',
                    color: 'primary.100',
                },
                dropdown_arrow: {
                    color: 'primary.100',
                },
            },
        },
        open: {
            true: {
                dropdown_list: {
                    display: 'block',
                },
                dropdown_arrow: {
                    transform: 'translateY(-50%) rotate(180deg)',
                },
            },
        },
    },
});
export default dropdownStyle;
export type dropdownVariants = RecipeVariantProps<typeof dropdownStyle>;
