"use client"
import React, { useState, useEffect } from 'react';
import dropdownStyle, {dropdownVariants} from "@/components/globals/Dropdown/style";
import {Arrow} from "@/components/globals/Icons";

/**
 * Represents an option with a label and value.
 * @interface
 */
interface option {
    label: string;
    value: string;
}

interface Props {
  label?: string;
  options: option[];
  defaultValue: string;
  validator?: (value: string) => string | null;
  onError?: (errorMsg: string | null) => void;
}

/**
 * Dropdown component
 * @param {Object} props - Dropdown props
 * @param {string} props.label - The label for the dropdown
 * @param {Array} props.options - The dropdown options
 * @param {string} props.defaultValue - The default selected value
 * @param {function} props.validator - A function to validate the selected value
 * @param {function} props.onError - A function to handle validation errors
 * @param {Object} props.visual - The visual styles for the dropdown
 */
const Dropdown = ({ label, options, defaultValue, validator, onError,visual }: Props & dropdownVariants) => {

  const [open, setOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(defaultValue);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (validator) {
      const error = validator(selectedValue);
      setError(error);
      if (onError) {
        onError(error);
      }
    }
  }, [selectedValue, validator, onError]);

  const classes = dropdownStyle({ open,visual});
  return (
      <div className={classes.root}>
            {label && <div className={classes.label}>{label}</div>}
            <div className={classes.dropdown}>
                <div className={classes.dropdown_result} onClick={()=>{setOpen(!open)}}>
                  {options.find(item =>  item.value === selectedValue)?.label}
                </div>
                <div className={classes.dropdown_arrow} onClick={()=>{setOpen(!open)}}>
                  <Arrow />
                </div>
              <div className={classes.dropdown_list}>
                <div className="container">
                    {options.map((option) => (
                        <div
                            key={option.value}
                            onClick={() => {
                                setSelectedValue(option.value)
                                setOpen(false)
                            }}
                            className={classes.dropdown_item + " " + (selectedValue === option.value ? 'selected' : "") }
                        >
                            {option.label}
                        </div>
                    ))}
                </div>
              </div>
              {error && <div className="dropdown-error">{error}</div>}
        </div>
      </div>
  )
}


export default Dropdown;