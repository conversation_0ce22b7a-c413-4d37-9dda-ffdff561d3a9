import React from 'react';
import style from './style';
import joinClassName from '@/utilities/globals/function/className';

type Props = {
    children?: React.ReactNode;
    containerClassName?: string;
    className?: string;
};

export default function ScrollView(props: Props) {
    const classNames = style();
    return (
        <div
            className={joinClassName(
                classNames.container,
                props.containerClassName
            )}
        >
            <div className={joinClassName(classNames.content, props.className)}>
                {props.children}
            </div>
        </div>
    );
}
