import { sva } from '@/styled-system/css';

export const drawerStyle = sva({
    slots: ['backdrop', 'root', 'content', 'header', 'close'],
    base: {
        root: {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            pointerEvents: 'none',
            zIndex: 9999,
            visibility: 'hidden',
            transition: 'visibility 0.3s linear',
        },
        backdrop: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            transition: 'opacity 0.3s ease-in-out',
            background: 'secondary.100',
        },
        content: {
            background: 'white',
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            transition: 'transform 0.3s ease-in-out',
            display: 'flex',
            flexDirection: 'column',
            padding: '32px 24px',
            pointerEvents: 'auto',
            minWidth: '380px',
        },
        header: {
            display: 'flex',
            alignItems: 'center',
            '& .modal_header': {
                flex: 1,
            },
        },
        close: {
            cursor: 'pointer',
        },
    },
    variants: {
        open: {
            true: {
                root: {
                    visibility: 'visible',
                },
                backdrop: {
                    opacity: 0.6,
                    pointerEvents: 'auto',
                },
                content: {
                    transform: 'translateX(0%)',
                },
            },
            false: {
                backdrop: { opacity: 0, pointerEvents: 'none' },
                root: {
                    // transitionDelay: '0.3s',
                },
                content: {
                    transform: 'translateX(100%)',
                },
            },
        },
    },
});
