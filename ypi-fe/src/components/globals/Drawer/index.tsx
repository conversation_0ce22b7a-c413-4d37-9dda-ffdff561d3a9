import React from 'react';
import { drawerStyle } from './style';
import Typography from '../Typography';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { Close } from '../Icons';

interface IDrawerProps {
    open: boolean;
    onClose: () => void;
    children: React.ReactNode;
    title?: string;
}

export default function Drawer(props: IDrawerProps) {
    const classes = drawerStyle({ open: props.open });

    return (
        <div className={classes.root}>
            <div onClick={() => props.onClose()} className={classes.backdrop} />
            <div className={classes.content}>
                <div className={classes.header}>
                    <Typography
                        color="primary_100"
                        typography="header_24"
                        className="modal_header"
                        tag={ETypographyTag.div}
                    >
                        {props.title}
                    </Typography>
                    <Close
                        className={classes.close}
                        onClick={() => props.onClose()}
                    />
                </div>
                {props.children}
            </div>
        </div>
    );
}
