import { dayjsLocalizer } from 'react-big-calendar';
import { dayjs } from '@/utilities/globals/function/dateHelper';
//All config calendar in here

// IMPORTANT NOTE: This source code use dayjs to manage date time func if you want to use another library please read document:  https://github.com/jquense/react-big-calendar

dayjs.updateLocale('en', {
    weekStart: 1, // Just custom for calendar, start from Monday ( 0 = Sunday -> 6 = Saturday)
});

export const localizer = dayjsLocalizer(dayjs);
