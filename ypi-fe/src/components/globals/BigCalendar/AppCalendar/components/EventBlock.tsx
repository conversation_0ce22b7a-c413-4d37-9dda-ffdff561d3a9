'use-client';

import { box, flex } from '@/styled-system/patterns';
import { IJob } from '@/utilities/types/entities/job';
import { EventItem } from '../../types';
import Typography from '@/components/globals/Typography';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/globals/Tooltip';
import { useEffect, useState } from 'react';
import { ArrowContainer, Popover } from 'react-tiny-popover';
import EventDetail from './EventDetail';
import dayjs from 'dayjs';
import { CATEGORY_ASSIGN_TASK_LABELS } from '@/constant/task';
import { useSearchParams } from 'next/navigation';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';
export interface EventBlockProps {
    event: EventItem<IJob>;
}

export default function EventBlock({ event }: EventBlockProps) {
    const [open, setOpen] = useState(false);
    const searchParams = useSearchParams();
    const onClose = () => {
        setOpen(false);
    };

    const selectedJobId = searchParams.get('jobId');
    const [confirmDelete, setConfirmDelete] = useState<{
        isOpen: boolean;
        id: string;
    }>({ isOpen: false, id: '' });

    const onDelete = async (id: string) => {
        setConfirmDelete({ isOpen: true, id: id });
    };

    useEffect(() => {
        if (selectedJobId === event?.data?.id) {
            const selector = document.getElementById(`job-${selectedJobId}`);

            if (selector) {
                selector?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });
                setTimeout(() => {
                    selector?.click();
                }, 500);
            }
        }
    }, [selectedJobId]);

    return (
        <>
            <Popover
                isOpen={open}
                positions={['right', 'left']} // preferred positions by priority
                reposition
                onClickOutside={onClose}
                content={({ position, childRect, popoverRect }) => (
                    <ArrowContainer // if you'd like an arrow, you can import the ArrowContainer!
                        position={position}
                        childRect={childRect}
                        popoverRect={popoverRect}
                        arrowColor={'white'}
                        arrowSize={10}
                        arrowStyle={{ opacity: 1 }}
                        className="popover-arrow-container"
                        arrowClassName="popover-arrow"
                    >
                        <EventDetail event={event} onClosePopover={onClose} />
                    </ArrowContainer>
                )}
            >
                <div
                    style={{
                        width: '100%',
                        height: '100%',
                        position: 'relative',
                    }}
                >
                    <Tooltip placement="right" offset={10}>
                        <TooltipTrigger
                            style={{
                                width: '100%',
                                height: '100%',
                                textAlign: 'left',
                                position: 'relative',
                            }}
                        >
                            <div
                                id={`job-${event?.data?.id}`}
                                className={flex({
                                    width: '100%',
                                    height: '100%',
                                    overflowX: 'hidden',
                                    direction: 'column',
                                    gap: 1,
                                    p: '12px',
                                    borderRadius: '12px',
                                    background:
                                        event?.data?.status ===
                                        EAssignTaskStatus['PRE_INSPECTION']
                                            ? 'yellow.20'
                                            : 'primary.20',
                                })}
                                onClick={() => setOpen(true)}
                            >
                                <div
                                    className={flex({
                                        display: 'flex',
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        flexWrap: 'wrap',
                                        gap: '4px',
                                    })}
                                >
                                    <Typography
                                        color="neutrals_20"
                                        typography="body_14"
                                    >
                                        {dayjs(
                                            event?.data?.originalStart
                                        ).format('HH:mm')}
                                    </Typography>
                                    <Typography
                                        color="white"
                                        typography="subtitle_10"
                                        className={box({
                                            width: 'fit-content',
                                            wordBreak: 'break-word',
                                            background:
                                                CATEGORY_ASSIGN_TASK_LABELS?.[
                                                    event?.data
                                                        ?.status as EAssignTaskStatus
                                                ]?.color?.replace('_', '.'),
                                            padding: '4px',
                                            borderRadius: '10px',
                                        })}
                                    >
                                        {
                                            CATEGORY_ASSIGN_TASK_LABELS?.[
                                                event?.data
                                                    ?.status as EAssignTaskStatus
                                            ]?.label
                                        }
                                    </Typography>
                                </div>
                                <div
                                    className={flex({
                                        display: 'flex',
                                        flexDirection: 'column',
                                    })}
                                >
                                    <Typography
                                        color="neutrals_20"
                                        typography="body_14"
                                    >
                                        Job ID:
                                    </Typography>
                                    <Typography
                                        color="neutrals_20"
                                        typography="subtitle_16"
                                        className={box({
                                            wordBreak: 'break-word',
                                        })}
                                    >
                                        {event?.data?.code}
                                    </Typography>
                                </div>
                            </div>
                        </TooltipTrigger>
                        <TooltipContent className="Tooltip"></TooltipContent>
                    </Tooltip>
                </div>
            </Popover>
            <ConfirmModal
                title="Alert"
                open={confirmDelete.isOpen}
                confirmText="Delete"
                message={format(CommonMessages.DeletingAlert, 'task')}
                onCancel={() => setConfirmDelete({ isOpen: false, id: '' })}
                onConfirm={() => {}}
            />
        </>
    );
}
