import { EventItem } from '../../types';
import { <PERSON>Job } from '@/utilities/types/entities/job';
import { divider, flex } from '@/styled-system/patterns';
import { css } from '@/styled-system/css';
import { BookmarkIcon, CalendarIcon } from '@/components/globals/Icons';
import Typography from '@/components/globals/Typography';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Discovery from '@/components/globals/Icons/Discovery';
import { iconStyles } from './styles';
import Location from '@/components/globals/Icons/Location';
import Scan from '@/components/globals/Icons/Scan';
import Profile from '@/components/globals/Icons/Profile';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { FORMAT } from '@/configs/date';
import { ASSIGN_TASK_STATUS_LABELS } from '@/constant/task';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';

export interface EventTooltipProps {
    event: EventItem<IJob> | null;
}

export default function EventTooltip({ event }: EventTooltipProps) {
    return (
        <>
            <div
                className={css({
                    width: '256px',
                    h: '100%',
                    overflowX: 'hidden',
                    flex: 1,
                    backgroundColor: 'white',
                    borderRadius: '10px',
                    padding: '12px',
                })}
            >
                <div
                    className={flex({
                        direction: 'column',
                        gap: 1,
                        h: '100%',
                        overflow: 'auto',
                    })}
                >
                    <h5>
                        <Typography
                            color="primary_100"
                            tag={ETypographyTag.span}
                            typography="body_12"
                        >
                            Job ID:
                        </Typography>
                        <Typography
                            className={css({
                                ml: 1,
                            })}
                            color="primary_100"
                            tag={ETypographyTag.span}
                            typography="subtitle_14"
                        >
                            {event?.data?.code}
                        </Typography>
                    </h5>

                    <hr
                        className={divider({
                            borderBlockEndWidth: 0,
                            height: 0.5,
                        })}
                    />
                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <Discovery
                                className={css({
                                    '&>path': {
                                        stroke: 'secondary.100',
                                    },
                                })}
                                width={12}
                                height={12}
                            />
                        </div>

                        <Typography color="secondary_100" typography="body_12">
                            {event?.data?.code}
                        </Typography>
                    </div>

                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'baseline',
                        })}
                    >
                        <div>
                            <Location
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>
                        <Typography color="secondary_100" typography="body_12">
                            {event?.data?.address}
                        </Typography>
                    </div>

                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <Scan
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>

                        <Typography color="secondary_100" typography="body_12">
                            {event?.data?.tankAirdentity}
                        </Typography>
                    </div>

                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                            color: 'secondary.100',
                        })}
                    >
                        <div>
                            <Profile
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>

                        <Typography color="secondary_100" typography="body_12">
                            {event?.data?.staff}
                        </Typography>
                    </div>
                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <BookmarkIcon width={12} height={12} />
                        </div>

                        <Typography color="secondary_100" typography="body_12">
                            {
                                ASSIGN_TASK_STATUS_LABELS?.[
                                    event?.data?.status as EAssignTaskStatus
                                ]?.label
                            }
                        </Typography>
                    </div>
                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <CalendarIcon
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>

                        <div
                            className={flex({
                                w: '100%',
                                justifyContent: 'space-between',
                            })}
                        >
                            <Typography
                                color="secondary_100"
                                typography="body_12"
                            >
                                {event?.start &&
                                    dayjs(event?.start).format(FORMAT.DATE)}
                            </Typography>
                            <Typography
                                color="secondary_100"
                                typography="body_12"
                            >
                                {event?.start &&
                                    dayjs(event?.start).format(FORMAT.TIME)}
                            </Typography>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
