import { EventItem } from '../../types';
import { IJob } from '@/utilities/types/entities/job';
import { divider, flex } from '@/styled-system/patterns';
import { css } from '@/styled-system/css';
import IconButton from '@/components/globals/IconButton';
import Delete from '@/components/globals/Icons/Delete';
import { CalendarIcon, Close, EditSquare } from '@/components/globals/Icons';
import Typography from '@/components/globals/Typography';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Discovery from '@/components/globals/Icons/Discovery';
import { iconStyles } from './styles';
import Location from '@/components/globals/Icons/Location';
import Scan from '@/components/globals/Icons/Scan';
import Profile from '@/components/globals/Icons/Profile';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { FORMAT } from '@/configs/date';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter, useSearchParams } from 'next/navigation';
import queryString from 'query-string';
import { CATEGORY_ASSIGN_TASK_LABELS } from '@/constant/task';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';
import { EActionTypes } from '@/utilities/types/enums/Form';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';
import { useDeleteAssignTask } from '@/hooks/assign-task';
import { CalendarContext } from '../context';
import { useContext, useState } from 'react';
import { HeadingColors } from '@/components/globals/Typography/style';

export interface EventDetailProps {
    event: EventItem<IJob> | null; // TODO:
    onClosePopover?: () => void;
}

export default function EventDetail({
    onClosePopover,
    event,
}: EventDetailProps) {
    const searchParams = useSearchParams();
    const router = useRouter();

    const selectedDate = dayjs(searchParams.get('date')).isValid()
        ? dayjs(searchParams.get('date')).toDate()
        : new Date();

    const [confirmDelete, setConfirmDelete] = useState<{
        isOpen: boolean;
        id: string;
    }>({ isOpen: false, id: '' });

    const deleteAssignTask = useDeleteAssignTask();
    const { onRefetch } = useContext(CalendarContext);

    const onDelete = async (id: string) => {
        setConfirmDelete({ isOpen: true, id: id });
    };

    const handleNavigate = () => {
        const query = queryString.stringify({
            mode: EActionTypes['EDIT'],
            assignTaskId: event?.data?.id,
            callbackUrl: `${Slug.CALENDAR}?date=${dayjs(selectedDate).format(
                'YYYY-MM-DD'
            )}`,
        });
        if (
            event?.data?.status === EAssignTaskStatus.PRE_INSPECTION &&
            !event?.data?.isStaffSubmitted
        ) {
            router.push(
                `${Slug.TANKS}/${event?.data?.tankId}/assign-task?${query}`
            );
            return;
        }

        if (
            event?.data?.status === EAssignTaskStatus.PRE_INSPECTION &&
            event?.data?.isStaffSubmitted
        ) {
            router.push(
                `${Slug.GR_PRE_INSPECTION}/${event?.data?.id}?${query}`
            );
            return;
        }
        if (
            event?.data?.status === EAssignTaskStatus.PREPARE ||
            event?.data?.status === EAssignTaskStatus.UNRESOLVED
        ) {
            router.push(
                `${Slug.GR_NON_COMPLIANCE}/${event?.data?.id}?${query}`
            );
            return;
        }
        if (
            event?.data?.status === EAssignTaskStatus.COMPLETED &&
            !event?.data?.isConfirmed
        ) {
            router.push(
                `${Slug.GR_NON_COMPLIANCE}/${event?.data?.id}?${query}`
            );
            return;
        }
        if (
            event?.data?.status === EAssignTaskStatus.COMPLETED &&
            event?.data?.isConfirmed
        ) {
            router.push(`${Slug.GR_COMPLETE}/${event?.data?.id}?${query}`);
            return;
        }
    };

    return (
        <>
            <div
                className={css({
                    width: '256px',
                    h: 'fit-content',
                    overflowX: 'hidden',
                    flex: 1,
                    backgroundColor: 'white',
                    borderRadius: '10px',
                    padding: '12px',
                    boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.1)',
                })}
            >
                <div
                    className={flex({
                        direction: 'column',
                        gap: 1,
                        h: '100%',
                        overflow: 'auto',
                    })}
                >
                    <div
                        className={flex({
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: '12px',
                        })}
                    >
                        <IconButton size="small" onClick={handleNavigate}>
                            <EditSquare width={18} height={18} />
                        </IconButton>
                        {event?.data?.status ===
                            EAssignTaskStatus.PRE_INSPECTION &&
                            !event?.data?.isStaffSubmitted && (
                                <IconButton
                                    size="small"
                                    onClick={() =>
                                        onDelete(event?.data?.id || '')
                                    }
                                >
                                    <Delete
                                        className={css({
                                            '&>path': {
                                                fill: 'body.1',
                                            },
                                        })}
                                        width={18}
                                        height={18}
                                    />
                                </IconButton>
                            )}
                        <IconButton onClick={onClosePopover} size="small">
                            <Close
                                className={css({
                                    '&>path': {
                                        fill: 'body.1',
                                    },
                                })}
                                width={18}
                                height={18}
                            />
                        </IconButton>
                    </div>

                    <h5>
                        <Typography
                            tag={ETypographyTag.span}
                            typography="body_14"
                            color={
                                CATEGORY_ASSIGN_TASK_LABELS?.[
                                    event?.data?.status as EAssignTaskStatus
                                ]?.color as HeadingColors
                            }
                        >
                            Job ID:
                        </Typography>
                        <Typography
                            className={css({
                                ml: 1,
                            })}
                            color={
                                CATEGORY_ASSIGN_TASK_LABELS?.[
                                    event?.data?.status as EAssignTaskStatus
                                ]?.color as HeadingColors
                            }
                            tag={ETypographyTag.span}
                            typography="subtitle_16"
                        >
                            {event?.data?.code}
                        </Typography>
                    </h5>

                    <hr
                        className={divider({
                            borderBlockEndWidth: 0,
                            height: 0.5,
                        })}
                    />
                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <Discovery
                                className={css({
                                    '&>path': {
                                        stroke: 'secondary.100',
                                    },
                                })}
                                width={12}
                                height={12}
                            />
                        </div>

                        <Typography color="secondary_100" typography="body_14">
                            {event?.data?.code}
                        </Typography>
                    </div>

                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <Location
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>
                        <Typography color="secondary_100" typography="body_14">
                            {event?.data?.address}
                        </Typography>
                    </div>

                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                            color: 'secondary.100',
                        })}
                    >
                        <div>
                            <Scan
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>

                        <Typography color="secondary_100" typography="body_14">
                            {event?.data?.tankAirdentity}
                        </Typography>
                    </div>

                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                            color: 'secondary.100',
                        })}
                    >
                        <div>
                            <Profile
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>

                        <Typography color="secondary_100" typography="body_14">
                            {event?.data?.staff}
                        </Typography>
                    </div>
                    {/* <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                        })}
                    >
                        <div>
                            <BookmarkIcon width={12} height={12} />
                        </div>

                        <Typography color="secondary_100" typography="body_14">
                            {
                                TASK_STATUS_LABELS?.[
                                    event?.data?.status as EAssignTaskStatus
                                ]?.label
                            }
                        </Typography>
                    </div> */}
                    <div
                        className={flex({
                            gap: 1,
                            alignItems: 'center',
                            color: 'secondary.100',
                        })}
                    >
                        <div>
                            <CalendarIcon
                                className={iconStyles}
                                width={12}
                                height={12}
                            />
                        </div>

                        <div
                            className={flex({
                                w: '100%',
                                justifyContent: 'space-between',
                            })}
                        >
                            <Typography
                                color="secondary_100"
                                typography="body_14"
                            >
                                {event?.data?.originalStart &&
                                    dayjs(event?.data?.originalStart).format(
                                        FORMAT.DATE
                                    )}
                            </Typography>
                            <Typography
                                color="secondary_100"
                                typography="body_14"
                            >
                                {event?.data?.originalStart &&
                                    dayjs(event?.data?.originalStart).format(
                                        FORMAT.TIME
                                    )}
                            </Typography>
                        </div>
                    </div>
                </div>
            </div>
            <ConfirmModal
                title="Alert"
                open={confirmDelete.isOpen}
                confirmText="Delete"
                message={format(CommonMessages.DeletingAlert, 'task')}
                onCancel={() => setConfirmDelete({ isOpen: false, id: '' })}
                onConfirm={async () => {
                    await deleteAssignTask(event?.data?.id || '');
                    await onRefetch();
                }}
            />
        </>
    );
}
