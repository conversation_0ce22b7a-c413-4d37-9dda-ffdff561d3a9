.rbc-time-view {
    border: none;
    color: rgba(112, 116, 125, 1);
}

.rbc-header {
    border: none;
    padding: 6px 12px;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 8px;
}

.rbc-time-content {
    border-top: none;
}
.rbc-time-header-content {
    border: none;
}

.rbc-time-header.rbc-overflowing {
    margin-right: 0 !important;
    border: none;
}

.rbc-time-slot {
    aspect-ratio: 3 / 2;
    min-height: 24px !important;
    min-width: 32px;
    width: auto;
}
.rbc-day-slot {
    background-color: #f3f3f4;
}
.rbc-time-gutter {
    margin-right: 12px;
}

.rbc-label {
    margin-right: 12px;
}
.rbc-header.rbc-today {
    background-color: var(--ypi-colors-primary-100);
    color: white;
    border-radius: 38px;
}

.rbc-header + .rbc-header {
    border: transparent;
}

.rbc-allday-cell {
    display: none;
}

/* Style Event */
/* .rbc-event {
    padding: 0;
    border: none !important;
    border-radius: 10px;
    overflow: auto !important;
} */
.rbc-event {
    padding: 0px !important;
    border: none !important;
    background-color: transparent !important;
}

.rbc-event-label {
    display: none;
}

.rbc-time-gutter .rbc-timeslot-group {
    border-bottom: transparent;
}

.rbc-day-slot .rbc-events-container {
    margin-right: 0;
}

.rbc-current-time-indicator {
    display: none;
}

.rbc-time-header .rbc-time-header-gutter,
.rbc-time-content > .rbc-time-gutter {
    display: none;
}

.rbc-time-content > * + * > * {
    border-left: 3px solid white;
}
.rbc-timeslot-group {
    border-bottom-width: 0px;
    border-bottom-color: transparent;
}
.rbc-day-slot .rbc-time-slot {
    border-top-width: 0px;
    border-top-color: transparent;
}
.rbc-day-slot,
.rbc-day-slot .rbc-events-container {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
}
/* .rbc-day-slot, .rbc-day-slot .rbc-event-content{
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
} */
