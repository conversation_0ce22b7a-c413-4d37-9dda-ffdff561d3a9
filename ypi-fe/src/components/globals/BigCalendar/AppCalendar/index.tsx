'use client';
import BigCalendar, { BigCalendarProps } from '..';
import './styles.css';
import EventBlock from './components/EventBlock';
import { Components, Event } from 'react-big-calendar';

export interface AppCalendarProps extends BigCalendarProps {}

export default function AppCalendar(props: AppCalendarProps) {
    // Component of calendar
    const components: Components<Event, object> | undefined = {
        event: ({ event }: any) => {
            return <EventBlock event={event} />;
        },
        timeSlotWrapper: ({ event }: any) => {
            return (
                <div className="rbc-time-slot">
                    <div className="rbc-label" />
                </div>
            );
        },
    };

    // const memoizedResources = useMemo(
    //     () => ({
    //         onRefetch,
    //     }),
    //     [onRefetch]
    // );

    // <CalendarContext.Provider value={{ onRefetch }}>

    // </CalendarContext.Provider>

    return (
        <BigCalendar
            components={components}
            step={15} // Minutes each 1 time slot
            timeslots={4}
            formats={{
                dayFormat: 'dddd',
                timeGutterFormat: 'HH:mm',
            }}
            // toolbar={false}
            {...props}
        />
    );
}
