'use client';

import { Calendar, CalendarProps } from 'react-big-calendar';
//Style of Big Calendar lib
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { localizer } from './config';

export interface BigCalendarProps extends Omit<CalendarProps, 'localizer'> { }

// This component just should be used to config and set defaults base for calendar

export default function BigCalendar({ ...rest }: BigCalendarProps) {
    return <Calendar {...rest} localizer={localizer} />;
}
