'use client';
import ISideBarItem from '@/utilities/types/side-bar-item';
import { box, flex } from '@/styled-system/patterns';
import { Logo } from '../Icons';
import { logoutButtonStyle } from './style';
import Link from 'next/link';
import Avatar from '../Avatar';
import Logout from '../Icons/Logout';
import { signOut } from 'next-auth/react';
import { useProfile } from '@/hooks/auth';
import { SidebarNavigate } from './SidebarNavigate';

interface Props {
    items: ISideBarItem[];
}

export default function SideBar(props: Props) {
    const { data: user } = useProfile();

    return (
        <div
            className={box({
                height: '100vh',
                overflow: 'hidden',
                maxW: '350px',
                position: 'relative',
            })}
        >
            <div
                className={flex({
                    flexDirection: 'column',
                    p: '32px',
                    pr: '0px',
                    gap: '28px',
                    height: '100vh',
                    overflow: 'hidden',
                })}
            >
                <Link href="/">
                    <Logo width={96} height={58} />
                </Link>
                <div
                    className={box({
                        height: 'calc(100% - 157px)',
                        overflowY: 'auto',
                    })}
                >
                    <SidebarNavigate menu={props.items} />
                </div>
            </div>

            <div
                className={flex({
                    position: 'absolute',
                    bottom: '0',
                    left: 0,
                    pl: '32px',
                    pb: '32px',
                    width: '100%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    zIndex: 10,
                })}
            >
                <Avatar src={user?.avatar} size="large" />
                <button
                    className={logoutButtonStyle}
                    onClick={() =>
                        signOut({
                            callbackUrl: process.env.APP_HOSTNAME,
                        })
                    }
                >
                    <Logout />
                    Logout
                </button>
            </div>
        </div>
    );
}
