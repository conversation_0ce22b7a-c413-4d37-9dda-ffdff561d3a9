import { css, cva } from '@/styled-system/css';

import { flex } from '@/styled-system/patterns';

export const contentContainerStyle = css({
    ml: 4,
    mt: 3,
});

export const collapsedIconStyle = css({
    rotate: '-180deg',
    transition: 'transform 0.2s ease-in-out',
});

export const sideBarItemStyle = cva({
    base: {
        display: 'flex',
        borderRadius: 40,
        fontSize: 'body.16',
        alignItems: 'center',
        justifyContent: 'space-between',
        pl: 3,
        pr: 3,
        pt: 12,
        pb: 12,
        minWidth: 216,
        transition: '0.2s',
    },
    variants: {
        states: {
            selected: {
                backgroundColor: 'primary.100',
                color: 'white',
                fontWeight: 600,
                _hover: {
                    backgroundColor: 'primary.80',
                },
            },
            default: {
                backgroundColor: 'transparent',
                color: 'body.1',
                fontWeight: 400,
                _hover: {
                    backgroundColor: 'primary.20',
                },
            },
            childrenSelected: {
                backgroundColor: 'transparent',
                color: 'primary.100',
                fontWeight: 400,
                _hover: {
                    backgroundColor: 'primary.20',
                },
            },
        },
    },
    defaultVariants: {
        states: 'default',
    },
});

export const logoutButtonStyle = flex({
    alignItems: 'center',
    fontWeight: 600,
    cursor: 'pointer',
    color: 'secondary.100',
    borderRadius: 8,
    '& svg': {
        mr: '12px',
    },
});
