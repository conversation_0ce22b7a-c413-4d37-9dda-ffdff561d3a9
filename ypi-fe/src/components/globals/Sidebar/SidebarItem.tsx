import ISideBarItem from '@/utilities/types/side-bar-item';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';
import { collapsedIconStyle, sideBarItemStyle } from './style';
import { SidebarNavigate } from './SidebarNavigate';
import Collapsible from '../Collapsible';
import ChevronDown from '../Icons/ChevronDown';
import joinClassName from '@/utilities/globals/function/className';

export interface SidebarItemProps extends ISideBarItem {}

export default function SidebarItem({
    path,
    title,
    subItems,
    subMenuClassName,
}: SidebarItemProps) {
    const pathname = usePathname();

    const [clamped, setClamped] = React.useState(false);
    // const ref = React.useRef({ collapsed: true }).current;

    if (subItems && subItems?.length > 0) {
        return (
            <>
                <button
                    className={sideBarItemStyle({
                        states: pathname.startsWith(path)
                            ? 'childrenSelected'
                            : 'default',
                    })}
                    key={path}
                    onClick={() => setClamped(!clamped)}
                >
                    <p>{title}</p>
                    <ChevronDown
                        className={!clamped ? collapsedIconStyle : ''}
                        width={16}
                        height={16}
                    />
                </button>
                <Collapsible collapsed={clamped}>
                    <SidebarNavigate menu={subItems} styles={{ gap: 0 }} />
                </Collapsible>
            </>
        );
    }

    return (
        <Link
            href={path}
            className={joinClassName(
                sideBarItemStyle({
                    states: pathname.startsWith(path) ? 'selected' : 'default',
                }),
                subMenuClassName
            )}
            key={path}
        >
            {title}
        </Link>
    );
}
