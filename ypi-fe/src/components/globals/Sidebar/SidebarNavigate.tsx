import ISideBarItem from '@/utilities/types/side-bar-item';
import * as React from 'react';
import SidebarItem from './SidebarItem';
import { VstackStyles, vstack } from '@/styled-system/patterns';

export interface SidebarNavigateProps {
    menu: ISideBarItem[];
    styles?: VstackStyles;
}

export function SidebarNavigate({ menu, styles }: SidebarNavigateProps) {
    return (
        <div className={vstack({ ...styles })}>
            {menu.map((item) => (
                <SidebarItem key={item.path} {...item} />
            ))}
        </div>
    );
}
