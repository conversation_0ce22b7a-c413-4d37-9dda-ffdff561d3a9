import { inputCalendar, rangeDateWrapepr } from './styles';
import Typography from '@/components/globals/Typography';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { flex } from '@/styled-system/patterns';
import { View, Views } from 'react-big-calendar';
import IconButton from '@/components/globals/IconButton';
import ChevronLeft from '@/components/globals/Icons/ChevronLeft';
import { DatePickerBase } from '@/components/globals/DatePickerBase';
import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { CalendarIcon } from '@/components/globals/Icons';
import { useMemo } from 'react';
import { FORMAT } from '@/configs/date';

export interface RangeDateProps {
    onChangeDate: (date: Date | null) => void;
    date: Date | null;
    viewCalendar: View;
}

export default function RangeDate({
    date,
    onChangeDate,
    viewCalendar,
}: RangeDateProps) {
    const today = dayjs().toDate();
    const onNextClick = () => {
        if (viewCalendar === Views.DAY) {
            onChangeDate(dayjs(date).add(1, 'day').toDate());
        }
        if (viewCalendar === Views.WEEK) {
            onChangeDate(dayjs(date).add(1, 'week').toDate());
        }
    };

    const onPrevClick = () => {
        if (viewCalendar === Views.DAY) {
            onChangeDate(dayjs(date).subtract(1, 'day').toDate());
        }
        if (viewCalendar === Views.WEEK) {
            onChangeDate(dayjs(date).subtract(1, 'week').toDate());
        }
    };

    const valueDate = useMemo(() => {
        if (!date) return '';
        if (viewCalendar === Views.WEEK) {
            const startWeek = dayjs(date)
                .startOf('week')
                .format(FORMAT.DATE_PICKER);
            const endWeek = dayjs(date)
                .endOf('week')
                .format(FORMAT.DATE_PICKER);

            return `${startWeek} - ${endWeek}`;
        }

        if (viewCalendar === Views.DAY) {
            return dayjs(date).format(FORMAT.DATE_PICKER);
        }

        return '';
    }, [date, viewCalendar]);

    return (
        <div className={rangeDateWrapepr}>
            <Typography
                color="secondary_100"
                typography="body_15"
                tag={ETypographyTag.span}
            >
                <button onClick={() => onChangeDate(today)}>Today</button>
            </Typography>
            <div
                className={flex({
                    alignItems: 'center',
                })}
            >
                <IconButton onClick={onPrevClick} size="small">
                    <ChevronLeft width={18} height={18} />
                </IconButton>
                <DatePickerBase
                    name="calendar_picker"
                    selected={date}
                    onChange={(date) => {
                        if (date) {
                            onChangeDate(date);
                        }
                    }}
                    value={valueDate}
                    variant="ghost"
                    calendarStartDay={1}
                    className={inputCalendar}
                    suffixIcon={false}
                    customInput={<button>{valueDate}</button>}
                />
                <IconButton onClick={onNextClick} size="small">
                    <ChevronRight width={18} height={18} />
                </IconButton>
            </div>

            <CalendarIcon width={18} height={18} />
        </div>
    );
}
