// Range date

import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const rangeDateWrapepr = flex({
    alignItems: 'center',
    justifyContent: 'space-between',
    border: '1px solid var(--ypi-colors-secondary-100)',
    borderRadius: '999px',
    height: 'fit-content',
    py: '6px',
    px: '18px',
    minW: '420px',
});

export const inputCalendar = css({
    padding: 0,
    color: 'secondary.100',
    fontSize: 'body.15',
});
