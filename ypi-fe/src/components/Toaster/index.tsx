import { getCssVariable } from '@/utilities/globals/panda';
import { Toaster as RHToaster, toast as RHToast } from 'react-hot-toast';
import { Close } from '../globals/Icons';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import generateUniqueId from '@/utilities/globals/function/generateUniqueId';
import { toastContentStyle } from './style';

type FunctionOrValue<TArg, TResult> = ((r: TArg) => TResult) | TResult;

export default function Toaster() {
    return (
        <RHToaster
            position="top-center"
            toastOptions={{
                success: {
                    style: {
                        background: getCssVariable('colors.primary.100'),
                        color: 'white',
                    },
                    iconTheme: {
                        primary: 'white',
                        secondary: getCssVariable('colors.primary.100'),
                    },
                },
                error: {
                    style: {
                        background: getCssVariable('colors.warning.100'),
                        color: 'white',
                    },
                    iconTheme: {
                        primary: 'white',
                        secondary: getCssVariable('colors.warning.100'),
                    },
                },
            }}
        />
    );
}

function getMessage<T>(msg: FunctionOrValue<T, string>, promiseResponse: T) {
    if (typeof msg === 'function') {
        return msg(promiseResponse);
    }
    return msg;
}

const renderContent = (message: string, id: string) => (
    <div
        className={flex({
            alignItems: 'center',
            fontSize: 'subtitle.15',
        })}
    >
        {message}
        <Close
            className={css({
                ml: '12px',
                width: '16px',
                height: '16px',
                color: 'inherit',
                cursor: 'pointer',
            })}
            onClick={() => RHToast.dismiss(id)}
        />
    </div>
);

function success(message: string) {
    return RHToast.success((t) => renderContent(message, t.id), {
        className: toastContentStyle,
    });
}

function error(message: string) {
    return RHToast.error((t) => renderContent(message, t.id), {
        className: toastContentStyle,
    });
}

function promise<T>(
    promise: Promise<T>,
    message: {
        loading: string;
        success: FunctionOrValue<T, string>;
        error: FunctionOrValue<any, string>;
    }
) {
    const uid = generateUniqueId('toast-');
    return RHToast.promise(
        promise,
        {
            loading: message.loading,
            success: (x) => renderContent(getMessage(message.success, x), uid),
            error: (e) => renderContent(getMessage(message.error, e), uid),
        },
        {
            className: toastContentStyle,
            id: uid,
        }
    );
}

export const toast = {
    success,
    promise,
    error,
};
