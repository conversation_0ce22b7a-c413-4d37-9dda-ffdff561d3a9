import ChevronRight from '@/components/globals/Icons/ChevronRight';
import Table, { IColProps } from '@/components/globals/Table';
import Typography from '@/components/globals/Typography';
import { FORMAT_DATE } from '@/configs/date';
import { CATEGORY_ASSIGN_TASK_LABELS } from '@/constant/task';
import { css } from '@/styled-system/css';
import { getFullAddressTank } from '@/utilities/helpers/tank';
import { IAssignTask } from '@/utilities/types/entities/task';
import { Slug } from '@/utilities/types/enums/Slug';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';
import dayjs from 'dayjs';
import Link from 'next/link';

interface IReminderList {
    data: IAssignTask[];
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
}

export default function ReminderList(props: IReminderList) {
    const columns: IColProps<IAssignTask>[] = [
        {
            dataKey: 'startAt',
            title: 'Date',
            key: 'startAt',
            align: 'left',
            render: (data) =>
                dayjs(data.startAt).format(FORMAT_DATE.DATE_SHORT),
        },
        {
            dataKey: 'code',
            title: 'Job type',
            key: 'code',
            align: 'left',
            className: css({
                whiteSpace: 'nowrap',
            }),
            render: (row) => {
                return (
                    <Typography color="secondary_100" typography="body_15">
                        {
                            CATEGORY_ASSIGN_TASK_LABELS?.[
                                row?.status as EAssignTaskStatus
                            ]?.label
                        }
                    </Typography>
                );
            },
        },
        {
            dataKey: 'code',
            title: 'Job ID',
            key: 'code',
            align: 'left',
            width: 100,
            className: css({
                whiteSpace: 'nowrap',
            }),
        },

        {
            dataKey: 'fullAddress',
            title: 'Address',
            key: 'fullAddress',
            align: 'left',
            render: (data) => (
                <div
                    className={css({
                        width: '300px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.tank?.location
                        ? getFullAddressTank(data?.tank?.location)
                        : ''}
                </div>
            ),
        },
        {
            dataKey: 'staffs',
            title: 'Assigned staff',
            key: 'staffs',
            align: 'left',
            render: (data) => {
                return (
                    <div
                        className={css({
                            width: '200px',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis',
                        })}
                    >
                        {data?.staffs
                            ?.map((item) => item?.fullName)
                            ?.join(', ')}
                    </div>
                );
            },
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            render: (row: IAssignTask) => {
                return (
                    <Link
                        href={`${Slug.CALENDAR}?date=${dayjs(
                            row?.startAt
                        ).format('YYYY-MM-DD')}&jobId=${row?.id}`}
                    >
                        <ChevronRight />
                    </Link>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
        />
    );
}
