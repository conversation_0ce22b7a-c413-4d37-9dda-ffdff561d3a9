import { useEffect, useMemo, useState } from 'react';
import { FormPart } from '@/components/globals/InputForm/FormPart';
import { TMultiSelectOption } from '@/components/globals/MultiSelect/type';
import {
    INonComplianceDetail,
    IPreInspectionDetail,
} from '@/utilities/types/entities/general-report';
import { EFormInputType } from '@/utilities/types/form';
import * as React from 'react';
import { FieldValues, useForm } from 'react-hook-form';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { useRouter, useSearchParams } from 'next/navigation';
import { assignStaffResolver, prepareAssignStaffResolver } from './validation';
import {
    advancedTableWrapper,
    bottomContainerStyle,
    containerPrepareWapper,
    containerWrapper,
    formPartStyle,
    formWrapper,
    rectifyWrapepr,
} from './style';
import { css } from '@/styled-system/css';
import Button from '@/components/globals/Button';
import {
    useAssignTaskStaff,
    useUpdateAssignTaskStaff,
} from '@/hooks/assign-task';
import {
    IAssignTaskStaff,
    IDeffectAssign,
} from '@/utilities/types/entities/assign-task';
import AdvancedAssignStaff from './AdvancedAssignStaff';
import Typography from '@/components/globals/Typography';
import toast from 'react-hot-toast';
import { useDefect } from '@/hooks/general-report';
import { EActionTypes } from '@/utilities/types/enums/Form';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';
import { ITask } from '@/utilities/types/entities/task';

type TFormData = {
    staffs: string[];
    timeLine: string;
    estimatedValue: number;
    acceptRectify: 'yes' | 'no';
};
export interface AssignStaffFormProps {
    data: IPreInspectionDetail | INonComplianceDetail;
    workerOptions?: TMultiSelectOption[];
    defaultValues?: Partial<TFormData>;
    submitText?: string;
    mode?: EActionTypes;
    tasks?: ITask[];
}

function PrepareAssignStaffForm(
    {
        data,
        workerOptions,
        defaultValues,
        submitText,
        mode,
        tasks = [],
    }: AssignStaffFormProps,
    ref: any
) {
    const assign = useAssignTaskStaff();
    const updateAssignTask = useUpdateAssignTaskStaff(data?.id);
    const [isMutating, setIsMutating] = React.useState(false);
    const router = useRouter();
    const [isAdvanced, setIsAdvanced] = useState(false);
    const [advancedAssignTask, setAdvancedAssignTask] = useState<
        Record<string, string[] | undefined> // { defectId: staffId[]}
    >({});
    const { getDefectIds } = useDefect();
    const defectIds = getDefectIds();
    const searchParams = useSearchParams();
    const callbackUrl = searchParams.get('callbackUrl');
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        control,
        watch,
        reset,
    } = useForm({
        defaultValues,
        resolver: prepareAssignStaffResolver,
    });

    const onSubmit = async (value: FieldValues) => {
        setIsMutating(true);
        const taskDate = dayjs(value.timeLine).toISOString();

        const defectAssignedList: IDeffectAssign[] = [];

        if (isAdvanced) {
            for (const defectId of Object.keys(advancedAssignTask)) {
                if (!defectIds.includes(defectId)) continue;

                const staffAssignedList = advancedAssignTask[defectId]?.filter(
                    (item) => value.staffs.includes(item)
                );
                if (!staffAssignedList?.length) continue;

                defectAssignedList.push({
                    defectId,
                    staffs: staffAssignedList as string[],
                });
            }
        } else {
            // Assign staff selected for all the defects
            for (const task of tasks) {
                if (!defectIds.includes(task?.defect?.id)) continue;
                defectAssignedList.push({
                    defectId: task?.defect?.id || '',
                    staffs: value.staffs as string[],
                });
            }
        }

        const payload: IAssignTaskStaff = {
            taskId: data?.id,
            startAt: taskDate,
            endAt: taskDate,
            acceptRectify: value.acceptRectify === 'yes',
            estimatedValue: +value?.estimatedValue
                ?.toString()
                ?.replace(/\D/g, ''),
            defects: defectAssignedList,
        };

        if (
            data?.status === EAssignTaskStatus['PREPARE'] ||
            data?.status === EAssignTaskStatus['UNRESOLVED']
        ) {
            await updateAssignTask(payload)
                .then(() => {
                    if (callbackUrl) {
                        router.push(callbackUrl);
                    } else {
                        router.back();
                    }
                })
                .catch((e: any) => {
                    console.log(e.msg);
                })
                .finally(() => {
                    setIsMutating(false);
                });
        } else {
            await assign(payload)
                .then(() => {
                    if (callbackUrl) {
                        router.push(callbackUrl);
                    } else {
                        router.back();
                    }
                })
                .catch((e) => {})
                .finally(() => {
                    setIsMutating(false);
                });
        }
    };

    const staffsSelectedId = watch('staffs');

    const onAssignAdvance = () => {
        if (!staffsSelectedId || staffsSelectedId?.length === 0) {
            setIsAdvanced(false);
            toast.error('Please designate at least one staff');
            return;
        }

        if (!isAdvanced) {
            // Set all staffs by default for each row
            setAdvancedAssignTask(
                Object.fromEntries(
                    tasks.map((task) => [task?.defect?.id, staffsSelectedId])
                )
            );
        }

        setIsAdvanced((prev) => !prev);
    };

    const staffsSelected = useMemo(() => {
        if (!workerOptions || !staffsSelectedId) {
            return [];
        }

        return workerOptions?.filter((option) =>
            staffsSelectedId?.includes(option.value)
        );
    }, [staffsSelectedId, workerOptions]);

    useEffect(() => {
        if (staffsSelected?.length === 0 && isAdvanced) {
            // Auto close advanced block and toast error if staffs selected is empty
            setIsAdvanced(false);
            toast.error('Please designate at least one staff');
        }
    }, [staffsSelected]);

    const filteredTasks = useMemo(() => {
        if (!tasks?.length) return [];

        const filteredTasks = [];
        for (const task of tasks) {
            if (!defectIds.includes(task?.defect?.id)) continue;
            filteredTasks.push(task);
        }

        return filteredTasks;
    }, [tasks]);

    useEffect(() => {
        if (mode === EActionTypes['EDIT'] && data) {
            reset({
                staffs: data?.staffs?.map((item) => item.id) ?? [],
                timeLine: data?.startAt,
                acceptRectify: data?.acceptRectify ? 'yes' : 'no',
                estimatedValue: data?.estimatedValue,
            });
        }
    }, [data, mode]);

    React.useImperativeHandle(
        ref,
        () => ({
            submitForm: handleSubmit(onSubmit),
            isSubmitting: isMutating,
        }),
        [onSubmit, isMutating]
    );

    return (
        <form
            className={containerPrepareWapper}
            onSubmit={handleSubmit(onSubmit)}
        >
            <div className={formWrapper}>
                <FormPart
                    inputs={[
                        {
                            id: 'acceptRectify',
                            name: 'acceptRectify',
                            type: EFormInputType.Radio,
                            options: [
                                {
                                    value: 'yes',
                                    label: 'Yes',
                                },
                                {
                                    value: 'no',
                                    label: 'No',
                                },
                            ],
                            label: 'Occupier accept to rectify non compliance',
                            labelPosition: 'left',
                            className: css({
                                flexDirection: 'row!important',
                            }),
                            classNameWrapper: rectifyWrapepr,
                            defaultValue: 'true',
                        },
                        {
                            id: 'staffs',
                            name: 'staffs',
                            label: 'Staffs',
                            type: EFormInputType.MultiSelect,
                            variant: 'contained',
                            labelPosition: 'left',
                            placeholder: 'Select Staff',
                            options: workerOptions,
                        },
                    ]}
                    control={control}
                    errors={errors}
                    register={register}
                    setValue={setValue}
                    className={formPartStyle}
                />
                <FormPart
                    inputs={[
                        {
                            id: 'timeLine',
                            name: 'timeLine',
                            label: 'Timeline',
                            type: EFormInputType.DatePicker,
                            variant: 'contained',
                            labelPosition: 'left',
                            showTimeSelect: true,
                            timeFormat: 'HH:mm',
                            timeIntervals: 15,
                            timeCaption: 'time',
                            dateFormat: 'HH:mm dd/MM/yyyy',
                            placeholderText: 'HH:MM DD/MM/YYYY',
                            minDate: new Date(),
                        },
                        {
                            id: 'estimatedValue',
                            name: 'estimatedValue',
                            label: 'Estimated Value',
                            type: EFormInputType.Number,
                            startAdornment: '$',
                            variant: 'contained',
                            labelPosition: 'left',
                        },
                    ]}
                    control={control}
                    errors={errors}
                    register={register}
                    setValue={setValue}
                    className={formPartStyle}
                />

                <div className={advancedTableWrapper}>
                    <button
                        type="button"
                        onClick={onAssignAdvance}
                        className={css({
                            w: 'fit-content',
                            ml: 'auto',
                        })}
                    >
                        <Typography
                            className={css({
                                fontWeight: '600!important',
                            })}
                            color={
                                !isAdvanced ? 'primary_100' : 'secondary_100'
                            }
                        >
                            {isAdvanced ? 'Hide' : 'Advanced'}
                        </Typography>
                    </button>
                    {isAdvanced && (
                        <AdvancedAssignStaff
                            workerOptions={staffsSelected ?? []}
                            data={filteredTasks}
                            advancedAssignTask={advancedAssignTask}
                            onAssign={setAdvancedAssignTask}
                        />
                    )}
                </div>
            </div>
        </form>
    );
}

export default React.memo(React.forwardRef(PrepareAssignStaffForm));
