'use client';

import { TMultiSelectOption } from '@/components/globals/MultiSelect/type';
import { SelectMulti } from '@/components/globals/Select';
import Table, { IColProps } from '@/components/globals/Table';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { IAssignTask, ITask } from '@/utilities/types/entities/task';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { IOption } from '@/utilities/types/form';
import * as React from 'react';

export interface AdvancedAssignStaffProps {
    data: ITask[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
    workerOptions: TMultiSelectOption[];
    onAssign?: React.Dispatch<
        React.SetStateAction<Record<string, string[] | undefined>>
    >;
    advancedAssignTask: Record<string, string[] | undefined>;
}

export default function AdvancedAssignStaff(props: AdvancedAssignStaffProps) {
    const convertOptions: IOption[] = props.workerOptions.map((item) => ({
        id: item.value,
        name: item.label,
    }));

    const columns: IColProps<ITask>[] = [
        {
            dataKey: 'index',
            title: '',
            key: 'index',
            render: (_, idx) => idx + 1,
            width: 60,
        },

        {
            dataKey: 'title',
            title: 'Defects',
            key: 'title',
            align: 'left',
            render: (data: ITask) => {
                return (
                    <Typography
                        color="body_1"
                        typography="body_15"
                        tag={ETypographyTag.h1}
                        className={css({
                            whiteSpace: 'pre-wrap',
                        })}
                    >
                        {data.title}
                    </Typography>
                );
            },
        },
        {
            dataKey: 'staffs',
            title: 'Staffs',
            key: 'staffs',
            align: 'left',
            width: 400,
            render: (data: ITask) => {
                if (typeof data?.defect?.id !== 'string') return <></>;
                const values = props.advancedAssignTask[data.defect.id];
                return (
                    <SelectMulti
                        value={values || []}
                        sizes="sm"
                        className={css({
                            width: '100%',
                        })}
                        variant="contained"
                        onChange={(newValues) => {
                            props.onAssign?.({
                                ...props.advancedAssignTask,
                                [data?.defect?.id as string]: newValues,
                            });
                        }}
                        options={convertOptions}
                    />
                );
            },
        },
    ];

    return (
        <Table<ITask>
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            rowColor="transparent"
            margin="dense"
        />
    );
}
