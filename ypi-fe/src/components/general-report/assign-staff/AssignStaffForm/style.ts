import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const formPartStyle = flex({
    direction: 'row',
    alignItems: 'center',
    gap: 6,
    '& > div': {
        flex: 3,
        alignItems: 'center',
        gap: 3,
        '& > div': {
            flex: 1,
            justifyContent: 'flex-start',
        },
    },
});

export const rectifyWrapepr = css({
    flex: '2!important',
    alignItems: 'center!important',
    '&>label': {
        fontWeight: 'bold',
        lineHeight: '20px',
        color: 'secondary.100',
    },
});

export const formWrapper = flex({
    flexDirection: 'column',
    mt: 6,
    gap: 3,
    h: '100%',
    flex: 1,
});

export const containerWrapper = flex({
    flexDirection: 'column',
    justifyContent: 'space-between',
    h: '100%',
});

export const containerPrepareWapper = flex({
    flexDirection: 'column',
    justifyContent: 'space-between',
    h: 'auto',
    mb: 24,
    mt: -24,
});

export const advancedTableWrapper = flex({
    flexDirection: 'column',
    gap: '12px',
    h: '100%',
    overflow: 'auto',
});

export const bottomContainerStyle = flex({
    justifyContent: 'flex-end',
    borderTop: '1px solid',
    borderColor: 'border.normal',
    gap: '12px',
    pt: 3,
    pb: 3,
});
