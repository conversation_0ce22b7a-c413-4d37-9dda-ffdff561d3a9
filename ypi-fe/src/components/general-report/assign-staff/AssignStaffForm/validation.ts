import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { dayjs } from '@/utilities/globals/function/dateHelper';

const newSchema = {
    timeLine: z.coerce.date().refine(
        (data) => {
            return dayjs(data).isSameOrAfter(dayjs());
        },
        {
            message: `Timeline should be after ${dayjs().format(
                'DD/MM/YYYY HH:mm'
            )}`,
        }
    ),
    staffs: z.array(z.string()),
    acceptRectify: z.string(),
    estimatedValue: z.string().or(z.number().int().optional()).optional(),
};

const prepareAssignStaffSchema = {
    timeLine: z.coerce.date().refine(
        (data) => {
            return dayjs(data).isSameOrAfter(dayjs());
        },
        {
            message: `Timeline should be after ${dayjs().format(
                'DD/MM/YYYY HH:mm'
            )}`,
        }
    ),
    staffs: z.array(z.string()),
    acceptRectify: z.string(),
    estimatedValue: z.string().or(z.number().int().optional()).optional(),
};

export const assignStaffResolver = zodResolver(z.object(newSchema));
export const prepareAssignStaffResolver = zodResolver(
    z.object(prepareAssignStaffSchema)
);
