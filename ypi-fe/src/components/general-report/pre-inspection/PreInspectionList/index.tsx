import Table, { IColProps } from '@/components/globals/Table';

import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { FORMAT_DATE } from '@/configs/date';
import { IPreInspection } from '@/utilities/types/entities/general-report';
import { MailIcon } from '@/components/globals/Icons';
import { Slug } from '@/utilities/types/enums/Slug';
import { css } from '@/styled-system/css';
import dayjs from 'dayjs';
import { flex } from '@/styled-system/patterns';
import { getFullAddressTank } from '@/utilities/helpers/tank';
import { useRouter } from 'next/navigation';
import { addressStyle, btnSendEmailStyle } from './style';

interface IPreInspectionList {
    data: IPreInspection[];
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    handleSendEmail?: (taskId: string) => void;
    onClick?: (rowData: any) => void;
}

export default function PreInspectionList(props: IPreInspectionList) {
    const router = useRouter();
    const columns: IColProps<IPreInspection>[] = [
        {
            dataKey: 'startAt',
            title: 'Date',
            key: 'startAt',
            align: 'left',
            render: (data) =>
                dayjs(data.startAt).format(FORMAT_DATE.DATE_SHORT),
        },
        {
            dataKey: 'code',
            title: 'Job ID',
            key: 'code',
            align: 'left',
            className: css({
                whiteSpace: 'nowrap',
            }),
        },

        {
            dataKey: 'fullAddress',
            title: 'Address',
            key: 'fullAddress',
            align: 'left',
            render: (data) => (
                <div
                    className={css({
                        width: '400px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.tank?.location
                        ? getFullAddressTank(data?.tank?.location)
                        : ''}
                </div>
            ),
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            render: (row) => {
                return (
                    <div
                        className={flex({
                            gap: 3,
                            justifyContent: 'space-between',
                        })}
                    >
                        <MailIcon
                            onClick={(e) => {
                                e.stopPropagation();
                                props.handleSendEmail?.(row?.id);
                            }}
                            className={btnSendEmailStyle}
                        />
                        <ChevronRight />
                    </div>
                );
            },
        },
    ];

    const handleClick = (rowData: any) => {
        router.push(`${Slug.GR_PRE_INSPECTION}/${rowData.id}`);
    };

    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
