import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const scrollViewContainer = flex({
    flex: 1,
    direction: 'column',
    pl: 1,
    pr: 1,
    display: 'flex',
    gap: '12px',
});

export const footerWrapper = flex({
    px: 0,
    py: 3,
    ml: 0,
    mr: 0,
    justifyContent: 'flex-end',
    borderTop: '1px solid ',
    borderColor: 'border.normal',
    gap: '12px',
});

export const emailIconStyle = css({
    '&>path': {
        stroke: 'primary.100',
        strokeWidth: '2px',
    },
});
