'use client';

import { useEffect, useMemo, useRef, useState } from 'react';
import { emailIconStyle, footerWrapper, scrollViewContainer } from './style';
import Button from '@/components/globals/Button';
import Link from 'next/link';
import PreInspectionHeader from '@/components/general-report/pre-inspection-detail/PreInspectionHeader';
import ScrollView from '@/components/globals/ScrollView';
import TaskList from '@/components/tasks/TaskList';
import TopBar from '@/components/manage-customers/TopBar';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { useDefect, useDownloadEmailContent } from '@/hooks/general-report';
import { useRouter } from 'next/navigation';
import { Slug } from '@/utilities/types/enums/Slug';
import { INonComplianceDetail } from '@/utilities/types/entities/general-report';
import { MailIcon } from '@/components/globals/Icons';
import { SendEmailModal } from '../pre-inspection-detail/SendEmailModal';
import JobProgress, {
    EJobProgressStep,
} from '@/components/manage-customers/JobProgress';
import { EAssignTaskStatus, ETaskStatus } from '@/utilities/types/enums/Task';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Typography from '@/components/globals/Typography';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import PrepareAssignStaffForm from '../assign-staff/AssignStaffForm/PrepareAssignStaffForm';
import { useStaffAllow } from '@/hooks/staff';
import { EActionTypes } from '@/utilities/types/enums/Form';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import { downloadFileBlob } from '@/utilities/globals/function/download';
import toast from 'react-hot-toast';
import CommonMessages from '@/utilities/messages/common';

type TProps = {
    params: { id: string };
    searchParams: { callbackUrl: string } & {
        [key: string]: string | string[] | undefined;
    };

    data?: INonComplianceDetail;
};

export default function PrepareNonComplianceDetailPage({
    data,
    searchParams,
}: TProps) {
    const router = useRouter();
    const { getDefectIds, setDefectId, removeAllDefectIds } = useDefect();
    const [checkboxes, setCheckboxes] = useState<string[]>([]);
    const [open, setOpen] = useState(false);

    const formRef = useRef<any>(null);

    const handleClick = (defectId: string) => {
        setDefectId(defectId);
        setCheckboxes(getDefectIds());
    };

    const handleClickSendEmail = () => {
        setOpen(!open);
    };

    console.log(getDefectIds());
    const staffsSelectedId = formRef?.current?.watch?.('staffs');

    useEffect(() => {
        if (Array.isArray(data?.tasks)) {
            removeAllDefectIds();
            setCheckboxes(
                data?.tasks
                    ?.filter((item) => !!item.selected && item?.defect?.id)
                    .map((item) => {
                        setDefectId(item?.defect?.id || '');
                        return item?.defect?.id || '';
                    }) || []
            );
        }
    }, [JSON.stringify(data?.tasks)]);

    const { trigger: downloadEmail, isMutating } = useDownloadEmailContent(
        EGeneralReport.NON_COMPLIANCE_DOWNLOAD_EMAIL
    );

    async function downloadPdf() {
        const response = await downloadEmail({
            assignTaskId: data?.id || '',
        });

        await downloadFileBlob({
            file: response,
            filename: 'NoneComplianceEmailContent',
        });
    }

    const { data: workers } = useStaffAllow();

    const workerOptions = workers?.map((item) => ({
        value: item.id,
        label: item.fullName,
    }));

    if (!data) return null;

    return (
        <>
            <div
                className={flex({
                    flex: 1,
                    direction: 'column',
                })}
            >
                <TopBar
                    onBack={() => {
                        router.back();
                    }}
                    title={`Job ID`}
                    subTitle={data?.code}
                    primaryButtonVisible={false}
                    renderButtons={() =>
                        searchParams?.tankId && (
                            <JobProgress
                                activeStep={
                                    EJobProgressStep['RectifyNonCompliance']
                                }
                            />
                        )
                    }
                />

                <ScrollView className={scrollViewContainer}>
                    <div
                        className={css({
                            mb: 3,
                        })}
                    >
                        <PreInspectionHeader
                            preInspectionDate={data.createdAt}
                            tank={data.tank}
                            location={data.tank.location}
                            staffs={data?.staffs || []}
                        />
                    </div>

                    {data?.status === EAssignTaskStatus['UNRESOLVED'] ? (
                        <>
                            <div
                                className={css({
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '12px',
                                })}
                            >
                                <Typography
                                    color="primary_100"
                                    typography="header_24"
                                    tag={ETypographyTag.h1}
                                >
                                    The rectified Defects
                                </Typography>
                                <TaskList
                                    data={data?.tasks?.filter(
                                        (task) =>
                                            task?.status ===
                                            ETaskStatus.COMPLETED
                                    )}
                                    selectedTaskIds={checkboxes}
                                    handleClick={(defectId) => {
                                        handleClick(defectId);
                                    }}
                                />
                            </div>

                            <div
                                className={css({
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '12px',
                                })}
                            >
                                <Typography
                                    color="primary_100"
                                    typography="header_24"
                                    tag={ETypographyTag.h1}
                                >
                                    The unresolve Defects
                                </Typography>
                                <TaskList
                                    data={data?.tasks?.filter(
                                        (task) =>
                                            task?.status ===
                                            ETaskStatus.UN_COMPLETED
                                    )}
                                    selectedTaskIds={checkboxes}
                                    handleClick={(defectId) => {
                                        handleClick(defectId);
                                    }}
                                />
                            </div>
                        </>
                    ) : (
                        <div>
                            <PrepareAssignStaffForm
                                data={data}
                                workerOptions={workerOptions}
                                defaultValues={{
                                    acceptRectify: 'yes',
                                }}
                                tasks={data?.tasks?.filter(
                                    (task) =>
                                        task?.status ===
                                            ETaskStatus['UN_COMPLETED'] ||
                                        task?.status === ETaskStatus['RECTIFY']
                                )}
                                mode={EActionTypes.EDIT}
                                ref={formRef}
                            />
                            <TaskList
                                data={data?.tasks}
                                selectedTaskIds={checkboxes}
                                handleClick={(defectId) => {
                                    handleClick(defectId);
                                }}
                                disabledChecked
                            />
                        </div>
                    )}
                </ScrollView>
                <div className={footerWrapper}>
                    <Button
                        size="medium"
                        type="button"
                        disabled={isMutating}
                        onClick={async () => {
                            toast.promise(downloadPdf(), {
                                loading: CommonMessages.Downloading,
                                success: () => {
                                    return CommonMessages.DownloadSuccessful;
                                },
                                error: (err) => err.message,
                            });
                        }}
                    >
                        <DownloadIcon
                            width={18}
                            height={18}
                            className={css({
                                color: 'white',
                            })}
                        />
                        <span>Download</span>
                    </Button>
                    <Button
                        onClick={handleClickSendEmail}
                        size="medium"
                        type="button"
                        visual="outline_primary"
                    >
                        <MailIcon
                            width={18}
                            height={18}
                            className={emailIconStyle}
                        />
                        <span>Send email</span>
                    </Button>

                    {data?.status === EAssignTaskStatus.UNRESOLVED ? (
                        <Link
                            href={`${Slug.GR_NON_COMPLIANCE}/${
                                data.id
                            }/assign-staff?callbackUrl=${
                                searchParams?.callbackUrl ||
                                Slug.GR_NON_COMPLIANCE
                            }&mode=edit`}
                        >
                            <Button disabled={checkboxes.length === 0}>
                                Create task
                            </Button>
                        </Link>
                    ) : (
                        <Button
                            disabled={
                                checkboxes.length === 0 ||
                                !!formRef?.current?.isSubmitting
                            }
                            onClick={() => {
                                formRef?.current?.submitForm();
                            }}
                        >
                            Update
                        </Button>
                    )}
                </div>
            </div>
            <SendEmailModal
                isOpen={open}
                onClose={handleClickSendEmail}
                tank={data?.tank}
                location={data?.tank?.location}
                staffs={data?.staffs}
                tasks={data?.tasks}
                preInspectionDate={data?.createdAt}
                assignTaskId={data?.id}
                status={data?.status as EAssignTaskStatus}
            />
        </>
    );
}
