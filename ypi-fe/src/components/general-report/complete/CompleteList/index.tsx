import Table, { IColProps } from '@/components/globals/Table';

import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { FORMAT_DATE } from '@/configs/date';
import { IComplete } from '@/utilities/types/entities/general-report';
import { MailIcon } from '@/components/globals/Icons';
import { Slug } from '@/utilities/types/enums/Slug';
import dayjs from 'dayjs';
import { getFullAddressTank } from '@/utilities/helpers/tank';
import { useRouter } from 'next/navigation';
import { actionStyle, btnSendEmailStyle, columnStyle } from './style';
import { css } from '@/styled-system/css';

interface ICompleteListProps {
    data: IComplete[];
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    handleSendEmail?: (taskId: string) => void;
}

export default function CompleteList(props: ICompleteListProps) {
    const router = useRouter();
    const columns: IColProps<IComplete>[] = [
        {
            dataKey: 'code',
            title: 'Job ID',
            key: 'code',
            align: 'left',
            className: columnStyle,
        },
        {
            dataKey: 'completedDate',
            title: 'Day complete',
            key: 'completedDate',
            align: 'left',
            className: columnStyle,
            render: (data) => {
                if (!data?.completedDate) return;
                return dayjs(data?.completedDate).format(
                    FORMAT_DATE.DATE_SHORT
                );
            },
        },
        {
            dataKey: 'sampleTakenDate',
            title: 'Lab sample taken',
            key: 'sampleTakenDate',
            align: 'left',
            className: columnStyle,
            render: (data) => {
                if (!data?.sampleTakenDate) return;
                return dayjs(data?.sampleTakenDate).format(
                    FORMAT_DATE.DATE_SHORT
                );
            },
        },
        {
            dataKey: 'reportDate',
            title: 'Date of report',
            key: 'reportDate',
            align: 'left',
            className: columnStyle,
            render: (data) => {
                if (!data?.reportDate) return;
                return dayjs(data?.reportDate).format(FORMAT_DATE.DATE_SHORT);
            },
        },
        {
            dataKey: 'fullAddress',
            title: 'Address',
            key: 'fullAddress',
            align: 'left',
            render: (data) => (
                <div
                    className={css({
                        width: '300px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.tank?.location
                        ? getFullAddressTank(data?.tank?.location)
                        : ''}
                </div>
            ),
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: '100px',
            render: (row) => {
                return (
                    <div className={actionStyle}>
                        <MailIcon
                            onClick={(e) => {
                                e.stopPropagation();
                                props.handleSendEmail?.(row?.id);
                            }}
                            className={btnSendEmailStyle}
                        />
                        <ChevronRight />
                    </div>
                );
            },
        },
    ];

    const handleClick = (rowData: any) => {
        router.push(`${Slug.GR_COMPLETE}/${rowData.id}`);
    };
    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
