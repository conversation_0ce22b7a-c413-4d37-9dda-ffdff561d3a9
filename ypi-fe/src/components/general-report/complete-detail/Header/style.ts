import { css } from '@/styled-system/css';

export const wrapperStyle = css({
    borderWidth: 1,
    borderColor: 'primary.100',
    borderStyle: 'solid',
    borderRadius: 10,
    gap: 3,
    mt: 4,
    p: 3,
});

export const containerStyle = css({
    display: 'flex',
    gap: 6,
});

export const projectInfoStyle = css({
    display: 'flex',
    gap: '12px',
    w: '100%',
    flexDirection: 'column',
});

export const projectInfoItemLeft = css.raw({
    minWidth: '232px',
});

export const projectInfoItem = css.raw({
    display: 'flex',
    gap: '12px',
    '& > p': {
        minWidth: '120px',
    },
});

export const expandedStyle = css({ flex: 1 });

export const headingTitle = css({
    w: '145px',
});

export const dividerStyle = css({
    borderTop: 1,
    borderColor: 'border.field',
    borderStyle: 'solid',
    mt: 3,
    mb: 3,
});

export const tankInfoRightStyle = css({
    display: 'flex',
    gap: 3,
    flexDirection: 'row',
    justifyContent: 'space-between',
    w: '100%',
});
