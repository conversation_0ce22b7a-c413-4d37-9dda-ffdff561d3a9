import dayjs from 'dayjs';
import toast from 'react-hot-toast';
import { KeyedMutator } from 'swr';
import Typography from '@/components/globals/Typography';
import { emptyStyle, fileContainerStyle, fileNameStyle, styles } from './style';
import Button from '@/components/globals/Button';
import Upload from '@/components/globals/Icons/Upload';
import { Close, FileIcon } from '@/components/globals/Icons';
import IconButton from '@/components/globals/IconButton';
import { ILabReport } from '@/utilities/types/entities/lab-report';
import { ChangeEventHandler, useEffect, useState } from 'react';
import { uploadFilePdf } from '@/hooks/general-report';
import CommonMessages from '@/utilities/messages/common';
import { ICompleteDetail } from '@/utilities/types/entities/general-report';
import { useDeleteFile } from '@/hooks/upload';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { format } from '@/utilities/globals/function/string';
import { box, flex } from '@/styled-system/patterns';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import DeleteIcon from '@/components/globals/Icons/DeleteIcon';
import { handleDownload } from '@/utilities/globals/function/download';
import PreviewFileModal from '@/components/globals/PreviewFileModal';
import { IDocument } from '@cyntler/react-doc-viewer';

interface LabReportProps {
    reports: ILabReport[];
    completeId: string;
    mutate: KeyedMutator<ICompleteDetail>;
}

export default function LabReport({
    reports,
    completeId,
    mutate,
}: LabReportProps) {
    const classNames = styles({});
    const [confirmDelete, setConfirmDelete] = useState<{
        isOpen: boolean;
        id: string;
    }>({ isOpen: false, id: '' });
    const [localReports, setLocalReports] = useState<ILabReport[]>([]);
    const deleteFile = useDeleteFile();

    const [previewFile, setPreviewFile] = useState<{
        isOpen: boolean;
        file: IDocument[] | null;
    }>({
        isOpen: false,
        file: null,
    });

    useEffect(() => {
        setLocalReports([...(reports || [])]);
    }, [reports]);

    const onDelete = async (id: string) => {
        setConfirmDelete({ isOpen: true, id });
    };

    const onConfirmDelete = async () => {
        deleteFile(confirmDelete.id)
            .then((res) => {
                setLocalReports(
                    localReports.filter((item) => item.id !== confirmDelete.id)
                );
                setConfirmDelete((pre) => ({ ...pre, isOpen: false, id: '' }));
            })
            .catch(() => {});
    };

    const onCancelDelete = () => {
        setConfirmDelete((pre) => ({ ...pre, isOpen: false, id: '' }));
    };

    const handleUploadFile: ChangeEventHandler<HTMLInputElement> = (event) => {
        const files = event?.target?.files;
        if (files) {
            toast.promise(uploadFilePdf(files[0], completeId), {
                loading: CommonMessages.UpLoadFile,
                success: () => {
                    mutate();
                    return CommonMessages.UpLoadFileSuccessful;
                },
                error: (err) => err.message,
            });
        }
    };

    const onOpenPreviewFile = (file: IDocument) => {
        setPreviewFile({
            file: [file],
            isOpen: true,
        });
    };

    return (
        <>
            <div className={classNames.container}>
                <table className={classNames.table}>
                    <thead className={classNames.thead}>
                        <tr>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Date of report
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Report results
                                </Typography>
                            </th>
                            <th align="left">
                                <div className={classNames.theadReport}>
                                    <Typography
                                        typography="subtitle_15"
                                        color="secondary_100"
                                    >
                                        Report
                                    </Typography>
                                    <Button
                                        className={classNames.theadBtnUpload}
                                        visual="solid_primary"
                                        htmlFor="labReportFileInput"
                                    >
                                        <input
                                            hidden
                                            type="file"
                                            accept=".pdf"
                                            name="labReportFileInput"
                                            id="labReportFileInput"
                                            onChange={handleUploadFile}
                                        />
                                        <Upload /> Upload
                                    </Button>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {localReports.length ? (
                            reports?.map((report, index) => {
                                const parts = report?.link?.split('/');
                                let fileName = '';
                                if (parts?.length) {
                                    fileName = parts[parts.length - 1];
                                }
                                return (
                                    <tr key={`lap_report_${index}`}>
                                        <td width={114}>
                                            {report?.createdAt
                                                ? dayjs(
                                                      report?.createdAt
                                                  ).format('DD/MM/YYYY')
                                                : ''}
                                        </td>
                                        <td>{report?.name}</td>
                                        <td width={250}>
                                            <div
                                                className={flex({
                                                    justifyContent:
                                                        'justify-between',
                                                    alignItems: 'center',
                                                    gap: '12px',
                                                })}
                                            >
                                                <button
                                                    type="button"
                                                    className={
                                                        fileContainerStyle
                                                    }
                                                    onClick={() => {
                                                        onOpenPreviewFile({
                                                            uri: report?.link,
                                                            fileType:
                                                                report?.mineType,
                                                            fileName:
                                                                report?.name,
                                                        });
                                                    }}
                                                >
                                                    <div
                                                        className={
                                                            fileNameStyle
                                                        }
                                                    >
                                                        <span>
                                                            <FileIcon />
                                                        </span>
                                                        <div>{fileName}</div>
                                                    </div>
                                                </button>
                                                <div
                                                    className={flex({
                                                        justifyContent:
                                                            'flex-start',
                                                        alignItems: 'center',
                                                        gap: '12px',
                                                    })}
                                                >
                                                    <button
                                                        type="button"
                                                        onClick={async () => {
                                                            await handleDownload(
                                                                report?.link,
                                                                report?.name
                                                            );
                                                        }}
                                                    >
                                                        <DownloadIcon
                                                            className={box({
                                                                cursor: 'pointer',
                                                                color: 'primary.100',
                                                            })}
                                                        />
                                                    </button>
                                                    <DeleteIcon
                                                        onClick={() =>
                                                            onDelete(report.id)
                                                        }
                                                        className={box({
                                                            cursor: 'pointer',
                                                            color: 'warning.100',
                                                        })}
                                                    />
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                );
                            })
                        ) : (
                            <tr>
                                <td colSpan={3}>
                                    <div className={emptyStyle}>
                                        <Typography
                                            typography="body_15"
                                            color="secondary_40"
                                        >
                                            No data
                                        </Typography>
                                    </div>
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
            <ConfirmModal
                title="Alert"
                open={confirmDelete.isOpen}
                confirmText="Delete"
                message={format(CommonMessages.DeletingAlert, 'report')}
                onCancel={onCancelDelete}
                onConfirm={onConfirmDelete}
            />

            <PreviewFileModal
                open={previewFile.isOpen}
                onClose={() =>
                    setPreviewFile({
                        isOpen: false,
                        file: null,
                    })
                }
                documents={previewFile?.file || []}
            />
        </>
    );
}
