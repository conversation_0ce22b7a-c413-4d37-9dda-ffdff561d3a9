import { css, sva } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const styles = sva({
    slots: ['container', 'table', 'thead', 'theadReport', 'theadBtnUpload'],
    base: {
        container: {
            overflow: 'hidden',
            borderRadius: 10,
            backgroundColor: 'background.40',
            position: 'relative',
            fontSize: 'body.15',
            mt: 3,
        },
        table: {
            width: '100%',
            '& th, td': {
                p: '12px 16px',
            },
        },
        thead: {
            borderBottomWidth: 1,
            borderBottomColor: 'border.normal',
            borderBottomStyle: 'solid',
        },
        theadReport: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        theadBtnUpload: {
            width: '118px',
            height: '30px',
        },
    },
});

export const fileContainerStyle = flex({
    borderRadius: 10,
    backgroundColor: 'white',
    padding: '8px 12px',
});

export const fileNameStyle = css({
    display: 'flex',
    alignItems: 'center',

    '& > div': {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        w: 170,
        marginLeft: '10px',
    },
});

export const emptyStyle = css({
    textAlign: 'center',
});
