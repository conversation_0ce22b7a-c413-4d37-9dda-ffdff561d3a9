import { titleStyle } from './style';
import Typography from '@/components/globals/Typography';
import { flex } from '@/styled-system/patterns';
import LabReportsTable from '../LabReportsTable';
import { ILabReport } from '@/utilities/types/entities/lab-report';

interface LabReportsListProps {
    data: ILabReport[];
}

export default function LabReportsList({ data }: LabReportsListProps) {
    return (
        <div className={flex({ direction: 'column', gap: '16px' })}>
            <div className={titleStyle}>
                <Typography typography="header_20" color="primary_100">
                    Lab Report
                </Typography>
            </div>
            <LabReportsTable data={data} />
        </div>
    );
}
