import { InfoItem } from '@/components/manage-customers/InfoItem';
import {
    containerStyle,
    dividerStyle,
    expandedStyle,
    headingTitle,
    projectInfoItem,
    tankInfoStyle,
    wrapperStyle,
} from './style';
import Typography from '@/components/globals/Typography';
import { flex } from '@/styled-system/patterns';
import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { ITank } from '@/utilities/types/entities/tank';

interface TankInfoProps {
    tank: ITank;
}

export default function TankInfo(props: TankInfoProps) {
    const { tank } = props;
    return (
        <div className={wrapperStyle}>
            <div className={containerStyle}>
                <div>
                    <Typography
                        typography="header_20"
                        color="primary_100"
                        className={headingTitle}
                    >
                        Tank Information
                    </Typography>
                </div>
                <div className={flex({ flexDirection: 'column' })}>
                    <div
                        className={flex({
                            flexDirection: 'column',
                            gap: '12px',
                        })}
                    >
                        <InfoItem
                            className={css(projectInfoItem)}
                            label="Postal code"
                            value={tank?.code}
                            valueStyle={css({ mt: 0, flex: 1 })}
                        />
                        <InfoItem
                            className={css(projectInfoItem)}
                            label="Address"
                            value={`${tank?.location?.street} ${tank?.location?.blockNo} ${tank?.location?.building}`}
                            valueStyle={css({ mt: 0, flex: 1 })}
                        />
                    </div>
                    <div className={dividerStyle} />
                    <div className={tankInfoStyle}>
                        <InfoItem
                            className={css({
                                w: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                flexDirection: 'row',
                                gap: '12px',
                            })}
                            labelClassName={css({
                                minW: '120px',
                                width: 'fit-content',
                            })}
                            valueStyle={css({
                                mt: 0,
                                flex: 1,
                                textTransform: 'capitalize',
                            })}
                            label="Tank type"
                            value={tank?.type}
                            valueColor={'yellow_100'}
                        />
                        <InfoItem
                            className={css({
                                w: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                flexDirection: 'row',
                                gap: '12px',
                            })}
                            labelClassName={css({
                                minW: '120px',
                                width: 'fit-content',
                            })}
                            valueStyle={css({ mt: 0, flex: 1 })}
                            label="Tank dimensions"
                            renderValue={() => (
                                <Typography
                                    className={flex({ gap: '6px' })}
                                    tag={ETypographyTag.div}
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    <div className={expandedStyle}>
                                        L: {tank?.length}
                                    </div>
                                    <div className={expandedStyle}>
                                        W: {tank?.width}
                                    </div>
                                    <div className={expandedStyle}>
                                        H: {tank?.height}
                                    </div>
                                </Typography>
                            )}
                        />
                        <InfoItem
                            className={css({
                                w: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                flexDirection: 'row',
                                gap: '12px',
                            })}
                            labelClassName={css({
                                minW: '120px',
                                width: 'fit-content',
                            })}
                            valueStyle={css({ mt: 0, flex: 1 })}
                            label="Material"
                            value={tank?.material?.name}
                        />

                        <InfoItem
                            className={css({
                                w: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                flexDirection: 'row',
                                gap: '12px',
                            })}
                            labelClassName={css({
                                minW: '120px',
                                width: 'fit-content',
                            })}
                            valueStyle={css({ mt: 0, flex: 1 })}
                            label="EC"
                            value={tank?.effectiveCap}
                        />
                        <InfoItem
                            className={css({
                                w: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                flexDirection: 'row',
                                gap: '12px',
                            })}
                            valueStyle={css({
                                mt: 0,
                                flex: 1,
                                textTransform: 'uppercase',
                            })}
                            labelClassName={css({ w: '120px' })}
                            label="Shape"
                            value={tank?.shape}
                        />

                        <InfoItem
                            className={css({
                                w: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                flexDirection: 'row',
                                gap: '12px',
                            })}
                            labelClassName={css({
                                minW: '120px',
                                width: 'fit-content',
                            })}
                            valueStyle={css({ mt: 0, flex: 1 })}
                            label="Floor level"
                            value={tank?.floorLevel}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
