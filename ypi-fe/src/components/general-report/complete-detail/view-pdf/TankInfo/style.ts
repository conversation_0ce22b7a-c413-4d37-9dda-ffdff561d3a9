import { css } from '@/styled-system/css';

export const wrapperStyle = css({
    mt: 4,
});

export const containerStyle = css({
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: '12px',
});

export const headingTitle = css({
    alignItems: 'self-start',
    maxWidth: '190px',
    w: '190px',
});

export const projectInfoItem = css.raw({
    display: 'flex',
    gap: '12px',
    '& > p:first-child': {
        width: '120px',
    },
    '& > div': {
        width: '100%',
        wordBreak: 'break-all',
    },
});

export const dividerStyle = css({
    mt: 2,
    mb: 2,
    borderTop: 1,
    borderColor: 'border.field',
    borderStyle: 'solid',
});

export const tankInfoStyle = css({
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    flexWrap: 'wrap',
});

export const expandedStyle = css({ flex: 1 });
