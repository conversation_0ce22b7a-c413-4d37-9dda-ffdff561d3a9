import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Table, { IColProps } from '@/components/globals/Table';
import { ITask } from '@/utilities/types/entities/task';
import ImageGrid from '@/components/globals/ImageGrid';
import { flex } from '@/styled-system/patterns';

interface DefectTableProps {
    data: ITask[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function DefectTable({
    data,
    loading,
    canLoadMore,
    onLoadMore,
}: DefectTableProps) {
    const columns: IColProps<any>[] = [
        {
            dataKey: 'index',
            title: '',
            key: 'index',
            render: (_, rowIndex) => {
                const index = rowIndex + 1;
                if (index < 10) return `0${index}`;
                return index;
            },
            width: 60,
        },
        {
            dataKey: 'title',
            title: 'Defects',
            key: 'title',
            align: 'left',
            width: 200,
            render: (data: ITask) => {
                return (
                    <div
                        className={flex({
                            flexDirection: 'column',
                            gap: 1,
                        })}
                    >
                        <Typography
                            color="body_1"
                            typography="body_15"
                            tag={ETypographyTag.h1}
                            className={css({
                                whiteSpace: 'pre-wrap',
                            })}
                        >
                            {data?.title}
                        </Typography>
                        <Typography
                            color="body_1"
                            typography="body_15"
                            tag={ETypographyTag.h1}
                            className={css({
                                whiteSpace: 'pre-wrap',
                            })}
                        >
                            <Typography
                                color="secondary_100"
                                typography="subtitle_15"
                                tag={ETypographyTag.span}
                                className={css({
                                    whiteSpace: 'pre-wrap',
                                })}
                            >
                                Remark:
                            </Typography>
                            {data?.before?.staffRemark}
                        </Typography>
                        <ImageGrid
                            maxItem={4}
                            photos={
                                data?.before?.images?.map((image) => ({
                                    id: image?.id || '',
                                    url: image?.link || '',
                                    key: image?.name || '',
                                    link: image?.link || '',
                                })) || []
                            }
                            visual="inline"
                        />
                    </div>
                );
            },
        },
        {
            dataKey: 'qty',
            title: 'Qty',
            key: 'qty',
            width: 60,
        },
        {
            dataKey: 'size',
            title: 'Size',
            key: 'size',
            width: 100,
        },
        {
            dataKey: 'remark',
            title: 'Remark',
            key: 'remark',
            align: 'left',
            width: 200,
            render: (data: ITask) => {
                return (
                    <div
                        className={flex({
                            flexDirection: 'column',
                            gap: 1,
                        })}
                    >
                        <Typography
                            color="body_1"
                            typography="body_15"
                            tag={ETypographyTag.h1}
                            className={css({
                                whiteSpace: 'pre-wrap',
                            })}
                        >
                            {data?.staffRemark}
                        </Typography>
                        <ImageGrid
                            maxItem={4}
                            photos={
                                data?.images?.map((image) => ({
                                    id: image?.id || '',
                                    url: image?.link || '',
                                    key: image?.name || '',
                                    link: image?.link || '',
                                })) || []
                            }
                            visual="inline"
                        />
                    </div>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={data}
            columns={columns}
            loading={loading}
            rowKey={(row) => row?.id}
            loadMoreProps={{
                canLoadMore: canLoadMore,
                onLoadMore: onLoadMore,
            }}
            rowColor="transparentBorder"
            margin="dense"
        />
    );
}
