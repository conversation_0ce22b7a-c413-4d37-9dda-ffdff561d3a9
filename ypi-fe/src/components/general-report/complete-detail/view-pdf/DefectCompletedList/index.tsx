import { titleStyle, titleRightStyle, infoStyle } from './style';
import Typography from '@/components/globals/Typography';
import { flex } from '@/styled-system/patterns';
import DefectTable from '../DefectTable';
import { ITask } from '@/utilities/types/entities/task';
import { IStaff } from '@/utilities/types/entities/staff';
import dayjs from 'dayjs';
import { FORMAT } from '@/configs/date';

interface DefectCompletedListProps {
    data: ITask[];
    staffs: IStaff[];
    date: string;
}

export default function DefectCompletedList({
    data,
    staffs,
    date,
}: DefectCompletedListProps) {
    return (
        <div className={flex({ direction: 'column', gap: '16px' })}>
            <div className={titleStyle}>
                <div>
                    <Typography typography="header_20" color="primary_100">
                        The Defect List has been completed
                    </Typography>
                </div>
                <div className={titleRightStyle}>
                    <div className={infoStyle}>
                        <p>Staffs:</p>
                        <div>
                            {staffs?.map((staff) => staff.fullName)?.join(', ')}
                        </div>
                    </div>
                    <div className={infoStyle}>
                        <p>Date:</p>
                        <div>
                            {date ? dayjs(date).format(FORMAT.DATE_PICKER) : ''}
                        </div>
                    </div>
                </div>
            </div>
            <DefectTable data={data} />
        </div>
    );
}
