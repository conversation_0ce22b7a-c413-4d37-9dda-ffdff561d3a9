import { css } from '@/styled-system/css';

export const titleStyle = css({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
});

export const titleRightStyle = css({
    display: 'flex',
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
});

export const infoStyle = css({
    display: 'flex',
    flexDirection: 'row',
    gap: '12px',
    alignItems: 'center',
    fontSize: 'body.14',
    '& > p': {
        fontWeight: 'semiBold',
    },
});
