import Typography from '@/components/globals/Typography';
import { containerStyle, signatureImgStyle } from './style';
import { InfoItem } from '@/components/manage-customers/InfoItem';

interface CustomerJobSatisfiedProps {
    customerName: string;
    signatureImg: string;
}

export default function CustomerJobSatisfied({
    signatureImg,
    customerName,
}: CustomerJobSatisfiedProps) {
    return (
        <div className={containerStyle}>
            <Typography typography="header_20" color="primary_100">
                Customer job satisfied
            </Typography>
            <InfoItem label="Report results" value={customerName} />
            <InfoItem
                label="Signature"
                renderValue={() => {
                    return (
                        <img
                            src={signatureImg}
                            className={signatureImgStyle}
                            alt="signature"
                        />
                    );
                }}
            />
        </div>
    );
}
