import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Table, { IColProps } from '@/components/globals/Table';
import ImageGrid from '@/components/globals/ImageGrid';
import { flex } from '@/styled-system/patterns';
import { ILabReport } from '@/utilities/types/entities/lab-report';
import dayjs from 'dayjs';
import { FORMAT } from '@/configs/date';
import PagerIcon from '@/components/globals/Icons/Pager';

interface LabReportsTableProps {
    data: ILabReport[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function LabReportsTable({
    data,
    loading,
    canLoadMore,
    onLoadMore,
}: LabReportsTableProps) {
    const columns: IColProps<ILabReport>[] = [
        {
            dataKey: 'index',
            title: '',
            key: 'index',
            render: (_, rowIndex) => {
                const index = rowIndex + 1;
                if (index < 10) return `0${index}`;
                return index;
            },
            width: 60,
        },
        {
            dataKey: 'index',
            title: 'Date of Report',
            key: 'index',
            render: (data: ILabReport) =>
                data?.createdAt
                    ? dayjs(data?.createdAt).format(FORMAT.DATE_PICKER)
                    : '',
            width: 200,
        },
        {
            dataKey: 'link',
            title: 'Report',
            key: 'link',
            render: (data: ILabReport) => (
                <div
                    className={css({
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        cursor: 'pointer',
                        color: 'secondary.100',
                    })}
                    onClick={() => {
                        window?.open(data.link, '_blank')?.focus();
                    }}
                >
                    <PagerIcon />
                    <Typography typography="body_15" color="secondary_100">
                        {data.name}
                    </Typography>
                </div>
            ),
        },
    ];

    return (
        <Table
            dataSource={data}
            columns={columns}
            loading={loading}
            rowKey={(row) => row?.id}
            loadMoreProps={{
                canLoadMore: canLoadMore,
                onLoadMore: onLoadMore,
            }}
            rowColor="transparentBorder"
            margin="dense"
        />
    );
}
