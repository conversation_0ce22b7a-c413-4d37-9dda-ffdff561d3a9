import dayjs from 'dayjs';
import Typography from '@/components/globals/Typography';
import { signatureImgStyle, signatureStyle, styles } from './style';
// import Button from '@/components/globals/Button';
// import { css } from '@/styled-system/css';
// import Upload from '@/components/globals/Icons/Upload';

interface CustomerJobSatisfiedProps {
    signatureDate: string;
    customerName: string;
    signatureImg: string;
}

export default function CustomerJobSatisfied({
    signatureDate,
    customerName,
    signatureImg,
}: CustomerJobSatisfiedProps) {
    const classNames = styles({});

    return (
        <div className={classNames.container}>
            <table className={classNames.table}>
                <thead className={classNames.thead}>
                    <tr>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Job inspect complete
                            </Typography>
                        </th>
                        <th align="left">
                            <Typography
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                Satisfied signed
                            </Typography>
                        </th>
                        <th align="right">
                            <div className={classNames.theadReport}>
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Signature
                                </Typography>
                                {/* <Button
                                    className={classNames.theadBtnUpload}
                                    visual="solid_primary"
                                    htmlFor="labReportFileInput"
                                >
                                    <input
                                        hidden
                                        className={css({
                                            display: 'none',
                                            position: 'absolute',
                                            w: 0,
                                            h: 0,
                                        })}
                                        type="file"
                                        accept=".pdf,.doc,.docx,application/msword"
                                        name="labReportFileInput"
                                        id="labReportFileInput"
                                    />
                                    <Upload /> Upload
                                </Button> */}
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td width={134}>
                            {signatureDate
                                ? dayjs(signatureDate).format('DD/MM/YYYY')
                                : ''}
                        </td>
                        <td>{customerName}</td>
                        <td width={250} align="center">
                            <div className={signatureStyle}>
                                <img
                                    src={signatureImg}
                                    className={signatureImgStyle}
                                    alt={customerName}
                                />
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
}
