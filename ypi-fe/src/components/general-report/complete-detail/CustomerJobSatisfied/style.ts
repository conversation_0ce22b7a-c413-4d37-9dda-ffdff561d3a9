import { css, sva } from '@/styled-system/css';
import { box } from '@/styled-system/patterns';

export const styles = sva({
    slots: ['container', 'table', 'thead', 'theadReport', 'theadBtnUpload'],
    base: {
        container: {
            overflow: 'hidden',
            borderRadius: 10,
            backgroundColor: 'background.40',
            position: 'relative',
            fontSize: 'body.15',
            mt: 3,
        },
        table: {
            width: '100%',
            '& th, td': {
                p: '12px 16px',
            },
        },
        thead: {
            borderBottomWidth: 1,
            borderBottomColor: 'border.normal',
            borderBottomStyle: 'solid',
        },
        theadReport: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        theadBtnUpload: {
            width: '118px',
            height: '30px',
        },
    },
});

export const signatureStyle = css({
    borderRadius: 10,
    backgroundColor: 'white',
    padding: '8px',
    width: '215px',
    height: '90px',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
});

export const signatureImgStyle = box({
    width: '120px',
    height: '60px',
    borderRadius: '10px',
});

export const signatureBtnClose = css({
    position: 'absolute',
    top: '5px',
    right: '5px',
});
