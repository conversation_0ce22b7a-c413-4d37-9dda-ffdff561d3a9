import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import AnimateHeight from 'react-animate-height';
import ImageGrid from '@/components/globals/ImageGrid';
import ChevronDown from '@/components/globals/Icons/ChevronDown';
import Typography from '@/components/globals/Typography';
import { taleStyles } from './style';
import { ITask } from '@/utilities/types/entities/task';
import { DatePickerBase } from '@/components/globals/DatePickerBase';
import { FORMAT } from '@/configs/date';
import { useUpdateSampleTakenDate } from '@/hooks/assign-task';

interface Props {
    data: ITask[];
    completedDate: string;
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
}

export default function DateCleanAndDisinfectionList({
    data,
    completedDate,
}: Props) {
    const tableRef = useRef<HTMLTableElement | null>(null);
    const [collapsed, setCollapsed] = useState(true);
    const [height, setHeight] = useState<'auto' | number>('auto');
    const [localData, setLocalData] = useState<ITask[]>([]);
    const classNames = taleStyles({ collapsed });
    const updateSamepleDate = useUpdateSampleTakenDate();
    useEffect(() => {
        let t: number | undefined;
        function updateHeight() {
            if (t) {
                cancelAnimationFrame(t);
            }
            t = requestAnimationFrame(() => {
                const height = tableRef.current?.getBoundingClientRect().height;
                setHeight((height ?? 0) + 48);
            });
        }
        updateHeight();
        window.addEventListener('resize', updateHeight);

        return () => {
            if (t) {
                cancelAnimationFrame(t);
            }
            window.removeEventListener('resize', updateHeight);
        };
    }, [collapsed]);

    useEffect(() => {
        setLocalData(data);
    }, [data]);

    return (
        <div className={classNames.root}>
            <AnimateHeight
                contentClassName={classNames.container}
                height={height}
                duration={300}
            >
                <table className={classNames.table} ref={tableRef}>
                    <thead className={classNames.thead}>
                        <tr>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Day complete
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Lab sample taken
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Defects
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Complete photo
                                </Typography>
                            </th>
                        </tr>
                    </thead>
                    <tbody className={classNames.tbody}>
                        {localData.map((item, index) => {
                            return (
                                <tr key={`${item?.id}_${index}`}>
                                    {index === 0 ? (
                                        <>
                                            <td
                                                width={170}
                                                rowSpan={data?.length}
                                            >
                                                {completedDate
                                                    ? dayjs(
                                                          completedDate
                                                      ).format('DD/MM/YYYY')
                                                    : ''}
                                            </td>
                                            <td
                                                width={170}
                                                rowSpan={data?.length}
                                            >
                                                <DatePickerBase
                                                    name="calendar_picker"
                                                    selected={
                                                        dayjs(
                                                            item.datetime
                                                        ).isValid()
                                                            ? dayjs(
                                                                  item.datetime
                                                              ).toDate()
                                                            : new Date()
                                                    }
                                                    onChange={(date) => {
                                                        const foundIndex =
                                                            localData.findIndex(
                                                                (i) =>
                                                                    i.id ===
                                                                    item.id
                                                            );
                                                        if (foundIndex !== -1) {
                                                            const newData = [
                                                                ...localData,
                                                            ];
                                                            newData[
                                                                foundIndex
                                                            ].datetime = dayjs(
                                                                date
                                                            ).format(
                                                                FORMAT.DATE_TIME_FULL
                                                            );
                                                            setLocalData(
                                                                newData
                                                            );
                                                        }
                                                        updateSamepleDate(
                                                            item.id,
                                                            {
                                                                datetime: dayjs(
                                                                    date
                                                                ).format(
                                                                    FORMAT.DATE_TIME_FULL
                                                                ),
                                                            }
                                                        );
                                                    }}
                                                    value={dayjs(
                                                        item.datetime
                                                    ).format(
                                                        FORMAT.DATE_PICKER
                                                    )}
                                                    variant="ghost"
                                                    calendarStartDay={1}
                                                    suffixIcon={false}
                                                    popperPlacement="top-end"
                                                    portalId="root"
                                                />
                                            </td>
                                        </>
                                    ) : null}
                                    <td>{`${index + 1}.  ${item?.title}`}</td>
                                    <td width={200}>
                                        <ImageGrid
                                            photos={
                                                item?.images?.map((image) => ({
                                                    id: image?.id || '',
                                                    url: image?.link || '',
                                                    key: image?.name || '',
                                                    link: image?.link || '',
                                                })) || []
                                            }
                                            visual="inline"
                                        />
                                    </td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
                <div className={classNames.footerPlaceholder} />
                <div
                    className={classNames.footer}
                    onClick={() => {
                        setCollapsed(!collapsed);
                    }}
                >
                    {collapsed ? 'View more' : 'View less'}
                    <ChevronDown />
                </div>
            </AnimateHeight>
        </div>
    );
}
