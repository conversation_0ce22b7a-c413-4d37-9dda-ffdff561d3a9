import { InfoItem } from '@/components/manage-customers/InfoItem';
import { signatureImgStyle } from './style';

interface CustomerJobSatisfiedProps {
    customerName?: string;
    signatureImg?: string;
}

export default function CustomerJobSatisfied({
    signatureImg,
    customerName,
}: CustomerJobSatisfiedProps) {
    return (
        <>
            <InfoItem label="Report results" value={customerName} />
            <InfoItem
                label="Signature"
                renderValue={() => {
                    return (
                        <img
                            src={signatureImg}
                            className={signatureImgStyle}
                            alt="signature"
                        />
                    );
                }}
            />
        </>
    );
}
