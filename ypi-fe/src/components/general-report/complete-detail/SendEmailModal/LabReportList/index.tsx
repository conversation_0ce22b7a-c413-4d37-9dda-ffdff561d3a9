import dayjs from 'dayjs';
import Table, { IColProps } from '@/components/globals/Table';
import { ILabReport } from '@/utilities/types/entities/lab-report';
import { reportResultStyle, reportStyle } from './style';
import { FileIcon } from '@/components/globals/Icons';

interface LabReportListProps {
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
    reports?: ILabReport[];
    completedDate?: string;
    sampleTakenDate?: string;
    reportDate?: string;
}

export default function LabReportList({
    loading,
    canLoadMore,
    onLoadMore,
    reports = [],
    completedDate,
    sampleTakenDate,
    reportDate,
}: LabReportListProps) {
    const columns: IColProps<ILabReport>[] = [
        {
            dataKey: 'day_complete',
            title: 'Day complete',
            key: 'day_complete',
            width: 200,
            render: (_) => {
                if (!completedDate) return;
                return dayjs(completedDate).format('DD/MM/YYYY');
            },
        },
        {
            dataKey: 'lab_sample_taken',
            title: 'Lab sample taken',
            key: 'lab_sample_taken',
            width: 200,
            render: (_) => {
                if (!sampleTakenDate) return;
                return dayjs(sampleTakenDate).format('DD/MM/YYYY');
            },
        },
        {
            dataKey: 'report_date',
            title: 'Date of report',
            key: 'report_date',
            width: 200,
            render: (_) => {
                if (!reportDate) return;
                return dayjs(reportDate).format('DD/MM/YYYY');
            },
        },
        {
            dataKey: 'name',
            title: 'Report results',
            key: 'name',
            render: (data) => {
                if (!data?.name) return;
                return <div className={reportResultStyle}>{data?.name}</div>;
            },
        },
        {
            dataKey: 'report',
            title: 'Report',
            key: 'report',
            render: (data) => {
                if (!data?.link) return;
                const parts = data?.link?.split('/');
                let fileName = '';
                if (parts?.length) {
                    fileName = parts[parts.length - 1];
                }
                return (
                    <div className={reportStyle}>
                        <span>
                            <FileIcon />
                        </span>
                        <div>{fileName}</div>
                    </div>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={reports}
            columns={columns}
            loading={loading}
            rowKey={(row) => row?.id}
            loadMoreProps={{
                canLoadMore: canLoadMore,
                onLoadMore: onLoadMore,
            }}
            rowColor="transparentBorder"
            margin="dense"
        />
    );
}
