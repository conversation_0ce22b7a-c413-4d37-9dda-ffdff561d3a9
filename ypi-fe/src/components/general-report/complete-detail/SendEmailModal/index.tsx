import { useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { FieldValues } from 'react-hook-form';
import Modal from '@/components/globals/Modal';
import InputForm from '@/components/globals/InputForm';
import { EFormInputType } from '@/utilities/types/form';
import {
    defectHeaderRightStyle,
    defectHeaderStyle,
    defectListStyle,
    formFieldRowStyle,
    headerRightItemStyle,
    infoStyle,
    jobSatisfiedStyle,
} from './style';
import TankInfo from './TankInfo';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { ITank } from '@/utilities/types/entities/tank';
import { IProjectLocation } from '@/utilities/types/entities/project-location';
import { ITask } from '@/utilities/types/entities/task';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { sendEmailSchema } from './validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSendEmail } from '@/hooks/general-report';
import CommonMessages from '@/utilities/messages/common';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import LabReportList from './LabReportList';
import DefectUncorrectedList from './DefectUncorrectedList';
import { ETaskStatus } from '@/utilities/types/enums/Task';
import { IStaff } from '@/utilities/types/entities/staff';
import { ILabReport } from '@/utilities/types/entities/lab-report';
import DefectCompletedList from './DefectCompletedList';
import CustomerJobSatisfied from './CustomerJobSatisfied';

interface SendEmailModalProps {
    isOpen: boolean;
    onClose: () => void;
    assignTaskId: string;
    tank?: ITank;
    location?: IProjectLocation;
    staffs?: IStaff[];
    tasks?: ITask[];
    completedDate?: string;
    sampleTakenDate?: string;
    reportDate?: string;
    customerSignature?: string;
    reports?: ILabReport[];
    defaultValues?: {
        to: string;
        subject: string;
    };
}

export function SendEmailModal({
    isOpen,
    onClose,
    tank,
    location,
    staffs,
    tasks,
    completedDate,
    assignTaskId,
    reports,
    sampleTakenDate,
    reportDate,
    customerSignature,
}: SendEmailModalProps) {
    const { trigger } = useSendEmail(EGeneralReport.COMPLETE_SEND_EMAIL);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const onSubmit = (data: FieldValues) => {
        if (!data?.subject || !data?.to?.length) return;
        setIsSubmitting(true);

        const body = {
            assignTaskId,
            toEmails: data?.to,
            subject: data?.subject,
        };

        toast.promise(trigger(body), {
            loading: CommonMessages.SendEmail,
            success: () => {
                setIsSubmitting(false);
                onClose();
                return CommonMessages.SendEmailSuccessful;
            },
            error: (err) => {
                setIsSubmitting(false);
                return err.message;
            },
        });
    };

    const taskData = useMemo(() => {
        if (!tasks || !tasks?.length) {
            return {
                defectCompleted: [],
                defectUncorrected: [],
            };
        }
        const defectCompleted = [];
        const defectUncorrected = [];

        for (const task of tasks) {
            if (task.status === ETaskStatus.UN_COMPLETED) {
                defectUncorrected.push(task);
                continue;
            }
            if (task.status === ETaskStatus.COMPLETED) {
                defectCompleted.push(task);
                continue;
            }
        }

        return {
            defectCompleted,
            defectUncorrected,
        };
    }, [tasks]);

    const staffNames = useMemo(() => {
        if (!staffs || !staffs?.length) return [];
        return staffs.map((item) => item?.fullName);
    }, [staffs]);

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={'Send email'}
            sizes={'lg'}
        >
            <InputForm
                defaultValues={{ to: [], subject: '' }}
                inputs={[
                    {
                        id: 'to',
                        name: 'to',
                        label: 'To',
                        type: EFormInputType.Tag,
                        labelPosition: 'left',
                        className: formFieldRowStyle,
                    },
                    {
                        id: 'subject',
                        name: 'subject',
                        label: 'Subject',
                        type: EFormInputType.Text,
                        variant: 'contained',
                        labelPosition: 'left',
                        className: formFieldRowStyle,
                    },
                ]}
                formInfo={
                    <>
                        <div className={infoStyle}>
                            <Typography
                                typography="header_20"
                                color="primary_100"
                            >
                                Tank information
                            </Typography>
                            <TankInfo tank={tank} location={location} />
                        </div>
                        <div className={defectListStyle}>
                            <div className={defectHeaderStyle}>
                                <div>
                                    <Typography
                                        typography="header_20"
                                        color="primary_100"
                                    >
                                        The Defect List has been completed
                                    </Typography>
                                </div>
                                <div className={defectHeaderRightStyle}>
                                    <div className={headerRightItemStyle}>
                                        <p>Staffs:</p>
                                        <div>{staffNames?.join(', ')}</div>
                                    </div>
                                    <div className={headerRightItemStyle}>
                                        <p>Date:</p>
                                        <div>
                                            {completedDate
                                                ? dayjs
                                                      .utc(completedDate)
                                                      .format('DD/MM/YYYY')
                                                : null}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <DefectCompletedList
                                tasks={taskData?.defectCompleted}
                            />
                        </div>
                        <div className={defectListStyle}>
                            <div className={defectHeaderStyle}>
                                <div>
                                    <Typography
                                        typography="header_20"
                                        color="primary_100"
                                    >
                                        List of uncorrected defects
                                    </Typography>
                                </div>
                                <div className={defectHeaderRightStyle}>
                                    <div className={headerRightItemStyle}>
                                        <p>Staffs:</p>
                                        <div>{staffNames?.join(', ')}</div>
                                    </div>
                                    <div className={headerRightItemStyle}>
                                        <p>Date:</p>
                                        <div>
                                            {completedDate
                                                ? dayjs
                                                      .utc(completedDate)
                                                      .format('DD/MM/YYYY')
                                                : null}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <DefectUncorrectedList
                                tasks={taskData?.defectUncorrected}
                            />
                        </div>
                        <div className={defectListStyle}>
                            <div className={defectHeaderStyle}>
                                <div>
                                    <Typography
                                        typography="header_20"
                                        color="primary_100"
                                    >
                                        Lab report
                                    </Typography>
                                </div>
                            </div>
                            <LabReportList
                                reports={reports}
                                completedDate={completedDate}
                                sampleTakenDate={sampleTakenDate}
                                reportDate={reportDate}
                            />
                        </div>
                        <div className={jobSatisfiedStyle}>
                            <div>
                                <Typography
                                    typography="header_20"
                                    color="primary_100"
                                >
                                    Customer job satisfied
                                </Typography>
                            </div>
                            <CustomerJobSatisfied
                                customerName={
                                    tank?.location?.customer?.name || ''
                                }
                                signatureImg={customerSignature}
                            />
                        </div>
                    </>
                }
                onSubmit={onSubmit}
                disabled={isSubmitting}
                resolver={zodResolver(sendEmailSchema)}
                submitText={'Send'}
                btnSubmitStyle={css({ w: '214px' })}
            />
        </Modal>
    );
}
