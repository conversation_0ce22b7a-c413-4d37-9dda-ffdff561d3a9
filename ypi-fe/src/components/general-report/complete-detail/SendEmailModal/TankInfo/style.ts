import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const infoColumnStyle = flex({
    flex: 1,
    w: '100%',
    gap: '12px',
    direction: 'column',
});

export const infoItemStyle = css({
    display: 'flex',
    gap: '12px',
    '& > p': {
        minWidth: '120px',
    },
});

export const dividerStyle = css({
    borderTop: 1,
    borderColor: 'border.field',
    borderStyle: 'solid',
});

export const expandedStyle = css({ flex: 1, w: '80px', gap: '12px' });
