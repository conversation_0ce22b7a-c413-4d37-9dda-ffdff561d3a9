import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import {
    dividerStyle,
    expandedStyle,
    infoColumnStyle,
    infoItemStyle,
} from './style';
import Typography from '@/components/globals/Typography';
import { InfoItem } from '@/components/manage-customers/InfoItem';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { ITank } from '@/utilities/types/entities/tank';
import { IProjectLocation } from '@/utilities/types/entities/project-location';
import {
    getTankMaterialConfig,
    getTankShapeConfig,
    getTankTypeConfig,
} from '@/utilities/helpers/tank';

interface TankInfoProps {
    tank?: ITank;
    location?: IProjectLocation;
}

export default function TankInfo({ tank, location }: TankInfoProps) {
    const tankTypeInfo = tank ? getTankTypeConfig(tank.type) : undefined;
    const meterialText = tank?.material?.name || '';
    const tankMaterialInfo = meterialText
        ? getTankMaterialConfig(meterialText)
        : undefined;
    const tankShapeInfo = tank ? getTankShapeConfig(tank?.shape) : undefined;

    return (
        <>
            <InfoItem
                label="Postal code"
                value={location?.postalCode}
                valueStyle={css({ mt: 0 })}
                className={infoItemStyle}
            />
            <InfoItem
                label="Address"
                value={`${location?.street || ''} ${location?.blockNo || ''} ${
                    location?.building || ''
                }`}
                valueStyle={css({ mt: 0 })}
                className={infoItemStyle}
            />
            <div className={dividerStyle} />
            <div className={flex({ flex: 1 })}>
                <div className={infoColumnStyle}>
                    <InfoItem
                        className={infoItemStyle}
                        label="Tank type"
                        value={tankTypeInfo?.shortLabel ?? tank?.type}
                        valueColor={tankTypeInfo?.color}
                        valueStyle={css({ mt: 0 })}
                    />
                    <InfoItem
                        className={infoItemStyle}
                        label="Material"
                        value={tankMaterialInfo?.label ?? meterialText}
                        valueStyle={css({ mt: 0 })}
                    />
                    <InfoItem
                        className={infoItemStyle}
                        label="Shape"
                        value={tankShapeInfo?.label ?? tank?.shape}
                        valueStyle={css({ mt: 0 })}
                    />
                </div>
                <div className={infoColumnStyle}>
                    <InfoItem
                        label="Tank dimensions"
                        valueStyle={css({ mt: 0 })}
                        className={css({
                            display: 'flex',
                            gap: '12px',
                        })}
                        renderValue={() => (
                            <Typography
                                tag={ETypographyTag.div}
                                typography="subtitle_15"
                                color="secondary_100"
                                className={css({
                                    display: 'flex',
                                })}
                            >
                                <div className={expandedStyle}>
                                    L: {tank?.length}
                                </div>
                                <div className={expandedStyle}>
                                    W: {tank?.width}
                                </div>
                                <div className={expandedStyle}>
                                    H: {tank?.height}
                                </div>
                            </Typography>
                        )}
                    />
                    <InfoItem
                        className={infoItemStyle}
                        label="EC"
                        value={tank?.effectiveCap}
                        valueStyle={css({ mt: 0 })}
                    />
                    <InfoItem
                        className={infoItemStyle}
                        label="Floor level"
                        value={tank?.floorLevel}
                        valueStyle={css({ mt: 0 })}
                    />
                </div>
            </div>
        </>
    );
}
