import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const wrapperFilter = flex({
    gap: 4,
    alignItems: 'center',
});

export const buttonStyles = css({
    fontWeight: 'bold',
    color: 'primary.100',
});

export const btnDateStyles = css({
    borderWidth: '1.5px !important',
    w: '154px !important',

    '& > p': {
        fontWeight: 'semiBold !important',
    },
});

export const datePickerStyle = css({
    '& .react-datepicker': {
        marginTop: '-5px !important',
        border: 'none !important',
        backgroundColor: 'background.40 !important',
        boxShadow: '0px 4px 6px 0px #3A3A3A26 !important',
        borderRadius: '12px !important',
        padding: '12px !important',
        fontFamily: 'inherit !important',
        fontSize: 'subtitle.15 !important',
        fontWeight: 'regular !important',

        '& .react-datepicker__header': {
            fontWeight: 'semiBold !important',
            border: 'none !important',
            padding: '0 !important',
            marginBottom: '10px !important',
            backgroundColor: 'unset !important',
        },

        '& .react-datepicker__month': {
            margin: '0 !important',
            height: '174px',
            overflow: 'auto',

            '& .react-datepicker__month-wrapper': {
                '& .react-datepicker__month-text': {
                    margin: '0 0 8px !important',
                },
            },
        },

        '& .react-datepicker__month-text--selected': {
            backgroundColor: 'unset !important',
            color: 'primary.100 !important',
            fontWeight: 'semiBold !important',
        },

        '& .react-datepicker__month-text--today': {
            fontWeight: 'semiBold !important',
        },

        '& .react-datepicker__navigation': {
            height: '25px !important',
            width: '25px !important',
            top: '8px !important',

            '& .react-datepicker__navigation-icon': {
                top: '2px !important',

                '&::before': {
                    borderWidth: '1.5px 1.5px 0 0 !important',
                    borderColor: 'black !important',
                },
            },
        },
    },

    '& .react-datepicker__triangle': {
        display: 'none !important',
    },
});
