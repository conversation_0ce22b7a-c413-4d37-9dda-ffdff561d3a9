import { btnDateStyles, datePickerStyle, wrapperFilter } from './styles';
import SearchInput from '@/components/globals/SearchInput';
import { debounce } from 'lodash';
import { DatePickerBase } from '@/components/globals/DatePickerBase';
import { useState } from 'react';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import Button from '@/components/globals/Button';
import { css } from '@/styled-system/css';
import Typography from '@/components/globals/Typography';
import ChevronDown from '@/components/globals/Icons/ChevronDown';

export interface FilterElementsProps {
    onSearch: (keyword: string) => void;
    onFilterDate: (date: Date | null) => void;
    defaultFilter?: {
        date: Date | null;
    };
}

export default function FilterElements({
    onSearch,
    onFilterDate,
    defaultFilter,
}: FilterElementsProps) {
    const [date, setDate] = useState<Date | null>(defaultFilter?.date || null);

    return (
        <div className={wrapperFilter}>
            <SearchInput
                onChange={debounce((s) => {
                    onSearch?.(s);
                }, 500)}
            />
            <div className={datePickerStyle}>
                <DatePickerBase
                    dropdownMode="select"
                    name="date"
                    selected={date}
                    onChange={(date) => {
                        onFilterDate(date);
                        setDate(date);
                    }}
                    customInput={
                        <Button
                            className={btnDateStyles}
                            visual="outline_primary"
                        >
                            <Typography
                                color="primary_100"
                                typography="header_15"
                            >
                                {date
                                    ? dayjs(date).format('MMM, YYYY')
                                    : 'MMM, YYYY'}
                            </Typography>
                            <ChevronDown
                                className={css({
                                    '&>path': {
                                        stroke: 'primary.100',
                                    },
                                })}
                                width={16}
                                height={16}
                            />
                        </Button>
                    }
                    dateFormat="MMM, yyyy"
                    showMonthYearPicker
                    suffixIcon={false}
                />
            </div>
        </div>
    );
}
