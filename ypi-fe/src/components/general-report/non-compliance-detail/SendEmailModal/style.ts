import { css } from '@/styled-system/css';

export const formFieldRowStyle = css({
    '& .input-label': {
        width: 100,
    },
});

export const infoStyle = css({
    marginTop: '48px',
    gap: '12px',
    display: 'flex',
    flexDirection: 'column',
});

export const defectListStyle = css({
    marginTop: '48px',
    gap: '12px',
    display: 'flex',
    flexDirection: 'column',
});

export const defectHeaderStyle = css({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
});

export const defectHeaderRightStyle = css({
    display: 'flex',
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
});

export const headerRightItemStyle = css({
    display: 'flex',
    flexDirection: 'row',
    gap: '12px',
    alignItems: 'center',
    fontSize: 'body.15',
    '& > p': {
        fontWeight: 'semiBold',
    },
});
