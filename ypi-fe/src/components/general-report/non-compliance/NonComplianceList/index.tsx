import Table, { IColProps } from '@/components/globals/Table';

import { ASSIGN_TASK_STATUS_LABELS } from '@/constant/task';
import ChevronRight from '@/components/globals/Icons/ChevronRight';
import { EAssignTaskStatus, ETaskStatus } from '@/utilities/types/enums/Task';
import { FORMAT_DATE } from '@/configs/date';
import { INonCompliance } from '@/utilities/types/entities/general-report';
import { MailIcon } from '@/components/globals/Icons';
import { Slug } from '@/utilities/types/enums/Slug';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import dayjs from 'dayjs';
import { getFullAddressTank } from '@/utilities/helpers/tank';
import { useRouter } from 'next/navigation';
import {
    actionStyle,
    addressStyle,
    assignStaffStyle,
    btnSendEmailStyle,
} from './style';
import { HeadingColors } from '@/components/globals/Typography/style';

interface INonComplianceListProps {
    data: INonCompliance[];
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    handleSendEmail?: (taskId: string, status: ETaskStatus) => void;
}

export default function NonComplianceList(props: INonComplianceListProps) {
    const router = useRouter();
    const columns: IColProps<INonCompliance>[] = [
        {
            dataKey: 'startAt',
            title: 'Date',
            key: 'startAt',
            align: 'left',
            render: (data) =>
                dayjs(data.startAt).format(FORMAT_DATE.DATE_SHORT),
        },
        {
            dataKey: 'code',
            title: 'Job ID',
            key: 'code',
            align: 'left',
            className: css({
                whiteSpace: 'nowrap',
            }),
        },
        {
            dataKey: 'staffs',
            title: 'Assigned staff',
            key: 'staffs',
            align: 'left',
            width: '160px',
            render: (data) => {
                return (
                    <div className={assignStaffStyle}>
                        {data?.staffs
                            ?.map((item) => item?.fullName)
                            ?.join(', ')}
                    </div>
                );
            },
        },
        {
            dataKey: 'fullAddress',
            title: 'Address',
            key: 'fullAddress',
            align: 'left',
            render: (data) => (
                <div
                    className={css({
                        width: '300px',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                    })}
                >
                    {data?.tank?.location
                        ? getFullAddressTank(data?.tank?.location)
                        : ''}
                </div>
            ),
        },
        {
            dataKey: 'status',
            title: 'Status',
            key: 'status',
            align: 'left',
            className: css({
                textTransform: 'capitalize',
            }),
            render: (row) => (
                <Typography
                    color={
                        ASSIGN_TASK_STATUS_LABELS[
                            row?.status as EAssignTaskStatus
                        ]?.color as HeadingColors
                    }
                    typography={
                        row?.status === EAssignTaskStatus['COMPLETED']
                            ? 'header_14'
                            : 'body_14'
                    }
                >
                    {
                        ASSIGN_TASK_STATUS_LABELS[
                            row?.status as EAssignTaskStatus
                        ]?.label
                    }
                </Typography>
            ),
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: '100px',
            render: (row) => {
                return (
                    <div className={actionStyle}>
                        <MailIcon
                            onClick={(e) => {
                                e.stopPropagation();
                                props.handleSendEmail?.(
                                    row?.id,
                                    row?.status as ETaskStatus
                                );
                            }}
                            className={btnSendEmailStyle}
                        />
                        <ChevronRight />
                    </div>
                );
            },
        },
    ];

    const handleClick = (rowData: any) => {
        router.push(`${Slug.GR_NON_COMPLIANCE}/${rowData.id}`);
    };

    return (
        <Table
            dataSource={props.data}
            columns={columns}
            loading={props.loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
