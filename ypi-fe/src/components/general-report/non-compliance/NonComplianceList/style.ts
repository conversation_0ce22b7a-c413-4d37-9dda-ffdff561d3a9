import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';

export const assignStaffStyle = css({
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: '160px',
});

export const actionStyle = flex({ gap: 2, justify: 'space-between' });

export const addressStyle = css({ whiteSpace: 'break-spaces' });

export const btnSendEmailStyle = css({ cursor: 'pointer' });