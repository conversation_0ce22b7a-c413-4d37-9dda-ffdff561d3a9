import {
    containerStyle,
    dividerStyle,
    expandedStyle,
    headingTitle,
    projectInfoItem,
    projectInfoItemLeft,
    projectInfoStyle,
    tankInfoRightStyle,
    wrapperStyle,
} from './style';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { ITank } from '@/utilities/types/entities/tank';
import {
    getTankMaterialConfig,
    getTankShapeConfig,
    getTankTypeConfig,
} from '@/utilities/helpers/tank';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { InfoItem } from '@/components/manage-customers/InfoItem';
import { IProjectLocation } from '@/utilities/types/entities/project-location';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { IStaff } from '@/utilities/types/entities/staff';

type Props = {
    tank?: ITank;
    location?: IProjectLocation;
    preInspectionDate: string;
    staffs: IStaff[];
};

export default function PreInspectionHeader({
    tank,
    location,
    preInspectionDate,
    staffs,
}: Props) {
    const tankTypeInfo = tank ? getTankTypeConfig(tank.type) : undefined;
    const meterialText =
        typeof tank?.material === 'string'
            ? tank?.material
            : tank?.material?.name;
    const tankMaterialInfo = tank
        ? getTankMaterialConfig(meterialText || '')
        : undefined;
    const tankShapeInfo = tank ? getTankShapeConfig(tank.shape) : undefined;

    const nameStaff = staffs[0]?.fullName;
    return (
        <div className={wrapperStyle}>
            <div className={containerStyle}>
                <div>
                    <Typography
                        typography="header_16"
                        color="primary_100"
                        className={headingTitle}
                    >
                        Project location information
                    </Typography>
                </div>
                <div className={projectInfoStyle}>
                    <div className={flex({ gap: 6, flexDirection: 'row' })}>
                        <InfoItem
                            className={css(
                                projectInfoItem,
                                projectInfoItemLeft
                            )}
                            label="Postal code"
                            value={location?.postalCode}
                            valueStyle={css({ mt: 0 })}
                        />
                        <InfoItem
                            className={css(projectInfoItem)}
                            label="Address"
                            value={`${location?.street || ''} ${
                                location?.blockNo || ''
                            } ${location?.building || ''}`}
                            valueStyle={css({ mt: 0 })}
                        />
                    </div>
                    <div className={flex({ gap: 6, flexDirection: 'row' })}>
                        <InfoItem
                            className={css(
                                projectInfoItem,
                                projectInfoItemLeft
                            )}
                            label="Pre-inspection"
                            value={dayjs(preInspectionDate).format(
                                'DD/MM/YYYY'
                            )}
                            valueStyle={css({ mt: 0 })}
                        />
                        <InfoItem
                            className={css(projectInfoItem)}
                            label="Staff"
                            value={nameStaff}
                            valueStyle={css({ mt: 0 })}
                        />
                    </div>
                </div>
            </div>
            <div className={dividerStyle} />
            <div className={containerStyle}>
                <div>
                    <Typography
                        typography="header_16"
                        color="primary_100"
                        className={headingTitle}
                    >
                        Tank information
                    </Typography>
                </div>
                <div className={tankInfoRightStyle}>
                    <InfoItem
                        className={css({ w: '14%', whiteSpace: 'nowrap' })}
                        label="Tank type"
                        value={tankTypeInfo?.shortLabel ?? tank?.type}
                        valueColor={tankTypeInfo?.color}
                    />
                    <InfoItem
                        className={css({ w: '14%', whiteSpace: 'nowrap' })}
                        label="Material"
                        value={tankMaterialInfo?.label ?? meterialText}
                    />
                    <InfoItem
                        className={css({ w: '14%', whiteSpace: 'nowrap' })}
                        label="Shape"
                        value={tankShapeInfo?.label ?? tank?.shape}
                    />
                    <InfoItem
                        className={css({ w: '30%' })}
                        label="Tank dimensions"
                        renderValue={() => (
                            <Typography
                                className={flex({ gap: '6px' })}
                                tag={ETypographyTag.div}
                                typography="subtitle_15"
                                color="secondary_100"
                            >
                                <div className={expandedStyle}>
                                    L: {tank?.length}
                                </div>
                                <div className={expandedStyle}>
                                    W: {tank?.width}
                                </div>
                                <div className={expandedStyle}>
                                    H: {tank?.height}
                                </div>
                            </Typography>
                        )}
                    />
                    <InfoItem
                        className={css({ w: '14%' })}
                        label="EC"
                        value={tank?.effectiveCap}
                    />
                    <InfoItem
                        className={css({ w: '14%' })}
                        label="Floor level"
                        value={tank?.floorLevel}
                    />
                </div>
            </div>
        </div>
    );
}
