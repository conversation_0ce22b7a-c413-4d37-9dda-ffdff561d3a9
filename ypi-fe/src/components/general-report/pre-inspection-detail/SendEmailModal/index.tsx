import { useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { FieldValues } from 'react-hook-form';
import Modal from '@/components/globals/Modal';
import InputForm from '@/components/globals/InputForm';
import { EFormInputType } from '@/utilities/types/form';
import {
    defectHeaderRightStyle,
    defectHeaderStyle,
    defectListStyle,
    formFieldRowStyle,
    headerRightItemStyle,
    infoStyle,
} from './style';
import TankInfo from './TankInfo';
import Typography from '@/components/globals/Typography';
import DefectList from './DefectList';
import { css } from '@/styled-system/css';
import { ITank } from '@/utilities/types/entities/tank';
import { IProjectLocation } from '@/utilities/types/entities/project-location';
import { ITask } from '@/utilities/types/entities/task';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { sendEmailSchema } from './validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSendEmail } from '@/hooks/general-report';
import CommonMessages from '@/utilities/messages/common';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import { IStaff } from '@/utilities/types/entities/staff';
import { EAssignTaskStatus, ETaskStatus } from '@/utilities/types/enums/Task';

interface SendEmailModalProps {
    isOpen: boolean;
    onClose: () => void;
    assignTaskId: string;
    tank?: ITank;
    location?: IProjectLocation;
    staffs?: IStaff[];
    tasks?: ITask[];
    preInspectionDate?: string;
    status: EAssignTaskStatus;
    defaultValues?: {
        to: string;
        subject: string;
    };
}

export function SendEmailModal({
    isOpen,
    onClose,
    tank,
    location,
    staffs,
    tasks,
    preInspectionDate,
    defaultValues,
    assignTaskId,
    status,
}: SendEmailModalProps) {
    const { trigger } = useSendEmail(EGeneralReport.PRE_INSPECTION_SEND_EMAIL);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const onSubmit = (data: FieldValues) => {
        if (!data?.subject || !data?.to?.length) return;
        setIsSubmitting(true);

        const body = {
            assignTaskId,
            toEmails: data?.to,
            subject: data?.subject,
        };

        toast.promise(trigger(body), {
            loading: CommonMessages.SendEmail,
            success: () => {
                setIsSubmitting(false);
                onClose();
                return CommonMessages.SendEmailSuccessful;
            },
            error: (err) => {
                setIsSubmitting(false);
                return err.message;
            },
        });
    };

    const staffNames = useMemo(() => {
        if (!staffs || !staffs?.length) return [];
        return staffs.map((item) => item?.fullName);
    }, [staffs]);

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={'Send email'}
            sizes="lg"
        >
            <InputForm
                defaultValues={{ to: [], subject: '' }}
                inputs={[
                    {
                        id: 'to',
                        name: 'to',
                        label: 'To',
                        type: EFormInputType.Tag,
                        labelPosition: 'left',
                        className: formFieldRowStyle,
                    },
                    {
                        id: 'subject',
                        name: 'subject',
                        label: 'Subject',
                        type: EFormInputType.Text,
                        variant: 'contained',
                        labelPosition: 'left',
                        className: formFieldRowStyle,
                    },
                ]}
                formInfo={
                    <>
                        <div className={infoStyle}>
                            <Typography
                                typography="header_20"
                                color="primary_100"
                            >
                                Tank information
                            </Typography>
                            <TankInfo tank={tank} location={location} />
                        </div>
                        <div className={defectListStyle}>
                            {status === EAssignTaskStatus.PRE_INSPECTION ||
                            status === EAssignTaskStatus.PREPARE ? (
                                <>
                                    <div className={defectHeaderStyle}>
                                        <div>
                                            <Typography
                                                typography="header_20"
                                                color="primary_100"
                                            >
                                                Pre-Inspection List
                                            </Typography>
                                        </div>
                                        <div className={defectHeaderRightStyle}>
                                            <div
                                                className={headerRightItemStyle}
                                            >
                                                <p>Staffs:</p>
                                                <div>
                                                    {staffNames?.join(', ')}
                                                </div>
                                            </div>
                                            <div
                                                className={headerRightItemStyle}
                                            >
                                                <p>Date:</p>
                                                <div>
                                                    {dayjs
                                                        .utc(preInspectionDate)
                                                        .format('DD/MM/YYYY')}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <DefectList tasks={tasks} />
                                </>
                            ) : (
                                <>
                                    <div className={defectHeaderStyle}>
                                        <div>
                                            <Typography
                                                typography="header_20"
                                                color="primary_100"
                                            >
                                                The rectified Defects
                                            </Typography>
                                        </div>
                                        <div className={defectHeaderRightStyle}>
                                            <div
                                                className={headerRightItemStyle}
                                            >
                                                <p>Staffs:</p>
                                                <div>
                                                    {staffNames?.join(', ')}
                                                </div>
                                            </div>
                                            <div
                                                className={headerRightItemStyle}
                                            >
                                                <p>Date:</p>
                                                <div>
                                                    {dayjs
                                                        .utc(preInspectionDate)
                                                        .format('DD/MM/YYYY')}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <DefectList
                                        tasks={tasks?.filter(
                                            (task) =>
                                                task?.status ===
                                                ETaskStatus.COMPLETED
                                        )}
                                    />
                                    <div>
                                        <Typography
                                            typography="header_20"
                                            color="primary_100"
                                        >
                                            The unresolve Defects
                                        </Typography>
                                    </div>
                                    <DefectList
                                        tasks={tasks?.filter(
                                            (task) =>
                                                task?.status ===
                                                ETaskStatus.UN_COMPLETED
                                        )}
                                    />
                                </>
                            )}
                        </div>
                    </>
                }
                onSubmit={onSubmit}
                disabled={isSubmitting}
                resolver={zodResolver(sendEmailSchema)}
                submitText={'Send'}
                btnSubmitStyle={css({ w: '214px' })}
            />
        </Modal>
    );
}
