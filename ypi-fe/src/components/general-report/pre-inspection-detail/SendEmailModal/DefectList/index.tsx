import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import Table, { IColProps } from '@/components/globals/Table';
import ImageGrid from '@/components/globals/ImageGrid';
import { ITask } from '@/utilities/types/entities/task';

interface DefectListProps {
    tasks?: ITask[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function DefectList({
    tasks = [],
    loading,
    canLoadMore,
    onLoadMore,
}: DefectListProps) {
    const columns: IColProps<ITask>[] = [
        {
            dataKey: 'index',
            title: '',
            key: 'index',
            render: (_, rowIndex) => {
                const index = rowIndex + 1;
                if (index < 10) return `0${index}`;
                return index;
            },
            width: 60,
        },
        {
            dataKey: 'title',
            title: 'Defects',
            key: 'title',
            align: 'left',
            render: (data) => {
                return (
                    <>
                        <Typography
                            color="body_1"
                            typography="body_15"
                            tag={ETypographyTag.h1}
                            className={css({
                                whiteSpace: 'pre-wrap',
                            })}
                        >
                            {data?.title}
                        </Typography>
                        <ImageGrid
                            maxItem={4}
                            photos={
                                data?.images?.map((image) => ({
                                    id: image?.id || '',
                                    url: image?.link || '',
                                    key: image?.name || '',
                                    link: image?.link || '',
                                })) || []
                            }
                            visual="inline"
                        />
                    </>
                );
            },
        },
        {
            dataKey: 'qty',
            title: 'Qty',
            key: 'qty',
            width: 60,
        },
        {
            dataKey: 'size',
            title: 'Size',
            key: 'size',
            width: 100,
        },
        {
            dataKey: 'remark',
            title: 'Remark',
            key: 'remark',
            align: 'left',
            width: 400,
            render: (data) => {
                return (
                    <Typography
                        color="body_1"
                        typography="body_15"
                        tag={ETypographyTag.h1}
                        className={css({
                            whiteSpace: 'pre-wrap',
                        })}
                    >
                        {data?.staffRemark}
                    </Typography>
                );
            },
        },
    ];

    return (
        <Table
            dataSource={tasks}
            columns={columns}
            loading={loading}
            rowKey={(row) => row?.id}
            loadMoreProps={{
                canLoadMore: canLoadMore,
                onLoadMore: onLoadMore,
            }}
            rowColor="transparentBorder"
            margin="dense"
        />
    );
}
