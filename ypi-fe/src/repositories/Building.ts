import request from '@/utilities/request/server';
import { IBuilding } from '@/utilities/types/entities/building';
import { EBuildingPath } from '@/utilities/types/enums/Building';
import { IResponseData } from '@/utilities/types/request';

function getList() {
    return request<IResponseData<IBuilding[]>>({
        url: EBuildingPath.ALLOW,
        method: 'GET',
    });
}

const BuildingRepository = Object.freeze({
    getList,
});

export default BuildingRepository;
