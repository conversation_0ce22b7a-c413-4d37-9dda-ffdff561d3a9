import request from '@/utilities/request/server';
import { Slug } from '../utilities/types/enums/Slug';
import { IUser } from '@/utilities/types/entities/user';
import { IResponseData } from '@/utilities/types/request';
import { EAuthPath } from '@/utilities/types/enums/Auth';

const forgotPasswordReturnURL = `${process.env.APP_HOSTNAME}${Slug.RESET_PASSWORD}`;

function signIn(email: string, password: string) {
    return request<
        IResponseData<{
            accessToken: string;
            refreshToken: string;
            accessTokenExp: string;
            user: IUser;
        }>
    >({
        method: 'POST',
        url: EAuthPath.SIGN_IN,
        data: { email, password },
    });
}

function forgotPassword(email: string) {
    return request<IResponseData<boolean>>({
        method: 'POST',
        url: EAuthPath.FORGOT_PASSWORD,
        data: {
            email,
            callbackURL: forgotPasswordReturnURL,
        },
    });
}

function verifyForgotPasswordToken(token: string) {
    return request<IResponseData<boolean>>({
        method: 'GET',
        url: EAuthPath.FORGOT_PASSWORD_VERIFY_TOKEN,
        params: {
            token,
        },
    });
}

function newPassword(token: string, password: string) {
    return request<IResponseData<boolean>>({
        method: 'POST',
        url: EAuthPath.NEW_PASSWORD,
        data: {
            token,
            password,
        },
    });
}
function getProfileMe(token: string) {
    return request<IResponseData<IUser>>({
        method: 'GET',
        url: EAuthPath.BE_PROFILE,
    });
}

function updateProfileMe(data: Partial<IUser>) {
    return request<IResponseData<IUser>>({
        url: EAuthPath.BE_PROFILE,
        method: 'PUT',
        data: data,
    });
}

const AuthRepository = Object.freeze({
    signIn,
    forgotPassword,
    verifyForgotPasswordToken,
    newPassword,
    getProfileMe,
    updateProfileMe,
});

export default AuthRepository;
