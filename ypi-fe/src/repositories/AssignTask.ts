import { IResponseData } from '@/utilities/types/request';
import request from '@/utilities/request/server';
import {
    IAssignTaskStaff,
    ITaskConfirm,
} from '@/utilities/types/entities/assign-task';
import { EAssignTask } from '@/utilities/types/enums/AssignTask';

function assignStaff(data: Partial<IAssignTaskStaff>) {
    return request<IResponseData<any>>({
        url: EAssignTask.ASSIGN_STAFF,
        method: 'POST',
        data: data,
    });
}

function updateAssignStaff(id: string, data: Partial<IAssignTaskStaff>) {
    return request<IResponseData<any>>({
        url: `${EAssignTask.ASSIGN_STAFF}/${id}`,
        method: 'PUT',
        data: data,
    });
}

function updateTakenSampleDate(id: string, data: { datetime: string }) {
    return request<IResponseData<any>>({
        url: `${EAssignTask.PATH}/complete/${id}/lab-sample-taken`,
        method: 'PUT',
        data: data,
    });
}

function taskConfirm(data: Partial<ITaskConfirm>) {
    return request<IResponseData<ITaskConfirm>>({
        url: EAssignTask.TASK_CONFIRM,
        method: 'POST',
        data: data,
    });
}

function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${EAssignTask.PATH}/${id}`,
        method: 'DELETE',
    });
}

const AssignTaskRepositories = Object.freeze({
    assignStaff,
    taskConfirm,
    updateAssignStaff,
    deleteById,
    updateTakenSampleDate,
});
export default AssignTaskRepositories;
