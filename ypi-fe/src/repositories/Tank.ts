import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import {
    IAssignTank,
    INewTankParams,
    ITank,
} from '@/utilities/types/entities/tank';
import { ETankPath } from '@/utilities/types/enums/Tank';
import { EAssignTask } from '@/utilities/types/enums/AssignTask';
import { IAssignTask } from '@/utilities/types/entities/task';

function getList(locationId: string, query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<ITank>>>({
        url: `${ETankPath.LOCATION_TANK}/${locationId}`,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword ?? '',
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function create(locationId: string, data: Partial<INewTankParams>) {
    // TODO: rename
    return request<IResponseData<ITank>>({
        url: `${ETankPath.LOCATION_TANK}/${locationId}`,
        method: 'POST',
        data: data,
    });
}

function createNewTank(data: Partial<ITank>) {
    return request<IResponseData<ITank>>({
        url: `${ETankPath.PATH}`,
        method: 'POST',
        data: data,
    });
}

function update(id: string, data: Partial<INewTankParams>) {
    return request<IResponseData<ITank>>({
        url: `${ETankPath.PATH}/${id}`,
        method: 'PUT',
        data: data,
    });
}

function getById(id: string, assignTaskId?: string) {
    let path = `${ETankPath.PATH}/${id}`;
    if (assignTaskId) path = `${path}?assignTaskId=${assignTaskId}`;
    return request<IResponseData<ITank>>({
        url: path,
        method: 'GET',
    });
}

function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${ETankPath.PATH}/${id}`,
        method: 'DELETE',
    });
}

function createAssignTank(data: Partial<IAssignTank>) {
    return request<IResponseData<ITank>>({
        url: EAssignTask.PATH,
        method: 'POST',
        data: data,
    });
}

function updateAssignTank(id: string, data: Partial<IAssignTank>) {
    return request<IResponseData<ITank>>({
        url: `${EAssignTask.PATH}/${id}`,
        method: 'PUT',
        data: data,
    });
}

function getCleanDisinfection(id: string, query: Partial<IPaginationParams>) {
    const path = `${ETankPath.PATH}/${id}/clean-disinfection`;
    return request<IResponseData<ITank>>({
        url: path,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword ?? '',
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function getHistory(id: string, query: Partial<IPaginationParams>) {
    const path = `${ETankPath.PATH}/${id}/clean-disinfection/history`;
    return request<IResponseData<IAssignTask>>({
        url: path,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword ?? '',
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function getRemarkInspector(id: string, query: Partial<IPaginationParams>) {
    const path = `${ETankPath.PATH}/${id}/remark-inspector`;
    return request<IResponseData<ITank>>({
        url: path,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword ?? '',
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function activate(id: string, data: { isActive: boolean }) {
    return request<IResponseData<ITank>>({
        url: `${ETankPath.PATH}/active/${id}`,
        method: 'PUT',
        data: data,
    });
}

const TankRepository = Object.freeze({
    getList,
    create,
    getById,
    update,
    deleteById,
    createAssignTank,
    createNewTank,
    updateAssignTank,
    getCleanDisinfection,
    getRemarkInspector,
    getHistory,
    activate,
});

export default TankRepository;
