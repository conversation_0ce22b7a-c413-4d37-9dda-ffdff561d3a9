import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import { ELocationPath } from '@/utilities/types/enums/Location';
import {
    INewProjectLocationParams,
    IProjectLocation,
} from '@/utilities/types/entities/project-location';

function getList(customerId: string, query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IProjectLocation>>>({
        url: `${ELocationPath.CUSTOMER_LOCATION}/${customerId}`,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function create(customerId: string, data: Partial<INewProjectLocationParams>) {
    return request<IResponseData<IProjectLocation>>({
        url: `${ELocationPath.PATH}`,
        method: 'POST',
        data: { ...data, customerId },
    });
}

function update(id: string, data: Partial<INewProjectLocationParams>) {
    return request<IResponseData<IProjectLocation>>({
        url: `${ELocationPath.PATH}/${id}`,
        method: 'PUT',
        data: data,
    });
}

function getById(id: string) {
    return request<IResponseData<IProjectLocation>>({
        url: `${ELocationPath.PATH}/${id}`,
        method: 'GET',
    });
}

function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${ELocationPath.PATH}/${id}`,
        method: 'DELETE',
    });
}

const LocationRepository = Object.freeze({
    getList,
    create,
    getById,
    update,
    deleteById,
});

export default LocationRepository;
