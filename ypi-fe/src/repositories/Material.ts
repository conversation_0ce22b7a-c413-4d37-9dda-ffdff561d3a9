import request from '@/utilities/request/server';
import { IMaterial } from '@/utilities/types/entities/material';
import { EMaterial } from '@/utilities/types/enums/Material';
import { IResponseData } from '@/utilities/types/request';

function getList() {
    return request<IResponseData<IMaterial[]>>({
        url: `${EMaterial.MATERIAL_ALLOW}`,
        method: 'GET',
    });
}

const MaterialRepository = Object.freeze({
    getList,
});

export default MaterialRepository;
