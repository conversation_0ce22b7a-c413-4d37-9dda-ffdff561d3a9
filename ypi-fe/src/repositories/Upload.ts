import { IFileUpload } from '@/utilities/types/entities/upload';
import { EUploadPath } from '@/utilities/types/enums/Upload';
import { IResponseData } from '@/utilities/types/request';
import request from '@/utilities/request/server';
import { EFilePath } from '@/utilities/types/enums/File';

function uploadFiles(formData: FormData) {
    return request<IResponseData<IFileUpload[]>>({
        url: EUploadPath.PATH,
        method: 'POST',
        data: formData,
    });
}

function uploadImages(formData: FormData) {
    return request<IResponseData<IFileUpload[]>>({
        url: EUploadPath.IMAGE_PATH,
        method: 'POST',
        data: formData,
    });
}
function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${EFilePath.PATH}/${id}`,
        method: 'DELETE',
    });
}
const UploadRepository = Object.freeze({
    uploadFiles,
    deleteById,
    uploadImages,
});
export default UploadRepository;
