import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import {
    IComplete,
    ICompleteDetail,
    INonCompliance,
    INonComplianceDetail,
    IPreInspection,
    IPreInspectionDetail,
    ISendEmail,
} from '@/utilities/types/entities/general-report';

function getList(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IPreInspection>>>({
        url: EGeneralReport.PRE_INSPECTION,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function getById(id: string) {
    return request<IResponseData<IPreInspectionDetail>>({
        url: `${EGeneralReport.PRE_INSPECTION}/${id}`,
        method: 'GET',
    });
}

function getListNonCompliance(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<INonCompliance>>>({
        url: EGeneralReport.NON_COMPLIANCE,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function getNonComplianceById(id: string) {
    return request<IResponseData<INonComplianceDetail>>({
        url: `${EGeneralReport.NON_COMPLIANCE}/${id}`,
        method: 'GET',
    });
}

function getListComplete(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IComplete>>>({
        url: EGeneralReport.COMPLETE,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function getListCompleteById(id: string) {
    return request<IResponseData<ICompleteDetail>>({
        url: `${EGeneralReport.COMPLETE}/${id}`,
        method: 'GET',
    });
}

function preInspectionSendEmail(data: Partial<ISendEmail>) {
    return request<IResponseData<ISendEmail>>({
        url: EGeneralReport.PRE_INSPECTION_SEND_EMAIL,
        method: 'POST',
        data: data,
    });
}

function nonComplianceSendEmail(data: Partial<ISendEmail>) {
    return request<IResponseData<ISendEmail>>({
        url: EGeneralReport.NON_COMPLIANCE_SEND_EMAIL,
        method: 'POST',
        data: data,
    });
}

function completeSendEmail(data: Partial<ISendEmail>) {
    return request<IResponseData<ISendEmail>>({
        url: EGeneralReport.COMPLETE_SEND_EMAIL,
        method: 'POST',
        data: data,
    });
}

function preInspectionDownloadEmailContent(data: Partial<ISendEmail>) {
    return request<IResponseData<string>>({
        url: EGeneralReport.PRE_INSPECTION_DOWNLOAD_EMAIL,
        method: 'POST',
        data: data,
    });
}

function nonComplianceDownloadEmailContent(data: Partial<ISendEmail>) {
    return request<IResponseData<string>>({
        url: EGeneralReport.NON_COMPLIANCE_DOWNLOAD_EMAIL,
        method: 'POST',
        data: data,
    });
}

function completeSendDownloadEmailContent(data: Partial<ISendEmail>) {
    return request<IResponseData<string>>({
        url: EGeneralReport.COMPLETE_DOWNLOAD_EMAIL,
        method: 'POST',
        data: data,
    });
}

const GeneralReportRepository = Object.freeze({
    getList,
    getById,
    getListNonCompliance,
    getNonComplianceById,
    getListComplete,
    getListCompleteById,
    preInspectionSendEmail,
    nonComplianceSendEmail,
    completeSendEmail,
    preInspectionDownloadEmailContent,
    nonComplianceDownloadEmailContent,
    completeSendDownloadEmailContent,
});

export default GeneralReportRepository;
