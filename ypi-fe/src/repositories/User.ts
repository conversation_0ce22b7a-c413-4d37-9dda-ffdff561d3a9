import { contentType } from './../app/icon';
import { EUserPath } from '@/utilities/types/enums/User';
import request from '@/utilities/request/server';
import {
    IUser,
    INewUserParams,
    EUserRole,
} from '@/utilities/types/entities/user';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import { Slug } from '@/utilities/types/enums/Slug';

export interface IUserListQueryParams extends IPaginationParams {
    roleType: EUserRole;
}

function getList(query: Partial<IUserListQueryParams>) {
    return request<IResponseData<IPaginateResponseData<IUser>>>({
        url: EUserPath.PATH,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
            roleType: query.roleType?.toLowerCase(),
        },
    });
}

function create(data: Partial<INewUserParams>) {
    return request<IResponseData<IUser>>({
        url: EUserPath.PATH,
        method: 'POST',
        data: {
            ...data,
            callbackURL: `${process.env.APP_HOSTNAME}${Slug.SIGN_IN}`,
            roleType: data.roleType?.toLowerCase(),
        },
    });
}

function update(id: string, data: Partial<INewUserParams>) {
    return request<IResponseData<IUser>>({
        url: `${EUserPath.PATH}/${id}`,
        method: 'PUT',
        data: {
            ...data,
            roleType: data.roleType?.toLowerCase(),
        },
    });
}

function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${EUserPath.PATH}/${id}`,
        method: 'DELETE',
    });
}

function uploadAvatar(formData: FormData) {
    return request<IResponseData<IUser>>({
        url: EUserPath.AVATAR_PATH,
        method: 'POST',
        data: formData,
    });
}

const UserRepository = Object.freeze({
    getList,
    create,
    update,
    deleteById,
    uploadAvatar,
});

export default UserRepository;
