import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import {
    ICustomer,
    INewCustomerParams,
} from '@/utilities/types/entities/customer';
import { ECustomerPath } from '@/utilities/types/enums/Customer';

function getList(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<ICustomer>>>({
        url: ECustomerPath.PATH,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function create(data: Partial<INewCustomerParams>) {
    return request<IResponseData<ICustomer>>({
        url: ECustomerPath.PATH,
        method: 'POST',
        data: data,
    });
}

function update(id: string, data: Partial<INewCustomerParams>) {
    return request<IResponseData<ICustomer>>({
        url: `${ECustomerPath.PATH}/${id}`,
        method: 'PUT',
        data: data,
    });
}

function activate(id: string, data: { isActive: boolean }) {
    return request<IResponseData<ICustomer>>({
        url: `${ECustomerPath.PATH}/active/${id}`,
        method: 'PUT',
        data: data,
    });
}

function getById(id: string) {
    return request<IResponseData<ICustomer>>({
        url: `${ECustomerPath.PATH}/${id}`,
        method: 'GET',
    });
}

function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${ECustomerPath.PATH}/${id}`,
        method: 'DELETE',
    });
}

const CustomerRepository = Object.freeze({
    getList,
    create,
    getById,
    update,
    deleteById,
    activate,
});

export default CustomerRepository;
