import request from '@/utilities/request/server';
import { IInCharge } from '@/utilities/types/entities/in-charge';
import { EInChargePath } from '@/utilities/types/enums/InCharge';
import { IResponseData } from '@/utilities/types/request';

function getList() {
    return request<IResponseData<IInCharge[]>>({
        url: EInChargePath.ALLOW,
        method: 'GET',
    });
}

const InChargeRepository = Object.freeze({
    getList,
});

export default InChargeRepository;
