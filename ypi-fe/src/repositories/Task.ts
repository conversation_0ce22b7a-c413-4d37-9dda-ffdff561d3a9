import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import { ETask } from '@/utilities/types/enums/Task';
import { IAssignTask } from '@/utilities/types/entities/task';

function getCalendarTask(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IAssignTask>>>({
        url: `${ETask.CALENDAR_TASK}`,
        method: 'GET',
        params: {
            startTime: query.startTime ?? '',
            endTime: query.endTime ?? '',
        },
    });
}

const TaskRepository = Object.freeze({
    getCalendarTask,
});

export default TaskRepository;
