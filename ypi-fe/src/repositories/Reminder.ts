import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import { IAssignTask } from '@/utilities/types/entities/task';
import { EReminder } from '@/utilities/types/enums/Remimder';

function getListUpcoming(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IAssignTask>>>({
        url: EReminder.UPCOMING,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function getListPast(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IAssignTask>>>({
        url: EReminder.PAST,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

const ReminderRepository = Object.freeze({
    getListUpcoming,
    getListPast,
});

export default ReminderRepository;
