import request from '@/utilities/request/server';
import {
    IPaginationParams,
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';

import { EStaffPath } from '@/utilities/types/enums/Staff';
import { IStaff } from '@/utilities/types/entities/staff';

function getList(query: Partial<IPaginationParams>) {
    return request<IResponseData<IPaginateResponseData<IStaff>>>({
        url: EStaffPath.PATH,
        method: 'GET',
        params: {
            ...query,
            keyword: query.keyword,
            pageIndex: query.pageIndex ?? 1,
            pageSize: query.pageSize ?? 30,
        },
    });
}

function create(data: Partial<IStaff>) {
    return request<IResponseData<IStaff>>({
        url: EStaffPath.PATH,
        method: 'POST',
        data: data,
    });
}

function update(id: string, data: Partial<IStaff>) {
    return request<IResponseData<IStaff>>({
        url: `${EStaffPath.PATH}/${id}`,
        method: 'PUT',
        data: data,
    });
}

function activate(id: string, data: { isActive: boolean }) {
    return request<IResponseData<IStaff>>({
        url: `${EStaffPath.PATH}/active/${id}`,
        method: 'PUT',
        data: data,
    });
}

function getById(id: string) {
    return request<IResponseData<IStaff>>({
        url: `${EStaffPath.PATH}/${id}`,
        method: 'GET',
    });
}

function deleteById(id: string) {
    return request<IResponseData<boolean>>({
        url: `${EStaffPath.PATH}/${id}`,
        method: 'DELETE',
    });
}

function getStaffAllow(query: Partial<IPaginationParams>) {
    return request<IResponseData<IStaff>>({
        url: EStaffPath.STAFF_ALLOW,
        method: 'GET',
        params: query,
    });
}

const StaffRepository = Object.freeze({
    getList,
    create,
    getById,
    update,
    deleteById,
    getStaffAllow,
    activate,
});

export default StaffRepository;
