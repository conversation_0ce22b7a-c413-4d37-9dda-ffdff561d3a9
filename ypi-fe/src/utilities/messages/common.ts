const CommonMessages = Object.freeze({
    CreatedSuccessful: 'Created successful',
    UpdatedSuccessful: 'Updated successful',
    DeletedSuccessful: 'Deleted successful',
    Updating: 'Updating',
    Creating: 'Creating',
    Deleting: 'Deleting',
    DeletingAlert: 'This action cannot revert. Do you want to delete this {0}?',
    ActivateAlert:
        'When you active this {0}, all created tasks related to this {0} will be affected. Do you want to continue being active?',
    DeactivateAlert:
        'When you inactive this {0}, all created tasks related to this {0} will be stopped. Do you want to continue being inactive?',
    Confirm: 'Confirm',
    ConfirmSuccessful: 'Confirm successful',
    UpLoadFile: 'Upload file',
    UpLoadFileSuccessful: 'Upload file successful',
    SendEmail: 'Send email',
    SendEmailSuccessful: 'Send email successful',
    CreatedFailed: 'Created failed',
    Downloading: 'Downloading...',
    DownloadSuccessful: 'Download Successful',
});

export default CommonMessages;
