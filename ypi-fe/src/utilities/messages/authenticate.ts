import { RequestError } from '../types/request';
import { EErrorCode } from '../types/enums/ErrorCodes';

const AuthenticateErrorMessages: Record<string, string> = {
    [EErrorCode.UserNotFound]: 'There is no account associated with this email',
    [EErrorCode.IncorrectPassword]: 'Incorrect password',
    [EErrorCode.TokenNotAccepted]: 'This token is not accepted',
    [EErrorCode.RoleNotAccepted]: 'You do not have permission',
    [EErrorCode.UserAlreadyExists]: 'This user already exists',
    [EErrorCode.EmailNotExist]:
        'There is no account associated with this email',
    [EErrorCode.Unauthorized]: 'Unauthorized',
    [EErrorCode.UserEmailNotExist]:
        'There is no account associated with this email',
};

const emailErrors: EErrorCode[] = [
    EErrorCode.UserNotFound,
    EErrorCode.EmailNotExist,
    EErrorCode.UserAlreadyExists,
];
const passwordErrors = [EErrorCode.IncorrectPassword];

export function getAuthErrorMessage(e: RequestError) {
    if ((emailErrors as string[]).includes(e.error)) {
        return ['email', AuthenticateErrorMessages[e.error]];
    }
    if ((passwordErrors as string[]).includes(e.error)) {
        return ['password', AuthenticateErrorMessages[e.error]];
    }
    return ['default', AuthenticateErrorMessages[e.error] ?? e.message];
}
