import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { getServerSession } from 'next-auth';
import { RequestError } from '@/utilities/types/request';
import { authOptions } from '../lib/auth';

const BASE_URL = process.env.APP_API_HOST;

export default function request<T>(config: AxiosRequestConfig) {
    return getServerSession(authOptions)
        .then((session) => {
            return session?.tokens?.accessToken;
        })
        .then((accessToken) => {
            return axios.request<T>({
                baseURL: BASE_URL,
                ...config,
                headers: accessToken
                    ? {
                          Authorization: `Bearer ${accessToken}`,
                          ...config.headers,
                      }
                    : config.headers,
            });
        })
        .then((res) => res.data)
        .catch((e: AxiosError) => {
            if (e.response) {
                const data = e.response.data as
                    | {
                          error?: string;
                          key?: string;
                          message?: string;
                          msg?: string;
                          statusCode: number;
                      }
                    | undefined;

                throw new RequestError(
                    data?.statusCode ?? e.response.status,
                    data?.key ?? data?.error ?? e.response.statusText,
                    data?.msg ?? data?.message ?? 'Unknown error'
                );
            }
            throw new RequestError(e.status ?? 500, e.name, e.message);
        });
}
