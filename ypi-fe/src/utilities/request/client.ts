import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import queryString from 'query-string';
import { IResponseData, RequestError } from '../types/request';
import { signOut } from 'next-auth/react';
import { EErrorCode } from '../types/enums/ErrorCodes';

export function request<T>(config: AxiosRequestConfig) {
    return axios
        .request<T>({
            baseURL: '/api',
            withCredentials: true,
            ...config,
        })
        .then((res) => res.data)
        .catch((e: AxiosError) => {
            if (e.response) {
                const data = e.response.data as
                    | {
                          error?: string;
                          key?: string;
                          message?: string;
                          msg?: string;
                          statusCode: number;
                      }
                    | undefined;
                if (data?.error === EErrorCode.Unauthorized) {
                    signOut();
                }
                throw new RequestError(
                    data?.statusCode ?? e.response.status,
                    data?.key ?? data?.error ?? e.response.statusText,
                    data?.msg ?? data?.message ?? 'Unknown error'
                );
            }
            throw new RequestError(e.status ?? 500, e.name, e.message);
        });
}

export function fetcher<T>(url: string) {
    const parsedUrl = queryString.parseUrl(url);
    return request<IResponseData<T>>({
        url: parsedUrl.url,
        params: parsedUrl.query,
    }).then((res) => res.data);
}
// TODO: COMBINE LATER

export async function postFetcher<T>(url: string, { arg }: { arg: T }) {
    const res = await fetch(`/api/${url}`, {
        method: 'POST',
        body: JSON.stringify(arg),
    });

    return await res?.json();
}

export async function putFetcher<T>(url: string, { arg }: { arg: T }) {
    const res = await fetch(`/api/${url}`, {
        method: 'PUT',
        body: JSON.stringify(arg),
    });
    return await res.json();
}

export async function postDownloadFile<T>(url: string, { arg }: { arg: T }) {
    const res = await fetch(`/api/${url}`, {
        method: 'POST',
        body: JSON.stringify(arg),
    });
    return res;
}
