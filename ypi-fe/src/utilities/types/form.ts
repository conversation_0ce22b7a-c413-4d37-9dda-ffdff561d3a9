import { AvatarVariantProps } from '@/components/globals/Avatar/style';
import { CheckboxProps } from '@/components/globals/Checkbox';
import { DatePickerProps } from '@/components/globals/DatePickerBase';
import { InputVariants } from '@/components/globals/Input';
import { TInputFileProps } from '@/components/globals/InputFile/types';
import { TInputTagProps } from '@/components/globals/InputTag';
import { MapProps } from '@/components/globals/Map';
import { TMultiSelectProps } from '@/components/globals/MultiSelect/type';
import { FormRadioProps } from '@/components/globals/Radio/FormRadio';
import { RadioFormVariant } from '@/components/globals/Radio/style';

export enum EFormInputType {
    Text = 'text',
    Password = 'password',
    Phone = 'phone',
    Email = 'email',
    Select = 'select',
    Multiple = 'multiple',
    DatePicker = 'datePicker',
    InputFile = 'inputFile',
    Checkbox = 'checkbox',
    MultiSelect = 'multiSelect',
    Radio = 'radio',
    Number = 'number',
    Tag = 'tag',
    Map = 'map',
    Avatar = 'Avatar',
}
export interface IOption {
    id: string;
    name?: string | null;
}

export type TFile = { name: string; id: string } | File;
export type TFiles = TFile[] | FileList | null;
export interface IFormFieldBase {
    type: EFormInputType;
    id: string;
    label?: string;
    name: string;
}

interface IInputOption extends IFormFieldBase, NonNullable<InputVariants> {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
    defaultValue?: string;
    placeholder?: string;
    readOnly?: boolean;
    className?: string;
    disabled?: boolean;
}

interface ITextInputOption extends IInputOption {
    type: EFormInputType.Text;
}

interface INumberInputOption extends IInputOption {
    type: EFormInputType.Number;
}

interface IPasswordInputOption extends IInputOption {
    type: EFormInputType.Password;
    autoGeneratePassword?: boolean;
    showTogglePassword?: boolean;
}

interface IPhoneInputOption extends IInputOption {
    type: EFormInputType.Phone;
    phoneCode: string;
}

interface IEmailInputOption extends IInputOption {
    type: EFormInputType.Email;
}

interface ISelectInputOption extends IInputOption {
    type: EFormInputType.Select;
    options: {
        id: string;
        name?: string | null;
    }[];
}

export interface IMultipleInputOption extends Omit<IFormFieldBase, 'name'> {
    type: EFormInputType.Multiple;
    className?: string;
    inputs: (TFormInputOption & {
        weight?: number;
    })[];
}

export type IDatePicker = IFormFieldBase &
    Partial<DatePickerProps> & {
        type: EFormInputType.DatePicker;
        // TODO: variant
        variant?: 'default' | string;
    };

export type IInputFile = TInputFileProps &
    Omit<IFormFieldBase, 'label'> &
    Partial<TInputFileProps> & {
        type: EFormInputType.InputFile;
    };

export type TFormCheckbox = Omit<IFormFieldBase, 'label'> &
    Partial<CheckboxProps> & {
        type: EFormInputType.Checkbox;
    };

export type TMultiSelect = Omit<IFormFieldBase, 'label'> &
    Partial<TMultiSelectProps> & {
        type: EFormInputType.MultiSelect;
    };

export type TTag = Omit<IFormFieldBase, 'label'> &
    Partial<TInputTagProps> & {
        type: EFormInputType.Tag;
    };

export type TRadio = Omit<IFormFieldBase, 'label'> &
    Partial<FormRadioProps & RadioFormVariant> & {
        type: EFormInputType.Radio;
    };

export type TMap = Omit<IFormFieldBase, 'label'> &
    Partial<MapProps> & {
        type: EFormInputType.Map;
    };

export type TAvatar = Omit<IFormFieldBase, 'label'> &
    Partial<AvatarVariantProps & { className?: string }> & {
        type: EFormInputType.Avatar;
    };

export type TFormInputOption =
    | ITextInputOption
    | IPasswordInputOption
    | IPhoneInputOption
    | ISelectInputOption
    | IEmailInputOption
    | IMultipleInputOption
    | IDatePicker
    | IInputFile
    | TFormCheckbox
    | TMultiSelect
    | TRadio
    | INumberInputOption
    | TTag
    | TMap
    | TAvatar;
