import { ICustomer } from './customer';

export interface IProjectLocation {
    id: string;
    postalCode: string;
    blockNo: string;
    street: string;
    building: string;
    customer: ICustomer;
    lat: string;
    long: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: null;
    isDeleted: boolean;
    __v: number;
}

export interface INewProjectLocationParams {
    postalCode: string;
    blockNo: string;
    street: string;
    building: string;
    lat: string;
    long: string;
}
