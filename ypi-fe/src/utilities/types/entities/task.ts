import { EAssignTaskStatus, ETaskStatus } from '../enums/Task';
import { ILabReport } from './lab-report';
import { IStaff } from './staff';
import { ITank } from './tank';

export interface IAssignTask {
    id: string;
    createdAt: string;
    updatedAt: string;
    status: EAssignTaskStatus;
    tankId: string;
    startAt: string;
    endAt: string;
    code: string;
    phase: string;
    acceptRectify: boolean;
    estimatedValue: number;
    isStaffSubmitted: boolean;
    isInspectorSubmitted: boolean;
    inspectorSubmittedDate: string;
    customerSignature: string;
    customerSignatureDate: string;
    completedDate: string;
    isConfirmed: boolean;
    staffs?: IStaff[];
    tank?: ITank;
    tasks: ITask[];
    remarkBy: {
        id: string;
        fullName: string;
        firstName: string;
        lastName: string;
    };
    labReports?: ILabReport[];
}

export interface ITaskImage {
    id: string;
    name: string;
    mineType: string;
    link: string;
    type: string;
}

export interface ITask {
    id: string;
    createdAt: string;
    updatedAt: string;
    inspectorRemark: string | null;
    staffRemark: string | null;
    images: ITaskImage[];
    inspectorRemarkImages?: ITaskImage[];
    qty: number | null;
    size: string | null;
    status: ETaskStatus;
    extra: boolean;
    title: string;
    type: string;
    staffs: IStaff[];
    before?: ITaskInspection;
    selected: boolean;
    defect?: {
        id: string;
        title?: string;
        type?: string;
    };
    datetime: string;
}

interface ITaskInspection {
    images: ITaskImage[];
    staffRemark: string | null;
}
