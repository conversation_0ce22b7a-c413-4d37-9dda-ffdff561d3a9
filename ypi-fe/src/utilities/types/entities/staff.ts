import { enumToOptions } from '@/utilities/globals/function/generateOptions';
import { IFileUpload } from './upload';
import { IUser } from './user';

export interface IStaff {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    isDeleted: boolean;
    __v: number;
    code: string;
    email: string;
    phoneCode: string;
    phoneNumber: string;
    fullName: string;
    firstName: string;
    lastName: string | null;
    address: string;
    birthday: string;
    passwordSalt: string;
    passwordHash: string;
    position: string;
    passwordChangeToken: string | null;
    isActive: boolean;
    avatar: string;
    certifications?: IFileUpload[];
    documents?: IFileUpload[];
    user?: IUser;
}

export interface INewStaffParams extends Partial<IStaff> {
    fileIds?: string[];
    removeFileIds?: string[];
}

export enum EStaffPosition {
    Worker = 'worker',
    Supervisor = 'supervisor',
}
export const staffOptions = enumToOptions(EStaffPosition);
