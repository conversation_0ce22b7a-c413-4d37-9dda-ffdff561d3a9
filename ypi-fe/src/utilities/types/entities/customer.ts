import { IBuilding } from './building';
import { IInCharge } from './in-charge';

export interface ICustomer {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    buildingCategory: string | null;
    isDeleted: boolean;
    isActive: boolean;
    pubNumber: string;
    pcNumber: string;
    name: string;
    email: string;
    phoneCode: string;
    phoneNumber: string;
    officePhoneCode: string;
    officePhoneNumber: string;
    building: IBuilding | null;
    agent: ICustomerAgent | null;
    nameBuilding: string;
}

export interface ICustomerAgent {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    isDeleted: boolean;
    name: string;
    company: string | null;
    email: string;
    phoneCode: string;
    phoneNumber: string;
    postalCode: string;
    designation: string | null;
    blockNo: string;
    street: string;
    building: string | null;
    inCharge: IInCharge;
    inChargeCategory: string;
}

export interface INewCustomerParams {
    name?: string;
    email: string;
    pubNumber?: string;
    phoneNumber?: string;
    phoneCode?: string;
    officePhoneNumber?: string;
    officePhoneCode?: string;
    buildingCategory?: string;
    nameBuilding: string;
    agent?: Partial<INewCustomerAgentParams>;
}

export interface INewCustomerAgentParams {
    name?: string;
    email?: string;
    company?: string;
    phoneCode?: string;
    phoneNumber?: string;
    postalCode?: string;
    blockNo?: string;
    street?: string;
    inChargeCategory: string;
    designation?: string;
    building?: string;
}
