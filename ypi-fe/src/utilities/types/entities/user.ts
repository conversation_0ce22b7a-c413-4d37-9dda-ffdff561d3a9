export interface IUser {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: boolean | null;
    isDeleted: boolean;
    username: string | null;
    email: string;
    company: string;
    phoneNumber: string;
    fullName: string | null;
    firstName: string | null;
    lastName: string | null;
    isActive: boolean;
    role: {
        name: string;
        value: EUserRole;
    };
    avatar?: string;
}

export enum EUserRole {
    SuperAdmin = 'SUPER_ADMIN',
    Admin = 'ADMIN',
    Inspector = 'INSPECTOR',
}

export interface INewUserParams {
    avatarUrl: string | null;
    avatar?: string | null;

    email: string;
    password: string;
    roleType: EUserRole;
    fullName?: string | null;
    phoneNumber?: string | null;
    phoneCode?: string | null;
    company?: string | null;
}
