import { ETankMaterial, ETankShape, ETankType } from '../enums/Tank';

import { ColorName } from '@/utilities/globals/panda/colors';
import { IAssignTask } from './task';
import { ILabReport } from './lab-report';
import { IMaterial } from './material';
import { IPaginationParams } from '../request';
import { IProjectLocation } from './project-location';

export interface ITank {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: null;
    isDeleted: boolean;
    __v: number;
    airdentity: string;
    code: string;
    type: ETankType;
    shape: string;
    length: string;
    width: string;
    height: string;
    effectiveCap: string;
    floorLevel: string;
    waterSaved: string;
    location: IProjectLocation;
    statisticType: {
        high: string;
        intermediate: string;
        low: string;
    };

    // TODO CHECK LATER
    airdentityId: string;
    customerId: string;
    locationId: string;
    material: IMaterial;
    materialId?: string;
    assignTask: IAssignTask;
    labReports: ILabReport[];
    isActive: boolean; // Tank is active or not
    canInactive: boolean; // This tank can inactive when tank doesn't have any tasks or all tasks are completed
}

export interface INewTankParams {
    airdentityId: string;
    id: string;
    type: string;
    material: string;
    shape: string;
    dimensions: {
        length: number;
        width: number;
        height: number;
    };
    effectiveCap: string;
    floorLevel: string;
}
export interface ITankTypeConfig {
    color: ColorName;
    shortLabel: string;
    label: string;
}

export interface IAssignTank {
    tankId: string;
    startAt: string;
    endAt: string;
    staffs: string[];
}

export interface ITankHistoryListQueryParams extends IPaginationParams {
    datetime?: string;
}
export const TANK_TYPE_CONFIGS = Object.freeze<
    Record<ETankType, ITankTypeConfig>
>({
    [ETankType.LOW]: {
        color: 'yellow_100',
        shortLabel: 'Low',
        label: 'Low Level Tank',
    },
    [ETankType.INTERMEDIATE]: {
        color: 'success_100',
        shortLabel: 'Intermediate',
        label: 'Intermediate Level Tank',
    },
    [ETankType.HIGH]: {
        color: 'primary_100',
        shortLabel: 'High',
        label: 'High Level Tank',
    },
});

export const TANK_MATERIAL_CONFIGS = Object.freeze<
    Record<ETankMaterial, { label: string }>
>({
    [ETankMaterial.FRP_GRP]: {
        label: 'FRP/GRP',
    },
    [ETankMaterial.RC]: {
        label: 'RC',
    },
});

export const TANK_SHAPE_CONFIGS = Object.freeze<
    Record<ETankShape, { label: string }>
>({
    [ETankShape.RECTANGULAR]: {
        label: 'Rectangular',
    },
    [ETankShape.CYLINDRICAL]: {
        label: 'Cylindrical',
    },
    [ETankShape.IRREGULAR]: {
        label: 'Irregular',
    },
});

export const TANK_TYPE_OPTIONS = Object.keys(TANK_TYPE_CONFIGS).map((k) => ({
    id: k,
    name: TANK_TYPE_CONFIGS[k as ETankType].label,
}));

export const TANK_MATERIAL_OPTIONS = Object.keys(TANK_MATERIAL_CONFIGS).map(
    (k) => ({
        id: k,
        name: TANK_MATERIAL_CONFIGS[k as ETankMaterial].label,
    })
);

export const TANK_SHAPE_OPTIONS = Object.keys(TANK_SHAPE_CONFIGS).map((k) => ({
    id: k,
    name: TANK_SHAPE_CONFIGS[k as ETankShape].label,
}));
