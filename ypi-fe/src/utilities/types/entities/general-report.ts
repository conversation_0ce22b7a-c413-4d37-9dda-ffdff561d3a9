import { ILabReport } from './lab-report';
import { IStaff } from './staff';
import { ITank } from './tank';
import { ITask } from './task';

export interface IPreInspection {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    isDeleted: boolean;
    __v: number;
    tank: ITank;
    tankId: string;
    startAt: string;
    endAt: string;
    code: string;
    phase: string;
    status: string;
    acceptRectify: boolean;
    estimatedValue: number;
}

export interface IPreInspectionDetail extends IPreInspection {
    staffs: IStaff[];
    tasks: ITask[];
}

export interface INonCompliance {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    isDeleted: boolean;
    __v: number;
    tank: ITank;
    tankId: string;
    startAt: string;
    endAt: string;
    code: string;
    phase: string;
    status: string;
    acceptRectify: boolean;
    estimatedValue: number;
    staffs?: IStaff[];
    completedDate?: string;
}

export interface INonComplianceDetail extends INonCompliance {
    tasks: ITask[];
}

export interface IComplete {
    id: string;
    code: string;
    status: string;
    completedDate: string;
    reportDate: string;
    sampleTakenDate: string;
    tank: ITank;
}

export interface ICompleteDetail extends IComplete {
    createdAt: string;
    updatedAt: string;
    __v: number;
    tankId: string;
    startAt: string;
    endAt: string;
    acceptRectify: boolean;
    estimatedValue: number;
    completedDate: string;
    customerSignature: string;
    customerSignatureDate: string;
    phase: string;
    inspectorSubmittedDate: string;
    isConfirmed: boolean;
    isInspectorSubmitted: boolean;
    isStaffSubmitted: boolean;
    nonCompliance: INonComplianceCustom;
    preInspection: IPreInspectionCustom;
    staffs: IStaff[];
    tasks: ITask[];
    labReports: ILabReport[];
}

interface INonComplianceCustom {
    date: string;
    staffs: IStaff[];
}

interface IPreInspectionCustom extends INonComplianceCustom {}

export interface ISendEmail {
    assignTaskId: string;
    toEmails: string[];
    subject: string;
}
