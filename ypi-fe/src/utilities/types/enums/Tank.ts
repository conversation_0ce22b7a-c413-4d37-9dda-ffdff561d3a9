export enum ETankType {
    LOW = 'low',
    INTERMEDIATE = 'intermediate',
    HIGH = 'high',
}

// TODO: remove (Dublicate with common)
export enum ETankMaterial {
    FRP_GRP = 'frp/grp',
    RC = 'rc',
    SS = 'ss',
}

export enum ETankShape {
    RECTANGULAR = 'rectangular',
    CYLINDRICAL = 'cylindrical',
    IRREGULAR = 'irregular',
}

export enum ETankPath {
    PATH = 'tank',
    LOCATION_TANK = 'tank/location',
}
