export interface IPaginationBase {
    pageIndex: number;
    pageSize: number;
    keyword?: string;
}
export interface IPaginationParams extends IPaginationBase {
    [key: string]:
        | string
        | number
        | boolean
        | string[]
        | number[]
        | boolean[]
        | undefined;
}

export class RequestError extends Error {
    statusCode: number;
    error: string;
    msg: string;
    constructor(statusCode: number, error: string, message: string) {
        super(message);
        this.statusCode = statusCode;
        this.error = error;
        this.msg = message;
    }
}

export interface IResponseData<T> {
    data: T;
    status: number;
    message: string;
}

export interface IPaginateResponseData<T> {
    records: T[];
    total: number;
    page: number;
    statisticType: {
        high: string;
        intermediate: string;
        low: string;
    };
    // TODO: update api
    // new
    pageCount: number;
}
