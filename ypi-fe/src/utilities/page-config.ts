import { Slug } from './types/enums/Slug';

interface PageConfig {
    title?: string;
}

export default Object.freeze<Record<string, PageConfig>>({
    [Slug.MANAGE_ADMIN]: {
        title: 'Manage admin',
    },
    [Slug.FORGOT_PASSWORD]: {
        title: 'Forgot password',
    },
    [Slug.MANAGE_INSPECTOR]: {
        title: 'Manage inspector',
    },
    [Slug.SIGN_IN]: {
        title: 'Sign in',
    },
    [Slug.RESET_PASSWORD]: {
        title: 'Reset password',
    },
    [Slug.CALENDAR]: {
        title: 'Calendar',
    },
    [Slug.GENERAL_REPORT]: {
        title: 'General report',
    },
    [Slug.GR_PRE_INSPECTION]: {
        title: 'Pre-inspection',
    },
    [Slug.GR_NON_COMPLIANCE]: {
        title: 'Non-compliance',
    },
    [Slug.GR_COMPLETE]: {
        title: 'Complete',
    },
    [Slug.MANAGE_STAFF]: {
        title: 'Manage staff',
    },
    [Slug.CREATE_STAFF]: {
        title: 'Create staff',
    },
    [Slug.REMINDERS]: {
        title: 'Reminders',
    },
    [Slug.MANAGE_CUSTOMERS]: {
        title: 'Manage customers',
    },
    [Slug.CREATE_CUSTOMER]: {
        title: 'Create customers',
    },
});
