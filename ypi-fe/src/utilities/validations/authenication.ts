import { z } from 'zod';

export const signInSchema = z.object({
    email: z.string().email('There is no account associated with this email'),
    password: z
        .string()
        .min(6, { message: 'Password must be at least 6 characters long' }),
});

export const ForgotPasswordSchema = z.object({
    email: z.string().email('There is no account associated with this email'),
});

export const ResetPasswordSchema = z
    .object({
        password: z
            .string()
            .min(6, { message: 'Password must be at least 6 characters long' }),
        confirmPassword: z.string().min(6),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords must match',
        path: ['confirmPassword'],
    });
