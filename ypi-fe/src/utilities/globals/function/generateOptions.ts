import { IOption } from '@/utilities/types/form';

/**
 * Helper to produce an array of enum descriptors.
 * @param enumeration Enumeration object.
 * @param separatorRegex Regex that would catch the separator in your enum key.
 */
export function enumToOptions<T>(
    enumeration: T,
    separatorRegex: RegExp = /_/g
): IOption[] {
    // @ts-ignore
    return (Object.keys(enumeration) as Array<keyof T>)
        .filter((key) => isNaN(Number(key)))
        .filter(
            (key) =>
                typeof enumeration[key] === 'number' ||
                typeof enumeration[key] === 'string'
        )
        .map((key) => ({
            id: enumeration[key],
            name: String(key).replace(separatorRegex, ' '),
        }));
}
