'use server';

import puppeteer from 'puppeteer';
export const generatePdfFromStringHtml = async (html: string) => {
    if (!html) {
        throw new Error('Invalid HTML response');
    }

    const configOnServer = {
        executablePath: '/usr/bin/chromium-browser',
    };
    try {
        const browser = await puppeteer.launch(
            process.env.NODE_ENV === 'production' ? configOnServer : undefined
        );

        if (!browser) throw new Error('Browser not available');
        const page = await browser.newPage();
        await page.setContent(html);
        const pdf = await page.pdf({ format: 'A4' });
        await browser.close();
        return pdf;
    } catch (e: any) {
        throw e;
    }
};
