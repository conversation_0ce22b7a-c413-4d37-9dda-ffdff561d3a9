interface PasswordOptions {
    minLength?: number;
    minSymbols?: number;
    minNumbers?: number;
    minLowercase?: number;
    minUppercase?: number;
}

const symbols = '!@#$%^&*()_+[]{}|;:,.<>?';
const symbolsRegex = /[!@#$%^&*()_+[\]{}|;:,.<>?]/;

const numbers = '0123456789';
const numbersRegex = /\d/;

const lowercase = 'abcdefghijklmnopqrstuvwxyz';
const lowercaseRegex = /[a-z]/;

const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const uppercaseRegex = /[A-Z]/;

const generateRandomChar = (characters: string): string => {
    const randomIndex = Math.floor(Math.random() * characters.length);
    return characters.charAt(randomIndex);
};

export function generatePassword({
    minLength = 8,
    minSymbols = 1,
    minNumbers = 1,
    minLowercase = 1,
    minUppercase = 1,
}: PasswordOptions): string {
    let password = '';

    // Add required symbols
    for (let i = 0; i < minSymbols; i++) {
        password += generateRandomChar(symbols);
    }

    // Add required numbers
    for (let i = 0; i < minNumbers; i++) {
        password += generateRandomChar(numbers);
    }

    // Add required lowercase letters
    for (let i = 0; i < minLowercase; i++) {
        password += generateRandomChar(lowercase);
    }

    // Add required uppercase letters
    for (let i = 0; i < minUppercase; i++) {
        password += generateRandomChar(uppercase);
    }

    // Add remaining characters to meet the minimum length
    const remainingLength = minLength - password.length;
    const allCharacters = symbols + numbers + lowercase + uppercase;

    for (let i = 0; i < remainingLength; i++) {
        password += generateRandomChar(allCharacters);
    }

    // Shuffle the password characters
    password = password
        .split('')
        .sort(() => Math.random() - 0.5)
        .join('');

    return password;
}

export function isStrongPassword(
    password: string,
    options?: PasswordOptions
): boolean {
    // Minimum requirements
    const {
        minLength = 8,
        minSymbols = 1,
        minNumbers = 1,
        minLowercase = 1,
        minUppercase = 1,
    } = options ?? {};

    if (password.length < minLength) {
        return false;
    }

    // Check symbols
    if ((password.match(symbolsRegex) || []).length < minSymbols) {
        return false;
    }

    // Check numbers
    if ((password.match(numbersRegex) || []).length < minNumbers) {
        return false;
    }

    // Check lowercase letters
    if ((password.match(lowercaseRegex) || []).length < minLowercase) {
        return false;
    }

    // Check uppercase letters
    if ((password.match(uppercaseRegex) || []).length < minUppercase) {
        return false;
    }

    return true;
}
