const download = (filename: string, content: string) => {
    const element = document.createElement('a');
    element.setAttribute('href', content);
    element.setAttribute('download', filename);
    element.style.display = 'none';
    document.body.appendChild(element);

    element.click();

    document.body.removeChild(element);
};

const downloadFileBlob = async ({
    file,
    filename,
}: {
    file: Response;
    filename?: string;
}) => {
    try {
        const blob = await file.blob();
        const url = URL.createObjectURL(blob);
        download(filename || 'download', url);
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error(error);
    }
};
const handleDownload = async (fileLink?: string, filename?: string) => {
    if (!fileLink) return;

    try {
        const result = await fetch(fileLink, {
            method: 'GET',
            headers: {},
        });
        const blob = await result.blob();

        const url = URL.createObjectURL(blob);

        download(filename || 'download', url);
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error(error);
    }
};

export { handleDownload, download, downloadFileBlob };
