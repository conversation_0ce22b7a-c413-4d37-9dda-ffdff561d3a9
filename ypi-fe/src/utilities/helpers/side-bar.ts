import { css } from '@/styled-system/css';
import pageConfig from '../page-config';
import { Slug } from '../types/enums/Slug';
import ISideBarItem from '../types/side-bar-item';
import SideBarItem from '../types/side-bar-item';

export function getSideBarItemFromSlug(
    slug: Slug,
    subItems?: ISideBarItem[],
    subMenuClassName?: string
): SideBarItem {
    return {
        path: slug,
        title: pageConfig[slug].title ?? '',
        subItems,
        subMenuClassName,
    };
}
