import { IProjectLocation } from '../types/entities/project-location';
import {
    ITankTypeConfig,
    TANK_MATERIAL_CONFIGS,
    TANK_SHAPE_CONFIGS,
    TANK_TYPE_CONFIGS,
} from '../types/entities/tank';
import { ETankMaterial, ETankShape, ETankType } from '../types/enums/Tank';

export function getTankTypeConfig(type: string): ITankTypeConfig | undefined {
    return TANK_TYPE_CONFIGS[type as ETankType];
}

export function getTankMaterialConfig(
    material: string
): { label: string } | undefined {
    return TANK_MATERIAL_CONFIGS[material as ETankMaterial];
}

export function getTankShapeConfig(
    shape: string
): { label: string } | undefined {
    return TANK_SHAPE_CONFIGS[shape as ETankShape];
}

export function getFullAddressTank(location: IProjectLocation): string {
    return `${location?.street || ''} ${location?.blockNo || ''} ${
        location?.building || ''
    }`;
}
