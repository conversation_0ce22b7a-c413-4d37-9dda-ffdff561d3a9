import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import AuthRepository from '../../repositories/Auth';
import { RequestError } from '../types/request';
import { EUserRole } from '../types/entities/user';

type TCredential = {
    email: string;
    password: string;
};

declare module 'next-auth/jwt' {
    interface JWT {
        tokens?: {
            accessToken: string;
            refreshToken: string;
            accessTokenExp: string;
            user: {
                name?: string;
                email: string;
                role: EUserRole;
            };
        };
    }
}

declare module 'next-auth' {
    interface Session {
        tokens?: {
            accessToken: string;
            refreshToken: string;
            accessTokenExp: string;
        };
        user?: {
            name?: string;
            email: string;
            role: EUserRole;
        };
    }
}

export const authOptions: NextAuthOptions = {
    // Secret for Next-auth, without this JWT encryption/decryption won't work
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: '/auth/sign-in',
        // signOut: '/auth/sign-out',
    },
    // Configure one or more authentication providers
    providers: [
        CredentialsProvider({
            // The name to display on the sign-in form (e.g. "Sign in with...")
            name: 'Credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email',
                    placeholder: 'Your email',
                },
                password: {
                    label: 'Password',
                    type: 'password',
                    placeholder: 'Your Password',
                },
            },
            async authorize(
                credentials:
                    | TCredential
                    | Record<'email' | 'password', string>
                    | undefined
            ) {
                const credentialDetails = {
                    email: credentials?.email || '',
                    password: credentials?.password || '',
                };
                const { data } = await AuthRepository.signIn(
                    credentialDetails.email,
                    credentialDetails.password
                ).catch((e: RequestError) => {
                    throw new Error(e.error);
                });

                if (data) {
                    return {
                        id: data.user.id,
                        name: data.user.fullName,
                        email: data.user.email,
                        tokens: {
                            user: {
                                role: data.user.role.value,
                                id: data.user.id,
                                name: data.user.fullName,
                                email: data.user.email,
                            },
                            accessToken: data.accessToken,
                            refreshToken: data.refreshToken,
                            accessTokenExp: data.accessTokenExp,
                        },
                    };
                }
                return null;
            },
        }),
    ],
    callbacks: {
        async jwt({ token, user, account }) {
            if (account && user) {
                return {
                    ...token,
                    ...account,
                    ...user,
                };
            }

            return token;
        },
        async session({ session, token }) {
            if (!token.tokens) {
                return session;
            }
            const { user, ...rest } = token.tokens;
            session.tokens = rest;
            session.user = user;

            if (token?.tokens?.accessTokenExp) {
                session.expires = token?.tokens?.accessTokenExp;
            }

            return session;
        },
        async redirect({ url, baseUrl }) {
            // Allows relative callback URLs
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            // Allows callback URLs on the same origin
            else if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        },
    },
    debug: process.env.NODE_ENV === 'development',
};
