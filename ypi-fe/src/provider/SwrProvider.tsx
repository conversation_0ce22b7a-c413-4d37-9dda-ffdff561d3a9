'use client';

import { fetcher } from '@/utilities/request/client';
import React, { ReactNode } from 'react';
import { SWRConfig, SWRConfiguration } from 'swr';

const swrConfig: SWRConfiguration = {
    fetcher: fetcher,
    revalidateOnFocus: false,
    errorRetryCount: 2,
    errorRetryInterval: 2000,
    loadingTimeout: 1000,
    dedupingInterval: 5000,
    revalidateIfStale: true,
    revalidateOnMount: true,
};

export function SwrProvider({ children }: { children: ReactNode }) {
    return <SWRConfig value={{ ...swrConfig }}>{children}</SWRConfig>;
}
