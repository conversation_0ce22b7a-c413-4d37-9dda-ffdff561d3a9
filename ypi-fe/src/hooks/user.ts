import { toast } from '@/components/Toaster';
import { IUserListQueryParams } from '@/repositories/User';
import CommonMessages from '@/utilities/messages/common';
import { putFetcher, request } from '@/utilities/request/client';
import { INewUserParams, IUser } from '@/utilities/types/entities/user';
import { EUserPath } from '@/utilities/types/enums/User';
import { IResponseData } from '@/utilities/types/request';
import { useCallback } from 'react';
import { useLazyLoad } from './lazyload';
import useSWRMutation from 'swr/mutation';
import { EAuthPath } from '@/utilities/types/enums/Auth';

export const useUserList = (params: Partial<IUserListQueryParams>) => {
    return useLazyLoad<IUser>(EUserPath.PATH, params);
};

export function useCreateUser() {
    return useCallback((data: Partial<INewUserParams>) => {
        return toast.promise(
            request<IResponseData<boolean>>({
                url: EUserPath.PATH,
                method: 'POST',
                data,
            }),
            {
                loading: CommonMessages.Creating,
                success: CommonMessages.CreatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useUpdateUser() {
    return useCallback((user: IUser, data: Partial<INewUserParams>) => {
        return toast.promise(
            request<IResponseData<boolean>>({
                url: `${EUserPath.PATH}/${user.id}`,
                method: 'PUT',
                data,
            }),
            {
                loading: CommonMessages.Updating,
                success: CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useDeleteUser() {
    return useCallback((user: IUser) => {
        return toast.promise(
            request<IResponseData<boolean>>({
                url: `${EUserPath.PATH}/${user.id}`,
                method: 'DELETE',
            }),
            {
                loading: CommonMessages.Deleting,
                success: CommonMessages.DeletedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export async function uploadAvatar({ file }: { file: File }) {
    const data = new FormData();
    data.append('avatar', file, file.name);

    return toast.promise(
        request<IResponseData<{ avatar: string }>>({
            url: EUserPath.AVATAR_PATH,
            method: 'POST',
            data,
        }),
        {
            loading: CommonMessages.Updating,
            success: CommonMessages.UpdatedSuccessful,
            error: (err) => err.message,
        }
    );
}

export function useUpdateProfile() {
    return useSWRMutation(EAuthPath.NEXT_PROFILE, putFetcher<Partial<IUser>>);
}
