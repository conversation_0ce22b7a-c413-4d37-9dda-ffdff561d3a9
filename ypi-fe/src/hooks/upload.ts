import { request } from '@/utilities/request/client';
import { IFileUpload } from '@/utilities/types/entities/upload';
import { EUploadPath } from '@/utilities/types/enums/Upload';
import { IResponseData } from '@/utilities/types/request';
import { useCallback } from 'react';
import { toast } from '@/components/Toaster';
import CommonMessages from '@/utilities/messages/common';
import { EFilePath } from '@/utilities/types/enums/File';

export async function uploadFiles({
    files,
    name,
    staffId,
}: {
    files: File[] | FileList;
    name?: string;
    staffId?: string;
}) {
    const data = new FormData();

    Array.from(files).forEach((file) => {
        data.append(name || 'files', file, file.name);
    });

    if (staffId) {
        data.append('staffId', staffId);
    }

    return request<IResponseData<IFileUpload[]>>({
        url: EUploadPath.PATH,
        method: 'POST',
        data,
        headers: {
            'Content-Type': 'multipart/form-data', // Important: Set content type to multipart/form-data
        },
    });
}

export async function uploadImages({
    files,
    name,
}: {
    files: File[] | FileList;
    name?: string;
    staffId?: string;
}) {
    const data = new FormData();

    Array.from(files).forEach((file) => {
        data.append(name || 'images', file, file.name);
    });

    return toast.promise(
        request<IResponseData<IFileUpload[]>>({
            url: EUploadPath.IMAGE_PATH,
            method: 'POST',
            data,
            headers: {
                'Content-Type': 'multipart/form-data', // Important: Set content type to multipart/form-data
            },
        }),
        {
            loading: CommonMessages.UpLoadFile,
            success: CommonMessages.UpLoadFileSuccessful,
            error: (err) => err.message,
        }
    );
}

export function useDeleteFile() {
    return useCallback((fileId: string) => {
        return toast.promise(
            request<boolean>({
                url: `${EFilePath.PATH}/${fileId}`,
                method: 'DELETE',
            }),
            {
                loading: CommonMessages.Deleting,
                success: CommonMessages.DeletedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}
