import queryString from 'query-string';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
    IPaginateResponseData,
    IResponseData,
} from '@/utilities/types/request';
import useSWR, { SWRResponse } from 'swr';
import { fetcher } from '@/utilities/request/client';

export function useLazyLoad<T>(
    path: string,
    query?: Record<string, any>,
    pageSize = 30
) {
    const [pageIndex, setPageIndex] = useState(1);
    const [res, setRes] = useState<IPaginateResponseData<T> | undefined>();

    const ref = useRef<{
        pageSize: number;
        path: string;
        query?: Record<string, any>;
    }>({
        pageSize,
        path,
        query,
    }).current;

    const searchParams = queryString.stringify({
        ...ref.query,
        pageIndex,
        pageSize: ref.pageSize,
    });
    const { data, ...rest } = useSWR<IPaginateResponseData<T>>(
        query ? `${ref.path}?${searchParams}` : path,
        fetcher
    );
    const canNext = !rest.isLoading && pageIndex < (data?.pageCount ?? 1);

    const refresh = useCallback(() => {
        if (pageIndex === 1) {
            rest.mutate();
            return;
        }
        setPageIndex(1);
    }, [rest.mutate, pageIndex]);

    const next = useCallback(() => {
        if (canNext) {
            setPageIndex((v) => v + 1);
        }
    }, [canNext]);

    useEffect(() => {
        if (data) {
            const prevSize = Math.max(0, ((data?.page || 1) - 1) * pageSize);
            setRes((prevRes) => ({
                ...data,
                records: (prevRes?.records ?? [])
                    .slice(0, prevSize)
                    .concat(data.records),
            }));
        }
    }, [data]);

    useEffect(() => {
        ref.pageSize = pageSize;
        ref.path = path;
        ref.query = query;
        refresh();
    }, [pageSize, path, query]);

    return {
        ...rest,
        data: res,
        refresh,
        canNext,
        next,
    };
}
