import { fetcher, request } from '@/utilities/request/client';
import { useCallback } from 'react';
import useSWR from 'swr';
import { toast } from '@/components/Toaster';
import {
    INewProjectLocationParams,
    IProjectLocation,
} from '@/utilities/types/entities/project-location';
import { ELocationPath } from '@/utilities/types/enums/Location';
import CommonMessages from '@/utilities/messages/common';
import { useLazyLoad } from './lazyload';

const PAGE_SIZE = 10;

export const useLocationList = (
    customerId: string,
    params?: Record<string, any>
) => {
    const path = `${ELocationPath.CUSTOMER_LOCATION}/${customerId}`;
    return useLazyLoad<IProjectLocation>(path, params);
};

export function useLocationById(id?: string) {
    return useSWR<IProjectLocation>(
        !!id ? `${ELocationPath.PATH}/${id}` : null,
        fetcher
    );
}

export function useCreateLocation(customerId: string) {
    return useCallback((data: Partial<INewProjectLocationParams>) => {
        return toast.promise(
            request<boolean>({
                url: `${ELocationPath.CUSTOMER_LOCATION}/${customerId}`,
                method: 'POST',
                data,
            }),
            {
                loading: CommonMessages.Creating,
                success: () => CommonMessages.CreatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useUpdateLocation() {
    return useCallback(
        (
            projectLocation: IProjectLocation,
            data: Partial<INewProjectLocationParams>
        ) => {
            return toast.promise(
                request<boolean>({
                    url: `${ELocationPath.PATH}/${projectLocation.id}`,
                    method: 'PUT',
                    data,
                }),
                {
                    loading: CommonMessages.Updating,
                    success: CommonMessages.UpdatedSuccessful,
                    error: (err) => err.message,
                }
            );
        },
        []
    );
}

export function useDeleteLocation() {
    return useCallback((location: IProjectLocation) => {
        return toast.promise(
            request<boolean>({
                url: `${ELocationPath.PATH}/${location.id}`,
                method: 'DELETE',
            }),
            {
                loading: CommonMessages.Deleting,
                success: CommonMessages.DeletedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}
