import { fetcher, postFetcher, putFetcher } from '@/utilities/request/client';
import { EStaffPosition, IStaff } from '@/utilities/types/entities/staff';
import { EStaffPath } from '@/utilities/types/enums/Staff';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { useLazyLoad } from './lazyload';
import queryString from 'query-string';
import { useCallback } from 'react';
import { toast } from '@/components/Toaster';
import { request } from '@/utilities/request/client';
import CommonMessages from '@/utilities/messages/common';

export function useCreateStaff() {
    return useSWRMutation(`/${EStaffPath.PATH}`, postFetcher<Partial<IStaff>>);
}

export function useStaffList(query: Record<string, any>) {
    return useLazyLoad<IStaff>(EStaffPath.PATH, query);
}

export function useStaffById(staffId: string) {
    return useSWR(`${EStaffPath.PATH}/${staffId}`, fetcher<Partial<IStaff>>);
}

export function useUpdateStaff(staffId: string) {
    return useSWRMutation(
        `${EStaffPath.PATH}/${staffId}`,
        putFetcher<Partial<IStaff>>
    );
}

export function useActivateStaff() {
    return useCallback((id: string, data: { isActive: boolean }) => {
        return toast.promise(
            request<boolean>({
                url: `${EStaffPath.PATH}/active/${id}`,
                method: 'PUT',
                data,
            }),
            {
                loading: CommonMessages.Updating,
                success: () => CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useStaffAllow(query?: EStaffPosition[]) {
    const searchParams = queryString.stringify(
        { position: query },
        { arrayFormat: 'bracket' }
    );

    return useSWR(
        `${EStaffPath.STAFF_ALLOW}?${searchParams}`,
        fetcher<IStaff[]>
    );
}
