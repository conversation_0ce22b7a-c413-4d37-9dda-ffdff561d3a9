import useSWR from 'swr';
import { useLazyLoad } from './lazyload';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import {
    ICompleteDetail,
    INonComplianceDetail,
    IPreInspectionDetail,
    ISendEmail,
} from '@/utilities/types/entities/general-report';
import { LocalStorage } from '@/utilities/types/enums/LocalStorage';
import {
    fetcher,
    postDownloadFile,
    postFetcher,
    request,
} from '@/utilities/request/client';
import { IResponseData } from '@/utilities/types/request';
import { IFileUpload } from '@/utilities/types/entities/upload';
import { EUploadPath } from '@/utilities/types/enums/Upload';
import useSWRMutation from 'swr/mutation';

export const useGeneralReport = <T>(
    endpoint: EGeneralReport,
    params?: Partial<Record<string, any>>
) => {
    return useLazyLoad<T>(endpoint, params, 10);
};

export function usePreInspectionId(id: string) {
    return useSWR<IPreInspectionDetail>(
        `${EGeneralReport.PRE_INSPECTION}/${id}`,
        fetcher
    );
}

export function useNonComplianceId(id: string) {
    return useSWR<INonComplianceDetail>(
        `${EGeneralReport.NON_COMPLIANCE}/${id}`,
        fetcher
    );
}

export function useCompleteId(id: string) {
    return useSWR<ICompleteDetail>(`${EGeneralReport.COMPLETE}/${id}`, fetcher);
}

export function useDefect() {
    const setDefectId = (defectId: string) => {
        if (typeof window === 'undefined') return;
        const defectIds = getDefectIds();
        if (!defectIds.includes(defectId)) {
            return localStorage.setItem(
                LocalStorage.DEFECT_IDS,
                JSON.stringify([...defectIds, defectId])
            );
        }

        return localStorage.setItem(
            LocalStorage.DEFECT_IDS,
            JSON.stringify(
                defectIds.filter((item: string) => item !== defectId)
            )
        );
    };

    const getDefectIds = () => {
        if (typeof window === 'undefined') return;
        const defectIds = localStorage.getItem(LocalStorage.DEFECT_IDS);
        if (!defectIds) return [];
        return JSON.parse(defectIds);
    };

    const removeAllDefectIds = () => {
        if (typeof window === 'undefined') return;
        return localStorage.setItem(
            LocalStorage.DEFECT_IDS,
            JSON.stringify([])
        );
    };

    return { getDefectIds, setDefectId, removeAllDefectIds };
}

export function uploadFilePdf(file: File, phaseId: string) {
    const data = new FormData();
    data.append('labReports', file, file.name);
    data.append('phaseId', phaseId);

    return request<IResponseData<IFileUpload[]>>({
        url: EUploadPath.PATH,
        method: 'POST',
        data,
    });
}

export function useSendEmail(endpoint: EGeneralReport) {
    return useSWRMutation(`/${endpoint}`, postFetcher<Partial<ISendEmail>>);
}

export function useDownloadEmailContent(endpoint: EGeneralReport) {
    return useSWRMutation(`/${endpoint}`, postDownloadFile<any>);
}

export function getPreInspectionIdClient(id: string) {
    return request<IResponseData<IPreInspectionDetail>>({
        url: `${EGeneralReport.PRE_INSPECTION}/${id}`,
        method: 'GET',
    });
}

export function getNonComplianceIdClient(id: string) {
    return request<IResponseData<INonComplianceDetail>>({
        url: `${EGeneralReport.NON_COMPLIANCE}/${id}`,
        method: 'GET',
    });
}

export function getCompleteIdClient(id: string) {
    return request<IResponseData<ICompleteDetail>>({
        url: `${EGeneralReport.COMPLETE}/${id}`,
        method: 'GET',
    });
}
