import {
    ICustomer,
    INewCustomerParams,
} from '@/utilities/types/entities/customer';
import { fetcher, request } from '@/utilities/request/client';

import CommonMessages from '@/utilities/messages/common';
import { ECustomerPath } from '@/utilities/types/enums/Customer';
import { toast } from '@/components/Toaster';
import { useCallback } from 'react';
import { useLazyLoad } from './lazyload';
import useSWR from 'swr';

export const useCustomerList = (params: Partial<Record<string, any>>) => {
    return useLazyLoad<ICustomer>(ECustomerPath.PATH, params, 10);
};

export function useCustomerById(id: string) {
    return useSWR<ICustomer>(`${ECustomerPath.PATH}/${id}`, fetcher);
}

export function useCreateCustomer() {
    return useCallback((data: Partial<INewCustomerParams>) => {
        return toast.promise(
            request<boolean>({
                url: ECustomerPath.PATH,
                method: 'POST',
                data,
            }),
            {
                loading: CommonMessages.Creating,
                success: CommonMessages.CreatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useUpdateCustomer() {
    return useCallback((user: ICustomer, data: Partial<INewCustomerParams>) => {
        return toast.promise(
            request<boolean>({
                url: `${ECustomerPath.PATH}/${user.id}`,
                method: 'PUT',
                data,
            }),
            {
                loading: CommonMessages.Updating,
                success: () => CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useActivateCustomer() {
    return useCallback((id: string, data: { isActive: boolean }) => {
        return toast.promise(
            request<boolean>({
                url: `${ECustomerPath.PATH}/active/${id}`,
                method: 'PUT',
                data,
            }),
            {
                loading: CommonMessages.Updating,
                success: () => CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useDeleteCustomer() {
    return useCallback((user: Partial<ICustomer>) => {
        return toast.promise(
            request<boolean>({
                url: `${ECustomerPath.PATH}/${user.id}`,
                method: 'DELETE',
            }),
            {
                loading: CommonMessages.Deleting,
                success: CommonMessages.DeletedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}
