import { EAuthPath } from '@/utilities/types/enums/Auth';
import { fetcher, request } from '@/utilities/request/client';
import { IResponseData } from '@/utilities/types/request';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect } from 'react';
import useSWR from 'swr';
import { IUser } from '@/utilities/types/entities/user';

export function useRedirectIfSignedIn() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const callbackUrl = searchParams.get('callbackUrl') || '/';
    const session = useSession();
    useEffect(() => {
        if (session.status === 'authenticated') {
            const returnURL = process.env.APP_HOSTNAME;
            if (callbackUrl.startsWith('/')) {
                router.replace(callbackUrl);
                return;
            }
            try {
                if (new URL(callbackUrl).origin === returnURL) {
                    router.replace(callbackUrl);
                    return;
                }
            } catch (e) {
                //
            }
            router.replace('/');
        }
    }, [session.status]);
}

export function useForgotPassword() {
    return useCallback((email: string) => {
        return request<IResponseData<boolean>>({
            url: `/${EAuthPath.FORGOT_PASSWORD}`,
            data: { email },
            method: 'POST',
        });
    }, []);
}

export function useNewPassword() {
    const newPassword = useCallback((token: string, password: string) => {
        return request<IResponseData<boolean>>({
            url: `/${EAuthPath.NEW_PASSWORD}`,
            data: { token, password },
            method: 'POST',
        });
    }, []);
    const verifyToken = useCallback((token: string) => {
        return request<IResponseData<boolean>>({
            url: `/${EAuthPath.FORGOT_PASSWORD_VERIFY_TOKEN}`,
            method: 'GET',
            params: { token },
        });
    }, []);
    return { newPassword, verifyToken };
}

export function useProfile() {
    return useSWR<IUser>(EAuthPath.NEXT_PROFILE, fetcher);
}
