import {
    fetcher,
    postFetcher,
    putFetcher,
    request,
} from '@/utilities/request/client';
import { useCallback, useRef } from 'react';
import useSWR, { SWRResponse } from 'swr';
import { toast } from '@/components/Toaster';
import { ETankPath } from '@/utilities/types/enums/Tank';
import CommonMessages from '@/utilities/messages/common';
import { IAssignTank, ITank } from '@/utilities/types/entities/tank';
import { useLazyLoad } from './lazyload';
import useSWRMutation from 'swr/mutation';
import { EAssignTask } from '@/utilities/types/enums/AssignTask';
import { IAssignTask } from '@/utilities/types/entities/task';

export const useTankList = (
    locationId: string,
    params?: Record<string, any>
) => {
    const path = `${ETankPath.LOCATION_TANK}/${locationId}`;
    return useLazyLoad<ITank>(path, params);
};

export function useTankById(id: string, assignTaskId?: string) {
    let path = `${ETankPath.PATH}/${id}`;
    if (assignTaskId) path = `${path}?assignTaskId=${assignTaskId}`;
    return useSWR<ITank>(path, fetcher);
}

export function useCreateTank() {
    return useSWRMutation(`/${ETankPath.PATH}`, postFetcher<Partial<ITank>>);
}

export function useUpdateTank(tankId: string) {
    return useSWRMutation(
        `${ETankPath.PATH}/${tankId}`,
        putFetcher<Partial<ITank>>
    );
}

export function useDeleteTank() {
    return useCallback((tankId: string) => {
        return toast.promise(
            request<boolean>({
                url: `${ETankPath.PATH}/${tankId}`,
                method: 'DELETE',
            }),
            {
                loading: CommonMessages.Deleting,
                success: CommonMessages.DeletedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useCreateAssignTank() {
    return useSWRMutation(
        `${EAssignTask.PATH}`,
        postFetcher<Partial<IAssignTank>>
    );
}

export function useUpdateAssignTank(id: string) {
    return useSWRMutation(
        `${EAssignTask.PATH}/${id}`,
        putFetcher<Partial<IAssignTank>>
    );
}

export function useCleanDisinfectionByTank(
    id: string,
    params?: Record<string, any>
) {
    const path = `${ETankPath.PATH}/${id}/clean-disinfection`;
    return useLazyLoad<IAssignTask>(path, params);
}

export function useRemarkInspectorByTank(
    id: string,
    params?: Record<string, any>
) {
    const path = `${ETankPath.PATH}/${id}/remark-inspector`;
    return useLazyLoad<IAssignTask>(path, params);
}

export const useTankHistory = (id: string, params?: Record<string, any>) => {
    const path = `${ETankPath.PATH}/${id}/clean-disinfection/history`;
    return useLazyLoad<IAssignTask>(path, params);
};

export function useActivateTank() {
    return useCallback((id: string, data: { isActive: boolean }) => {
        return toast.promise(
            request<boolean>({
                url: `${ETankPath.PATH}/active/${id}`,
                method: 'PUT',
                data,
            }),
            {
                loading: CommonMessages.Updating,
                success: () => CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}
