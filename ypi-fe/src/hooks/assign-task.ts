import CommonMessages from '@/utilities/messages/common';
import { postFetcher, request } from '@/utilities/request/client';
import {
    IAssignTaskStaff,
    ITaskConfirm,
} from '@/utilities/types/entities/assign-task';
import { EAssignTask } from '@/utilities/types/enums/AssignTask';
import { capitalize } from 'lodash';
import { useCallback } from 'react';
import toast from 'react-hot-toast';
import useSWRMutation from 'swr/mutation';

export function useAssignTaskStaff() {
    return useCallback((payload: IAssignTaskStaff) => {
        return toast.promise(
            request<Partial<IAssignTaskStaff>>({
                url: `${EAssignTask.ASSIGN_STAFF}`,
                method: 'POST',
                data: payload,
            }),
            {
                loading: CommonMessages.Creating,
                success: CommonMessages.CreatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useUpdateAssignTaskStaff(id: string) {
    return useCallback((payload: IAssignTaskStaff) => {
        return toast.promise(
            request<Partial<IAssignTaskStaff>>({
                url: `${EAssignTask.ASSIGN_STAFF}/${id}`,
                method: 'PUT',
                data: payload,
            }),
            {
                loading: CommonMessages.Updating,
                success: CommonMessages.UpdatedSuccessful,
                error: (e) => {
                    const errorMsg = e?.msg?.[0]?.constraints || e?.msg;
                    return capitalize(errorMsg) || 'Something went wrong';
                },
            }
        );
    }, []);
}

export function useTaskConfirm() {
    return useSWRMutation(
        `/${EAssignTask.TASK_CONFIRM}`,
        postFetcher<Partial<ITaskConfirm>>
    );
}

export function useDeleteAssignTask() {
    return useCallback((id: string) => {
        return toast.promise(
            request<boolean>({
                url: `${EAssignTask.PATH}/${id}`,
                method: 'DELETE',
            }),
            {
                loading: CommonMessages.Deleting,
                success: CommonMessages.DeletedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}

export function useUpdateSampleTakenDate() {
    return useCallback((id: string, data: { datetime: string }) => {
        return toast.promise(
            request<boolean>({
                url: `${EAssignTask.PATH}/complete/${id}/lab-sample-taken`,
                method: 'PUT',
                data,
            }),
            {
                loading: CommonMessages.Updating,
                success: CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            }
        );
    }, []);
}
