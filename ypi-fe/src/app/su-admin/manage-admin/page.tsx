'use client';

import {
    EUserRole,
    INewUserParams,
    IUser,
} from '@/utilities/types/entities/user';
import {
    useCreateUser,
    useDeleteUser,
    useUpdateUser,
    useUserList,
} from '@/hooks/user';

import CommonMessages from '@/utilities/messages/common';
import ConfirmModal from '@/components/globals/ConfirmModal';
import CreateNewForm from '@/components/manage-admin/CreateNewForm';
import { IUserListQueryParams } from '@/repositories/User';
import Modal from '@/components/globals/Modal';
import { Slug } from '@/utilities/types/enums/Slug';
import Topbar from '@/components/globals/Topbar';
import UserList from '@/components/manage-admin/UserList';
import { flex } from '@/styled-system/patterns';
import { format } from '@/utilities/globals/function/string';
import pageConfig from '@/utilities/page-config';
import { useState } from 'react';

export default function ManageAdmin() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [createModelOpen, setCreateModelOpen] = useState(false);
    const [confirmDeleteModelOpen, setConfirmDeleteModelOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<IUser | undefined>();
    const [filter, setFilter] = useState<Partial<IUserListQueryParams>>({
        roleType: EUserRole.Admin,
    });
    const createUser = useCreateUser();
    const updateUser = useUpdateUser();
    const deleteUser = useDeleteUser();

    const { data, isLoading, canNext, refresh, next } = useUserList(filter);

    const closeModel = () => {
        if (selectedUser) {
            setSelectedUser(undefined);
        }
        setCreateModelOpen(false);
    };

    const closeConfirmModel = () => {
        setConfirmDeleteModelOpen(false);
        setSelectedUser(undefined);
    };

    const updateFilter = (newValue: Partial<IUserListQueryParams>) => {
        setFilter({
            ...filter,
            ...newValue,
        });
    };

    const onSubmit = (data: Partial<INewUserParams>) => {
        setIsSubmitting(true);
        return Promise.resolve()
            .then(() => {
                if (selectedUser) {
                    return updateUser(selectedUser, data);
                }
                return createUser(data);
            })
            .then(() => {
                closeModel();
                refresh();
            })
            .finally(() => {
                setIsSubmitting(false);
            });
    };

    const onItemEdit = (row: IUser) => {
        setSelectedUser(row);
        setCreateModelOpen(true);
    };

    /* Delete admin */

    // const onItemDelete = (row: IUser) => {
    //     setSelectedUser(row);
    //     setConfirmDeleteModelOpen(true);
    // };

    return (
        <>
            <div
                className={flex({
                    flex: 1,
                    gap: '48px',
                    direction: 'column',
                })}
            >
                <Topbar
                    title={pageConfig[Slug.MANAGE_ADMIN].title}
                    onCreateClick={() => {
                        setCreateModelOpen(true);
                    }}
                    onSearch={(keyword) =>
                        updateFilter({ keyword, pageIndex: 1 })
                    }
                />
                <div className={flex({ flex: 1 })}>
                    <UserList
                        loading={isLoading}
                        // onItemDelete={onItemDelete}
                        onItemEdit={onItemEdit}
                        data={data?.records ?? []}
                        canLoadMore={canNext}
                        onLoadMore={next}
                    />
                </div>
            </div>
            <ConfirmModal
                title="Alert"
                confirmText="Delete"
                message={format(CommonMessages.DeletingAlert, 'user')}
                onCancel={() => {
                    if (isSubmitting) {
                        return;
                    }
                    closeConfirmModel();
                }}
                onConfirm={() => {
                    if (selectedUser) {
                        deleteUser(selectedUser).then(refresh);
                    }
                    closeConfirmModel();
                }}
                open={confirmDeleteModelOpen}
            />
            <Modal
                isOpen={createModelOpen}
                onClose={() => {
                    if (!isSubmitting) {
                        closeModel();
                    }
                }}
                title={selectedUser ? 'Update admin' : 'Create new admin'}
                sizes="sm"
            >
                <CreateNewForm
                    isLoading={isSubmitting}
                    onSubmit={onSubmit}
                    isEdit={!!selectedUser}
                    initValue={{
                        avatar: selectedUser?.avatar,
                        email: selectedUser?.email,
                        company: selectedUser?.company,
                        fullName: selectedUser?.fullName,
                        phoneNumber: selectedUser?.phoneNumber,
                    }}
                    formContainerVariant={{
                        container: 'table',
                    }}
                />
            </Modal>
        </>
    );
}
