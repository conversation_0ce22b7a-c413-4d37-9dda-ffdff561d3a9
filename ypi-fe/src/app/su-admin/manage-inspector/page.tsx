'use client';

import Topbar from '@/components/globals/Topbar';
import { Slug } from '@/utilities/types/enums/Slug';
import pageConfig from '@/utilities/page-config';
import { useState } from 'react';
import { IUserListQueryParams } from '@/repositories/User';
import Modal from '@/components/globals/Modal';
import CreateNewForm from '@/components/manage-inspector/CreateNewForm';
import { flex } from '@/styled-system/patterns';
import {
    useCreateUser,
    useDeleteUser,
    useUpdateUser,
    useUserList,
} from '@/hooks/user';
import {
    INewUserParams,
    EUserRole,
    IUser,
} from '@/utilities/types/entities/user';
import UserList from '@/components/manage-inspector/UserList';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';

export default function ManageInspector() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [createModelOpen, setCreateModelOpen] = useState(false);
    const [confirmDeleteModelOpen, setConfirmDeleteModelOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<IUser | undefined>();
    const [filter, setFilter] = useState<Partial<IUserListQueryParams>>({
        roleType: EUserRole.Inspector,
    });

    const { data, isLoading, refresh, next, canNext } = useUserList(filter);
    const createUser = useCreateUser();
    const updateUser = useUpdateUser();
    const deleteUser = useDeleteUser();

    const closeModel = () => {
        if (selectedUser) {
            setSelectedUser(undefined);
        }
        setCreateModelOpen(false);
    };

    const closeConfirmModel = () => {
        setConfirmDeleteModelOpen(false);
        setSelectedUser(undefined);
    };

    const updateFilter = (newValue: Partial<IUserListQueryParams>) => {
        setFilter({
            ...filter,
            ...newValue,
        });
    };

    const onSubmit = (data: Partial<INewUserParams>) => {
        setIsSubmitting(true);
        return Promise.resolve()
            .then(() => {
                if (selectedUser) {
                    return updateUser(selectedUser, data);
                }
                return createUser(data);
            })
            .then(() => {
                closeModel();
                refresh();
            })
            .finally(() => {
                setIsSubmitting(false);
            });
    };

    const onItemEdit = (row: IUser) => {
        setSelectedUser(row);
        setCreateModelOpen(true);
    };

    const onItemDelete = (row: IUser) => {
        setSelectedUser(row);
        setConfirmDeleteModelOpen(true);
    };

    return (
        <>
            <div
                className={flex({
                    flex: 1,
                    gap: '48px',
                    direction: 'column',
                })}
            >
                <Topbar
                    title={pageConfig[Slug.MANAGE_INSPECTOR].title}
                    onCreateClick={() => {
                        setCreateModelOpen(true);
                    }}
                    onSearch={(keyword) =>
                        updateFilter({ keyword, pageIndex: 1 })
                    }
                />
                <div className={flex({ flex: 1 })}>
                    <UserList
                        loading={isLoading}
                        onItemDelete={onItemDelete}
                        onItemEdit={onItemEdit}
                        data={data?.records ?? []}
                        canLoadMore={canNext}
                        onLoadMore={next}
                    />
                </div>
            </div>
            <ConfirmModal
                title="Alert"
                confirmText="Delete"
                message={format(CommonMessages.DeletingAlert, 'user')}
                onCancel={() => {
                    if (isSubmitting) {
                        return;
                    }
                    closeConfirmModel();
                }}
                onConfirm={() => {
                    if (selectedUser) {
                        deleteUser(selectedUser).then(refresh);
                    }
                    closeConfirmModel();
                }}
                open={confirmDeleteModelOpen}
            />
            <Modal
                isOpen={createModelOpen}
                onClose={() => {
                    if (!isSubmitting) {
                        closeModel();
                    }
                }}
                title={
                    selectedUser ? 'Update inspector' : 'Create new inspector'
                }
                sizes="sm"
            >
                <CreateNewForm
                    isLoading={isSubmitting}
                    onSubmit={onSubmit}
                    isEdit={!!selectedUser}
                    initValue={{
                        email: selectedUser?.email,
                        fullName: selectedUser?.fullName,
                    }}
                    formContainerVariant={{
                        container: 'table',
                    }}
                />
            </Modal>
        </>
    );
}
