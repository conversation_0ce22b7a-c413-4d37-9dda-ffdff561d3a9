import React from 'react';
import Sidebar from '@/components/globals/Sidebar';
import { Slug } from '@/utilities/types/enums/Slug';
import pageConfig from '@/utilities/page-config';
import { EUserRole } from '@/utilities/types/entities/user';
import { RedirectType, redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utilities/lib/auth';
import { RightLayout, SuperAdminLayout } from '@/components/globals/Layout';

interface Props {
    children: React.ReactNode;
}

export default async function Layout({ children }: Props) {
    const auth = await getServerSession(authOptions);
    if (auth?.user?.role !== EUserRole.SuperAdmin) {
        redirect('/', RedirectType.replace);
    }
    return (
        <SuperAdminLayout>
            <Sidebar
                items={[
                    {
                        path: Slug.MANAGE_ADMIN,
                        title: pageConfig[Slug.MANAGE_ADMIN].title ?? '',
                    },
                    {
                        path: Slug.MANAGE_INSPECTOR,
                        title: pageConfig[Slug.MANAGE_INSPECTOR].title ?? '',
                    },
                ]}
            />
            <RightLayout>{children}</RightLayout>
        </SuperAdminLayout>
    );
}
