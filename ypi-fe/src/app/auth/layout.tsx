'use client';
import React from 'react';
import {
    <PERSON>th<PERSON><PERSON><PERSON>,
    Auth<PERSON><PERSON>r<PERSON>rap<PERSON>,
    LogoAuthWrapper,
} from '@/app/auth/style';
import { Logo } from '@/components/globals/Icons';
import { useRedirectIfSignedIn } from '@/hooks/auth';

const AuthLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    useRedirectIfSignedIn();

    return (
        <div className={AuthContainerWrapper}>
            <div className={LogoAuthWrapper}>
                <Logo />
            </div>
            <div className={AuthContainer}>{children}</div>
        </div>
    );
};

export default AuthLayout;
