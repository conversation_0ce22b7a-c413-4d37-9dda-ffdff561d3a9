import { css } from '@/styled-system/css';
import { center } from '@/styled-system/patterns';

export const LogoAuthWrapper = css({
    position: 'absolute',
    top: '56px',
    left: '56px',
    zIndex: 10,
});

export const AuthContainerWrapper = css({
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    width: '100vw',
    bg: 'background.40',
});

export const AuthContainer = css({
    width: '464px',
    // height: '423px',
    bg: 'white',
    borderRadius: '20px',
    p: '48px',
    textAlign: 'center',
});

export const AuthForm = css({
    p: '48px 0',
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
});

export const errorTextStyle = center({
    color: 'warning.100 !important',
    padding: '8px 0',
});
