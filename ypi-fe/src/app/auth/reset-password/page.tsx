'use client';
import headingTag from '@/utilities/types/enums/Typography';
import Typography from '@/components/globals/Typography';
import { center } from '@/styled-system/patterns';
import { FieldValues } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ResetPasswordSchema } from '@/utilities/validations/authenication';
import { useRouter, useSearchParams } from 'next/navigation';
import { Slug } from '@/utilities/types/enums/Slug';
import { useEffect, useState } from 'react';
import { getAuthErrorMessage } from '@/utilities/messages/authenticate';
import { RequestError } from '@/utilities/types/request';
import { useNewPassword } from '@/hooks/auth';
import { toast } from '@/components/Toaster';
import InputForm from '@/components/globals/InputForm';
import { EFormInputType } from '@/utilities/types/form';

const ResetPasswordPage = () => {
    const query = useSearchParams();
    const router = useRouter();
    const token = query.get('token');
    const [checkingToken, setCheckingToken] = useState(true);
    const [loading, setLoading] = useState(false);

    const { newPassword, verifyToken } = useNewPassword();

    const gotoSignInPage = () => {
        router.push(Slug.SIGN_IN);
    };

    const onSubmit = (data: FieldValues) => {
        if (token) {
            setLoading(true);
            toast
                .promise(newPassword(token, data.password), {
                    loading: 'Resetting password',
                    error: (e) => e.message,
                    success: 'Password reset successful',
                })
                .then(gotoSignInPage)
                .catch(() => {})

                .finally(() => {
                    setLoading(false);
                });
        }
    };

    useEffect(() => {
        if (!token) {
            // navigate to sign in page if not have reset password token
            router.push(Slug.SIGN_IN);
            return;
        }
        verifyToken(token)
            .then((res) => {
                if (!res.data) {
                    // handled in catch statement
                    throw new Error('Verify token failed');
                }
                setCheckingToken(false);
            })
            .catch((e: RequestError) => {
                const [_, message] = getAuthErrorMessage(e);
                toast.error(message);

                // setError(field, { message });
                // navigate to sign in page if verify token failed
                gotoSignInPage();
            });
    }, [token, router]);

    if (checkingToken) {
        return <div>Loading...</div>;
    }

    return (
        <InputForm
            onSubmit={onSubmit}
            formHeader={
                <div
                    className={center({
                        mb: 4,
                    })}
                >
                    <Typography
                        typography="header_32"
                        tag={headingTag.h1}
                        color="primary_100"
                    >
                        Create new password
                    </Typography>
                </div>
            }
            resolver={zodResolver(ResetPasswordSchema)}
            disabled={loading}
            inputs={[
                {
                    id: 'new_password',
                    name: 'password',
                    label: '',
                    type: EFormInputType.Password,
                    placeholder: 'New password',
                    color: 'secondary',
                },
                {
                    id: 're_enter_password',
                    name: 'confirmPassword',
                    label: '',
                    type: EFormInputType.Password,
                    placeholder: 'Re-enter password',
                    color: 'secondary',
                },
            ]}
        />
    );
};

export default ResetPasswordPage;
