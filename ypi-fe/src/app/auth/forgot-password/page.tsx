'use client';
import Link from 'next/link';
import { center, circle } from '@/styled-system/patterns';
import { css } from '@/styled-system/css';
import headingTag from '@/utilities/types/enums/Typography';
import Typography from '@/components/globals/Typography';
import { Slug } from '@/utilities/types/enums/Slug';
import { FieldValues, UseFormSetError } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ForgotPasswordSchema } from '@/utilities/validations/authenication';
import { useState } from 'react';
import EmailSubbed from '@/components/globals/Icons/EmailSubbed';
import { getAuthErrorMessage } from '@/utilities/messages/authenticate';
import { useForgotPassword } from '@/hooks/auth';
import InputForm from '@/components/globals/InputForm';
import { EFormInputType } from '@/utilities/types/form';

const ForgotPasswordPage = () => {
    const [isEmailSent, setIsEmailSent] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const forgotPassword = useForgotPassword();

    const onSubmit = (
        data: FieldValues,
        setError: UseFormSetError<FieldValues>
    ) => {
        setIsLoading(true);
        forgotPassword(data.email)
            .then(() => {
                setIsEmailSent(true);
            })
            .catch((e) => {
                const [field, message] = getAuthErrorMessage(e);

                setError(field, { message });
            })
            .finally(() => {
                setIsLoading(false);
            });
    };

    if (isEmailSent) {
        return (
            <div
                className={center({
                    flexDirection: 'column',
                })}
            >
                <div
                    className={circle({
                        size: 80,
                        borderWidth: 2,
                        borderColor: 'primary.100',
                    })}
                >
                    <EmailSubbed />
                </div>
                <div className={css({ mt: 3, mb: '12px' })}>
                    <Typography
                        typography="header_24"
                        tag={headingTag.div}
                        color="primary_100"
                    >
                        Email sent successfully
                    </Typography>
                </div>
                <Typography typography="body_16" tag={headingTag.div}>
                    Please check your email to continue resetting your password
                </Typography>
            </div>
        );
    }

    return (
        <InputForm
            inputs={[
                {
                    id: 'email',
                    name: 'email',
                    label: '',
                    type: EFormInputType.Email,
                    placeholder: 'Email',
                    color: 'secondary',
                },
            ]}
            disabled={isLoading}
            resolver={zodResolver(ForgotPasswordSchema)}
            submitText="Send email"
            formFooter={
                <Typography
                    typography="body_14"
                    tag={headingTag.p}
                    color="secondary_60"
                    className={css({ mt: '12px' })}
                >
                    Back to
                    <Link
                        className={css({
                            color: 'primary.100',
                            fontWeight: 'semiBold',
                            marginLeft: '8px',
                        })}
                        href={Slug.SIGN_IN}
                    >
                        Sign in
                    </Link>
                </Typography>
            }
            onSubmit={onSubmit}
            formHeader={
                <>
                    <Typography
                        typography="header_32"
                        tag={headingTag.h1}
                        color="primary_100"
                    >
                        Forgot password
                    </Typography>
                    <div
                        className={center({
                            paddingTop: '12px',
                            marginBottom: '30px',
                        })}
                    >
                        <Typography
                            typography="body_16"
                            tag={headingTag.p}
                            color="secondary_60"
                        >
                            Please enter your email address to <br />
                            receive a verification
                        </Typography>
                    </div>
                </>
            }
        />
    );
};

export default ForgotPasswordPage;
