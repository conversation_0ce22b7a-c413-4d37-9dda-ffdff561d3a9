'use client';
import Link from 'next/link';
import { signIn } from 'next-auth/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { FieldValues, useForm } from 'react-hook-form';
import Typography from '@/components/globals/Typography';
import headingTag from '@/utilities/types/enums/Typography';
import { AuthForm, errorTextStyle } from '@/app/auth/style';
import Input from '@/components/globals/Input';
import Checkbox from '@/components/globals/Checkbox';
import { flex } from '@/styled-system/patterns';
import Button from '@/components/globals/Button';
import { Slug } from '@/utilities/types/enums/Slug';
import { signInSchema } from '@/utilities/validations/authenication';
import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { getAuthErrorMessage } from '@/utilities/messages/authenticate';

const SignInPage = () => {
    const {
        register,
        handleSubmit,
        formState: { errors },
        setError,
    } = useForm({
        resolver: zodResolver(signInSchema),
    });
    const [isRememberMe, setIsRememberMe] = useState(true);
    const [loading, setLoading] = useState(false);
    const searchParams = useSearchParams();
    const callbackUrl = searchParams.get('callbackUrl') || '/';

    const onSubmit = (data: FieldValues) => {
        setLoading(true);
        signIn('credentials', {
            email: data.email,
            password: data.password,
            redirect: false,
            callbackUrl,
        })
            .then((res) => {
                if (!res?.error) {
                    return;
                }
                throw res;
            })
            .catch((e) => {
                const [field, message] = getAuthErrorMessage(e);
                setError(field, { message });
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <Typography
                typography="header_32"
                tag={headingTag.h1}
                color="primary_100"
            >
                Sign In
            </Typography>
            <div className={AuthForm}>
                <Input
                    type="text"
                    placeholder="Email"
                    id="email"
                    autoComplete="off"
                    color="secondary"
                    error={!!errors.email?.message}
                    helperText={errors.email?.message as string}
                    {...register('email')}
                />
                <Input
                    type="password"
                    placeholder="Password"
                    id="password"
                    color="secondary"
                    autoComplete="off"
                    error={!!errors.password?.message}
                    helperText={errors.password?.message as string}
                    {...register('password')}
                />
                <div
                    className={flex({
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    })}
                >
                    <Checkbox
                        size="small"
                        label="Remember me"
                        isChecked={isRememberMe}
                        onChange={setIsRememberMe}
                    />
                    <Link href={Slug.FORGOT_PASSWORD}>
                        <Typography
                            typography="subtitle_14"
                            tag={headingTag.p}
                            color="primary_100"
                        >
                            Forgot password
                        </Typography>
                    </Link>
                </div>
            </div>
            {errors.default ? (
                <div className={errorTextStyle}>
                    {errors.default.message?.toString()}
                </div>
            ) : null}
            <div className={flex({ justifyContent: 'center' })}>
                <Button
                    size="large"
                    type={'submit'}
                    visual="solid_primary"
                    disabled={loading}
                >
                    Sign In
                </Button>
            </div>
        </form>
    );
};

export default SignInPage;
