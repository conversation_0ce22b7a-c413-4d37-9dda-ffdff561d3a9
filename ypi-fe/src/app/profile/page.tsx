import { ProfileContainer } from '@/components/profile/ProfileContainer';
import { authOptions } from '@/utilities/lib/auth';
import { EUserRole } from '@/utilities/types/entities/user';
import { Slug } from '@/utilities/types/enums/Slug';
import { getServerSession } from 'next-auth';
import { redirect, RedirectType } from 'next/navigation';

export default async function Profile() {
    const auth = await getServerSession(authOptions);

    switch (auth?.user?.role) {
        case EUserRole.Admin: {
            redirect(Slug.ADMIN_PROFILE, RedirectType.replace);
        }
        case EUserRole.SuperAdmin: {
            redirect(Slug.SU_ADMIN_PROFILE, RedirectType.replace);
        }
    }

    return (
        <section>
            <ProfileContainer />
        </section>
    );
}
