import Sidebar from '@/components/globals/Sidebar';
import React from 'react';
import { flex } from '@/styled-system/patterns';
import SideBarItem from '@/utilities/types/side-bar-item';
import { getSideBarItemFromSlug } from '@/utilities/helpers/side-bar';
import { Slug } from '@/utilities/types/enums/Slug';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utilities/lib/auth';
import { EUserRole } from '@/utilities/types/entities/user';
import { RedirectType, redirect } from 'next/navigation';

interface Props {
    children: React.ReactNode;
}

const sideBarItems: SideBarItem[] = [
    getSideBarItemFromSlug(Slug.MANAGE_CUSTOMERS),
    getSideBarItemFromSlug(Slug.GENERAL_REPORT),
    getSideBarItemFromSlug(Slug.MANAGE_STAFF),
    getSideBarItemFromSlug(Slug.CALENDAR),
    getSideBarItemFromSlug(Slug.REMINDERS),
];

export default async function Layout({ children }: Props) {
    const auth = await getServerSession(authOptions);

    if (auth?.user?.role !== EUserRole.Admin) {
        redirect('/', RedirectType.replace);
    }
    return (
        <div
            className={flex({
                minHeight: '100vh',
            })}
        >
            <Sidebar items={sideBarItems} />
            <div
                className={flex({
                    direction: 'column',
                    flex: 1,
                })}
            >
                {children}
            </div>
        </div>
    );
}
