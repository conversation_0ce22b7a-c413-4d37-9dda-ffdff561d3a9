import MaterialRepository from '@/repositories/Material';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export async function GET(req: Request) {
    return MaterialRepository.getList()
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
