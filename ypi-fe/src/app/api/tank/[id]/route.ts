import { NextResponse } from 'next/server';
import queryString from 'query-string';
import { RequestError } from '@/utilities/types/request';
import TankRepository from '@/repositories/Tank';

export async function GET(
    req: Request,
    { params }: { params: { id: string } }
) {
    const { query } = queryString.parseUrl(req.url);
    return TankRepository.getById(
        params.id,
        (query?.assignTaskId || '') as string
    )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function PUT(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => TankRepository.update(context.params.id, body))
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function DELETE(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return TankRepository.deleteById(context.params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
