import { NextResponse } from 'next/server';
import { IPaginationParams, RequestError } from '@/utilities/types/request';
import TankRepository from '@/repositories/Tank';
import queryString from 'query-string';

export async function GET(
    req: Request,
    { params }: { params: { id: string } }
) {
    const { query } = queryString.parseUrl(req.url);
    return TankRepository.getCleanDisinfection(
        params.id,
        query as Partial<IPaginationParams>
    )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
