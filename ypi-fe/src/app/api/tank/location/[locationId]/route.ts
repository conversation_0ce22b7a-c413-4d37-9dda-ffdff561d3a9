import { NextResponse } from 'next/server';
import queryString from 'query-string';
import { IPaginationParams, RequestError } from '@/utilities/types/request';
import TankRepository from '@/repositories/Tank';

export async function GET(
    req: Request,
    {
        params,
    }: {
        params: { locationId: string };
    }
) {
    const { query } = queryString.parseUrl(req.url);

    return TankRepository.getList(
        params.locationId,
        query as Partial<IPaginationParams>
    )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function POST(
    req: Request,
    {
        params,
    }: {
        params: { locationId: string };
    }
) {
    return req
        .json()
        .then((body) => {
            return TankRepository.create(params.locationId, body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
