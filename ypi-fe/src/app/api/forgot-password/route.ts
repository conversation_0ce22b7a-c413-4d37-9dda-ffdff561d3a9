import AuthRepository from '@/repositories/Auth';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export function POST(req: Request) {
    return req
        .json()
        .then(({ email }) => {
            return AuthRepository.forgotPassword(email);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
