import AuthRepository from '@/repositories/Auth';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';
import queryString from 'query-string';

export function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);
    const token = query.token;

    if (typeof token !== 'string') {
        return NextResponse.json(
            {
                statusCode: 400,
                data: false,
            },
            { status: 400 }
        );
    }

    return AuthRepository.verifyForgotPasswordToken(token)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
