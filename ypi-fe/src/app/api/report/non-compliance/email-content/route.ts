import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';
import GeneralReportRepository from '@/repositories/GeneralReport';
import { generatePdfFromStringHtml } from '@/utilities/globals/function/generatePDF.server';
export async function POST(req: Request) {
    return req
        .json()
        .then((body) => {
            return GeneralReportRepository.preInspectionDownloadEmailContent(
                body
            );
        })
        .then(async (res) => {
            const html = res.data;

            if (!html) {
                throw new Error('Invalid HTML response');
            }
            const pdf = await generatePdfFromStringHtml(html);

            if (!pdf) {
                throw new Error('Generate PDF failed!');
            }
            return new Response(pdf, {
                status: 200,
                headers: {
                    'Content-Type': 'application/pdf',
                    'Content-Disposition':
                        'attachment; filename="generated.pdf"',
                },
            });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
