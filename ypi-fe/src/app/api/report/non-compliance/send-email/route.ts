import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';
import GeneralReportRepository from '@/repositories/GeneralReport';

export async function POST(req: Request) {
    return req
        .json()
        .then((body) => {
            return GeneralReportRepository.nonComplianceSendEmail(body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
