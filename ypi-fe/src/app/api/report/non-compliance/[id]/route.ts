import { NextResponse } from 'next/server';
import { RequestError } from '@/utilities/types/request';
import GeneralReportRepository from '@/repositories/GeneralReport';

export async function GET(
    req: Request,
    { params }: { params: { id: string } }
) {
    return GeneralReportRepository.getNonComplianceById(params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
