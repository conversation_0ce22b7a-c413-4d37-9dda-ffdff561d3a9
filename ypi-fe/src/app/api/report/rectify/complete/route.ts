import { NextResponse } from 'next/server';
import queryString from 'query-string';
import GeneralReportRepository from '@/repositories/GeneralReport';
import { IPaginationParams, RequestError } from '@/utilities/types/request';

export async function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);

    return GeneralReportRepository.getListComplete(
        query as Partial<IPaginationParams>
    )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
