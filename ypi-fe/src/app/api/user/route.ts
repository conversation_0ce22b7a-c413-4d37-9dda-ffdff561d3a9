import UserRepository, { IUserListQueryParams } from '@/repositories/User';
import { NextResponse } from 'next/server';
import queryString from 'query-string';
import { RequestError } from '@/utilities/types/request';

export async function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);

    return UserRepository.getList(query as Partial<IUserListQueryParams>)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function POST(req: Request) {
    return req
        .json()
        .then((body) => {
            return UserRepository.create(body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
