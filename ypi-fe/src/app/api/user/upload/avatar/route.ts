import UserRepository from '@/repositories/User';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData();
        if (!formData) throw new Error();
        const res = await UserRepository.uploadAvatar(formData);
        return NextResponse.json(res);
    } catch (error) {
        return NextResponse.json({ success: false });
    }
}
