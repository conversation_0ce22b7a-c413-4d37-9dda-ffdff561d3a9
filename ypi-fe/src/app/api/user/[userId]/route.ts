import UserRepository from '@/repositories/User';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export async function DELETE(
    req: Request,
    context: {
        params: { userId: string };
    }
) {
    return UserRepository.deleteById(context.params.userId)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function PUT(
    req: Request,
    context: {
        params: { userId: string };
    }
) {
    return req
        .json()
        .then((body) => UserRepository.update(context.params.userId, body))
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
