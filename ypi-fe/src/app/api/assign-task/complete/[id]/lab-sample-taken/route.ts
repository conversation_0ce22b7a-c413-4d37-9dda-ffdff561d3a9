import AssignTaskRepositories from '@/repositories/AssignTask';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export async function PUT(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => {
            return AssignTaskRepositories.updateTakenSampleDate(
                context.params.id,
                body
            );
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
