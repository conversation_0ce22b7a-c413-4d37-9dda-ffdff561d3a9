import { NextResponse } from 'next/server';
import AssignTaskRepositories from '@/repositories/AssignTask';
import { RequestError } from '@/utilities/types/request';

export async function POST(req: Request) {
    return req
        .json()
        .then((body) => {
            return AssignTaskRepositories.taskConfirm(body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
