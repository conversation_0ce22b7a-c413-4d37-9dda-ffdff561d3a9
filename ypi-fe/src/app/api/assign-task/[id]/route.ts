import { NextResponse } from 'next/server';
import { RequestError } from '@/utilities/types/request';
import TankRepository from '@/repositories/Tank';
import AssignTaskRepositories from '@/repositories/AssignTask';

export async function PUT(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) =>
            TankRepository.updateAssignTank(context.params.id, body)
        )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function DELETE(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return AssignTaskRepositories.deleteById(context.params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
