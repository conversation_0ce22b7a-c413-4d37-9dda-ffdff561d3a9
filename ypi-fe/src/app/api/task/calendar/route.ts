import { NextResponse } from 'next/server';
import queryString from 'query-string';
import { IPaginationParams, RequestError } from '@/utilities/types/request';
import TaskRepository from '@/repositories/Task';

export async function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);

    return TaskRepository.getCalendarTask(query as Partial<IPaginationParams>)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
