import { NextResponse } from "next/server";
import { RequestError } from "@/utilities/types/request";
import DefectRepository from "@/repositories/Defects";

export async function POST(req: Request) {
    try {
        const body = await req.json();
        const response = await DefectRepository.create(body);
        return NextResponse.json(response, { status: response.status });
    } catch (error) {
        const e = error as RequestError;
        return NextResponse.json(
            { message: e.message || 'An error occurred' },
            { status: e.statusCode || 500 }
        );
    }
}