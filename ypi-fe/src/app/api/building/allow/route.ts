import { NextResponse } from 'next/server';
import { RequestError } from '@/utilities/types/request';
import BuildingRepository from '@/repositories/Building';

export async function GET() {
    return BuildingRepository.getList()
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
