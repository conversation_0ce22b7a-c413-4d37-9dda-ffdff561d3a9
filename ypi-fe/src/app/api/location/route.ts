import LocationRepository from '@/repositories/Location';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';
import queryString from 'query-string';

export async function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);
    return NextResponse.json({
        status: 200,
        data: {
            currentPage: 1,
            totalItem: 9,
            totalPages: 1,
            records: [
                {
                    block_no: '11',
                    building: 'AIA Tower',
                    id: '1',
                    postal_code: '520147',
                    street: 'Marlow Street',
                },
                {
                    block_no: '11',
                    building: 'AIA Tower',
                    id: '2',
                    postal_code: '520147',
                    street: 'Marlow Street',
                },
                {
                    block_no: '11',
                    building: 'AIA Tower',
                    id: '3',
                    postal_code: '520147',
                    street: 'Marlow Street',
                },
                {
                    block_no: '11',
                    building: 'AIA Tower',
                    id: '4',
                    postal_code: '520147',
                    street: 'Marlow Street',
                },
            ],
        },
    });
    // return CustomerRepository.getList(query as Partial<IPaginationParams>)
    //     .then((res) => {
    //         return NextResponse.json(res, { status: res.status });
    //     })
    //     .catch((e: RequestError) => {
    //         return NextResponse.json(e, { status: e.statusCode });
    //     });
}

export async function POST(
    req: Request,
    {
        params,
    }: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => {
            return LocationRepository.create(params.id, body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
