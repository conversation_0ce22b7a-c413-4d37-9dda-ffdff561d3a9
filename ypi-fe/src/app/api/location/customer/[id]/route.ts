import { NextResponse } from 'next/server';
import queryString from 'query-string';
import { IPaginationParams, RequestError } from '@/utilities/types/request';
import LocationRepository from '@/repositories/Location';

export async function GET(
    req: Request,
    {
        params,
    }: {
        params: { id: string };
    }
) {
    const { query } = queryString.parseUrl(req.url);

    return LocationRepository.getList(
        params.id,
        query as Partial<IPaginationParams>
    )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function POST(
    req: Request,
    {
        params,
    }: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => {
            return LocationRepository.create(params.id, body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
