import { NextResponse } from 'next/server';
// import queryString from 'query-string';
import { RequestError } from '@/utilities/types/request';
import LocationRepository from '@/repositories/Location';

export async function GET(
    req: Request,
    { params }: { params: { id: string } }
) {
    return LocationRepository.getById(params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function PUT(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => LocationRepository.update(context.params.id, body))
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function DELETE(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return LocationRepository.deleteById(context.params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
