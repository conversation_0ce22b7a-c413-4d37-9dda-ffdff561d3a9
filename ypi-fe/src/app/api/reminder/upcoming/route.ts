import ReminderRepository from '@/repositories/Reminder';
import { IPaginationParams, RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';
import queryString from 'query-string';

export async function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);

    return ReminderRepository.getListUpcoming(
        query as Partial<IPaginationParams>
    )
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
