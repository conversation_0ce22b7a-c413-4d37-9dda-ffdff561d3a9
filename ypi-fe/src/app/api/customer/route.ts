import { NextResponse } from 'next/server';
import queryString from 'query-string';
import { IPaginationParams, RequestError } from '@/utilities/types/request';
import CustomerRepository from '@/repositories/Customer';

export async function GET(req: Request) {
    const { query } = queryString.parseUrl(req.url);

    return CustomerRepository.getList(query as Partial<IPaginationParams>)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function POST(req: Request) {
    return req
        .json()
        .then((body) => {
            return CustomerRepository.create(body);
        })
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
