import CustomerRepository from '@/repositories/Customer';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export async function PUT(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => CustomerRepository.activate(context.params.id, body))
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
