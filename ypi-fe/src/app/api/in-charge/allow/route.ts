import { NextResponse } from 'next/server';
import { RequestError } from '@/utilities/types/request';
import InChargeRepository from '@/repositories/InCharge';

export async function GET() {
    return InChargeRepository.getList()
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
