import AuthRepository from '@/repositories/Auth';
import { RequestError } from '@/utilities/types/request';
import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';

const ErrorMess = 'Unauthorize';
export async function GET(req: NextRequest) {
    try {
        const token = await getToken({
            req,
            secret: process.env.NEXTAUTH_SECRET,
        });

        if (!token?.tokens?.accessToken) throw new Error(ErrorMess);
        const res = await AuthRepository.getProfileMe(token.tokens.accessToken);
        return NextResponse.json(res, { status: res.status });
    } catch (error) {
        return NextResponse.json(error);
    }
}

export async function PUT(req: Request) {
    return req
        .json()
        .then((body) => AuthRepository.updateProfileMe(body))
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
