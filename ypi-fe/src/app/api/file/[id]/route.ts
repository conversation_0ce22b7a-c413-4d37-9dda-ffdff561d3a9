import UploadRepository from '@/repositories/Upload';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export async function DELETE(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return UploadRepository.deleteById(context.params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
