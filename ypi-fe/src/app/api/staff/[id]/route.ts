import StaffRepository from '@/repositories/Staff';
import { RequestError } from '@/utilities/types/request';
import { NextResponse } from 'next/server';

export async function GET(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return StaffRepository.getById(context.params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function DELETE(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return StaffRepository.deleteById(context.params.id)
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}

export async function PUT(
    req: Request,
    context: {
        params: { id: string };
    }
) {
    return req
        .json()
        .then((body) => StaffRepository.update(context.params.id, body))
        .then((res) => {
            return NextResponse.json(res, { status: res.status });
        })
        .catch((e: RequestError) => {
            return NextResponse.json(e, { status: e.statusCode });
        });
}
