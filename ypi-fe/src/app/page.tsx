import { authOptions } from '@/utilities/lib/auth';
import { EUserRole } from '@/utilities/types/entities/user';
import { getServerSession } from 'next-auth';
import { notFound, redirect } from 'next/navigation';

export default async function Home() {
    const auth = await getServerSession(authOptions);

    switch (auth?.user?.role) {
        case EUserRole.SuperAdmin: {
            return redirect('/su-admin');
        }
        case EUserRole.Admin: {
            return redirect('/admin');
        }
        default: {
            return notFound();
        }
    }
}
