import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import * as React from 'react';

export interface PrivacyPolicyProps {}

export default function PrivacyPolicy(props: PrivacyPolicyProps) {
    return (
        <div
            className={css({
                minW: '1280px',
                margin: '0 auto',
                padding: '24px',
                fontSize: '16px',
                lineHeight: '1.5',
            })}
        >
            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_24"
            >
                Privacy Policy - YPIWATER PTE LTD
            </Typography>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                Introduction
            </Typography>
            <p>
                YPI (“the App”) is an internal administrative platform designed
                to consolidate and centralize official documents, photographs,
                and reports. This privacy policy outlines how we collect, use,
                and protect personal data within the App.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                1. Information We Collect
            </Typography>
            <p>The App collects the following types of personal information:</p>
            <ul>
                <li>
                    <strong>Name and Phone Number:</strong> Collected to edit
                    user information and personalize user experience.
                </li>
                <li>
                    <strong>Location Data:</strong> Collected to support certain
                    features of the App that require location access.
                </li>
            </ul>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                2. Use of Personal Data
            </Typography>
            <p>
                The personal data we collect is used for the following purposes:
            </p>
            <ul>
                <li>To manage and update user profiles within the App.</li>
                <li>
                    To enable specific features that rely on location data for
                    better functionality.
                </li>
                <li>
                    To ensure the App operates effectively for internal
                    administrative purposes.
                </li>
            </ul>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                3. Third-Party Services
            </Typography>
            <p>
                The App may use third-party services for certain functions
                (e.g., analytics or hosting). These third-party services may
                collect information about your device and how you use the App.
                This data will only be shared with third parties when necessary
                for the operation and improvement of the App.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                4. Data Sharing
            </Typography>
            <p>
                We do not sell or share your personal data with external parties
                for marketing or advertising purposes. Data may be shared within
                the organization only for internal use related to administrative
                tasks.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                5. User Rights
            </Typography>
            <p>
                Users have the following rights regarding their personal data:
            </p>
            <ul>
                <li>
                    <strong>Access:</strong> You can request to access the
                    personal information we hold about you.
                </li>
                <li>
                    <strong>Correction:</strong> You can request that we correct
                    any inaccurate or incomplete information.
                </li>
                <li>
                    <strong>Deletion:</strong> You may request that we delete
                    your data, subject to organizational retention policies.
                </li>
            </ul>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                6. Data Retention
            </Typography>
            <p>
                We retain personal data for as long as it is necessary to
                fulfill the purposes for which it was collected, or as required
                by law.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                7. Security
            </Typography>
            <p>
                We take reasonable measures to protect your personal information
                from unauthorized access, disclosure, alteration, or
                destruction. However, no security measures are 100% effective,
                and we cannot guarantee absolute protection.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                8. Children’s Privacy
            </Typography>
            <p>
                The App is not intended for use by children under the age of 13.
                We do not knowingly collect personal information from children.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                9. Changes to This Privacy Policy
            </Typography>
            <p>
                We may update this privacy policy from time to time. Any changes
                will be posted within the App and will become effective when
                posted.
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_20"
            >
                10. Contact Us
            </Typography>
            <p>
                If you have any questions or concerns about this privacy policy,
                please contact us at{' '}
                <a
                    href="https://carelaplus.com"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    https://carelaplus.com
                </a>
            </p>

            <Typography
                className={css({ textTransform: 'uppercase' })}
                color="primary_100"
                typography="header_16"
            >
                Last updated: October 7, 2024
            </Typography>
        </div>
    );
}
