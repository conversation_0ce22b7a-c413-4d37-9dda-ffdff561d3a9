'use client';

import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { useMemo, useState } from 'react';
import ScrollView from '@/components/globals/ScrollView';
import { useReminders } from '@/hooks/reminder';
import { IAssignTask } from '@/utilities/types/entities/task';
import { EReminder, EReminderType } from '@/utilities/types/enums/Remimder';
import ReminderList from '@/components/reminders/ReminderList';
import Tabs from '@/components/globals/Tabs';
import { OBJECT } from 'swr/_internal';
import { REMINDER_TYPE_LABELS } from '@/constant/reminder';

export default function Reminder() {
    const [currentTab, setCurrentTab] = useState<EReminderType>(
        EReminderType['UPCOMING']
    );

    const filter = useMemo(() => {
        return {
            pageIndex: 1,
        };
    }, [currentTab]);

    const { data, isLoading, next, canNext } = useReminders<IAssignTask>(
        currentTab === EReminderType['UPCOMING']
            ? EReminder.UPCOMING
            : EReminder.PAST,
        filter
    );

    return (
        <div
            className={flex({
                flex: 1,
                direction: 'column',
                gap: 6,
            })}
        >
            <div
                className={flex({
                    justifyContent: 'space-between',
                    alignItems: 'center',
                })}
            >
                <div className={css({ flex: 1 })}>
                    <Typography
                        color="primary_100"
                        typography="header_24"
                        tag={ETypographyTag.h1}
                        className={css({ textTransform: 'uppercase' })}
                    >
                        REMINDERS
                    </Typography>
                </div>
            </div>
            <div
                className={flex({
                    flex: 1,
                    direction: 'column',
                    gap: 3,
                })}
            >
                <Tabs
                    tabs={OBJECT.keys(REMINDER_TYPE_LABELS).map((key) => ({
                        key: key,
                        title: REMINDER_TYPE_LABELS?.[key as EReminderType]
                            ?.label,
                    }))}
                    selectedTabKey={currentTab}
                    onChange={(key) => setCurrentTab(key as EReminderType)}
                />
                <ScrollView
                    className={flex({
                        flex: 1,
                        direction: 'column',
                    })}
                >
                    <ReminderList
                        data={data?.records ?? []}
                        loading={isLoading}
                        canLoadMore={canNext}
                        onLoadMore={next}
                    />
                </ScrollView>
            </div>
        </div>
    );
}
