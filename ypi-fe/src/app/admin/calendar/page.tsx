'use client';
import AppCalendar from '@/components/globals/BigCalendar/AppCalendar';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import pageConfig from '@/utilities/page-config';
import { Slug } from '@/utilities/types/enums/Slug';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { useRef } from 'react';
import { Views } from 'react-big-calendar';
import { containerWrapper } from './styles';
import RangeDate from '@/components/calendar/FilterTools/RangeDate';
import { useCalendarTask } from '@/hooks/task';
import { convertTaskToEventItem } from '@/utilities/helpers/task';
import { FORMAT } from '@/configs/date';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { CalendarContext } from '@/components/globals/BigCalendar/AppCalendar/context';

dayjs.updateLocale('en', {
    weekStart: 1, // start from Monday ( 0 = Sunday -> 6 = Saturday)
});
export default function Calendar() {
    const searchParams = useSearchParams();
    const router = useRouter();

    const selectedDate = dayjs(searchParams.get('date')).isValid()
        ? dayjs(searchParams.get('date')).toDate()
        : new Date();

    const startOfSelectDate = dayjs(selectedDate)
        .startOf('week')
        .format(FORMAT.DATE_TIME_FULL);
    const endOfSelectDate = dayjs(selectedDate)
        .endOf('week')
        .format(FORMAT.DATE_TIME_FULL);

    const calendarRef = useRef(null);

    const { data: tasks, mutate: refreshTasks } = useCalendarTask(
        startOfSelectDate,
        endOfSelectDate
    );

    const memoizedResources = useMemo(
        () => ({
            onRefetch: refreshTasks,
        }),
        [refreshTasks]
    );

    return (
        <CalendarContext.Provider value={memoizedResources}>
            <div className={containerWrapper}>
                <div
                    className={flex({
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 6,
                    })}
                >
                    <Typography
                        className={css({ textTransform: 'uppercase' })}
                        color="primary_100"
                        typography="header_24"
                    >
                        {pageConfig[Slug.CALENDAR].title}
                    </Typography>

                    <RangeDate
                        date={selectedDate}
                        onChangeDate={(date) => {
                            if (date) {
                                router.push(
                                    `${Slug.CALENDAR}?date=${dayjs(date).format(
                                        'YYYY-MM-DD'
                                    )}`
                                );
                            }
                        }}
                        viewCalendar={Views.WEEK}
                    />
                </div>

                <div
                    className={css({
                        h: 'calc(100vh - 52px - 68px)',
                        overflow: 'auto',
                        maxW: '100%',
                    })}
                >
                    <AppCalendar
                        defaultDate={selectedDate}
                        date={selectedDate}
                        ref={calendarRef}
                        defaultView={Views.WEEK}
                        dayLayoutAlgorithm="no-overlap"
                        views={[Views.WEEK]}
                        view={Views.WEEK}
                        events={convertTaskToEventItem(tasks || [])}
                        toolbar={false}
                    />
                </div>
            </div>
        </CalendarContext.Provider>
    );
}
