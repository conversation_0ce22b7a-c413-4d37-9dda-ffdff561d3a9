'use client';

import Typography from '@/components/globals/Typography';
import NewCustomerForm from '@/components/manage-customers/NewCustomerForm';
import { useCreateCustomer } from '@/hooks/customer';
import { css } from '@/styled-system/css';
import pageConfig from '@/utilities/page-config';
import { INewCustomerParams } from '@/utilities/types/entities/customer';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
// import { FieldValues, UseFormSetError } from 'react-hook-form';

export default function CreateCustomPage() {
    const createCustomer = useCreateCustomer();
    const [loading, setLoading] = useState(false);
    const router = useRouter();

    const onSubmit = (
        data: Partial<INewCustomerParams>
        // setError: UseFormSetError<FieldValues>
    ) => {
        setLoading(true);
        createCustomer(data)
            .then(() => {
                router.push(Slug.MANAGE_CUSTOMERS);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <div
            className={css({
                pt: 4,
                ml: 6,
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            })}
        >
            <Typography
                className={css({ textTransform: 'uppercase', mb: 2 })}
                color="primary_100"
                typography="header_24"
            >
                {pageConfig[Slug.CREATE_CUSTOMER].title}
            </Typography>

            <NewCustomerForm disabled={loading} onSubmit={onSubmit} />
        </div>
    );
}
