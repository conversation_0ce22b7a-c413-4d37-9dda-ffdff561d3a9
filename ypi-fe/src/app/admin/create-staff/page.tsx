'use client';

import Typography from '@/components/globals/Typography';
import ManageStaffForm from '@/components/manage-staff/ManageStaffForm';
import { useCreateStaff } from '@/hooks/staff';
import { css } from '@/styled-system/css';
import CommonMessages from '@/utilities/messages/common';
import pageConfig from '@/utilities/page-config';
import { INewStaffParams } from '@/utilities/types/entities/staff';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter } from 'next/navigation';
import { FieldValues, UseFormSetError } from 'react-hook-form';
import toast from 'react-hot-toast';

export default function CreateStaffPage() {
    const { trigger, isMutating } = useCreateStaff();
    const router = useRouter();
    const onSubmit = async (
        data: INewStaffParams,
        setError: UseFormSetError<FieldValues>
    ) => {
        toast
            .promise(
                trigger(data).then((res) => {
                    if (res.statusCode === 400) {
                        return Promise.reject(res);
                    }

                    return Promise.resolve(res);
                }),
                {
                    loading: CommonMessages.Creating,
                    success: CommonMessages.SendEmailSuccessful,
                    error: (err) => err.msg || CommonMessages.CreatedFailed,
                }
            )
            .then((res) => {
                router.push(Slug.MANAGE_STAFF);
            });
    };

    return (
        <div
            className={css({
                pt: 4,
                ml: 6,
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            })}
        >
            <Typography
                className={css({ textTransform: 'uppercase', mb: 2 })}
                color="primary_100"
                typography="header_24"
            >
                {pageConfig[Slug.CREATE_STAFF].title}
            </Typography>

            <ManageStaffForm
                disabled={isMutating}
                onSubmit={onSubmit}
                isActive
            />
        </div>
    );
}
