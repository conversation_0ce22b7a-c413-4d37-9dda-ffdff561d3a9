'use client';

import Drawer from '@/components/globals/Drawer';
import ScrollView from '@/components/globals/ScrollView';
import Topbar from '@/components/globals/Topbar';
import Filter from '@/components/manage-staff/Filter';
import StaffList from '@/components/manage-staff/StaffList';
import { useStaffList } from '@/hooks/staff';
import { flex } from '@/styled-system/patterns';
import pageConfig from '@/utilities/page-config';
import { staffOptions } from '@/utilities/types/entities/staff';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// TODO: refactor structure + fix ts
type TFilterStaff = { keyword?: string; 'position[]'?: string[] };
export default function ManageStaff() {
    const router = useRouter();
    const [filterOpen, setFilterOpen] = useState(false);
    const [filterPosition, setFilterPosition] = useState<
        Record<string, boolean>
    >({});

    const [filter, setFilter] = useState<TFilterStaff>({});

    const { data, isLoading, canNext, next } = useStaffList(filter);

    const updateFilter = (newValue: TFilterStaff) => {
        setFilter({
            ...filter,
            ...newValue,
        });
    };

    return (
        <div
            className={flex({
                flex: 1,
                direction: 'column',
            })}
        >
            <Drawer
                title="Filter"
                open={filterOpen}
                onClose={() => {
                    setFilterOpen(false);
                    setFilter({
                        ...filter,
                        'position[]': Object.keys(filterPosition).filter(
                            (key) => filterPosition[key]
                        ),
                    });
                }}
            >
                <Filter
                    options={staffOptions ?? []}
                    valueKey="id"
                    value={filterPosition}
                    onChange={(key, v) => {
                        setFilterPosition({
                            ...filterPosition,
                            [key]: v,
                        });
                    }}
                />
            </Drawer>
            <Topbar
                title={pageConfig[Slug.MANAGE_STAFF].title}
                filterButtonVisible
                onFilterClick={() => {
                    setFilterOpen(true);
                }}
                onSearch={(keyword) => updateFilter({ keyword })}
                onCreateClick={() => router.push(Slug.CREATE_STAFF)}
            />
            <ScrollView
                className={flex({
                    pt: 6,
                    flex: 1,
                    direction: 'column',
                })}
            >
                <StaffList
                    data={data?.records ?? []}
                    loading={isLoading}
                    canLoadMore={canNext}
                    onLoadMore={next}
                />
            </ScrollView>
        </div>
    );
}
