'use client';

import Button from '@/components/globals/Button';
import { ButtonBack } from '@/components/globals/Button/ButtonBack';
import ManageStaffForm from '@/components/manage-staff/ManageStaffForm';
import { useActivateStaff, useStaffById, useUpdateStaff } from '@/hooks/staff';
import { css } from '@/styled-system/css';
import { INewStaffParams } from '@/utilities/types/entities/staff';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter } from 'next/navigation';
import { FieldValues, UseFormSetError } from 'react-hook-form';
import toast from 'react-hot-toast';
import { formHeaderStyle } from './style';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';
import { flex } from '@/styled-system/patterns';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { useState } from 'react';

export default function EditStaffPage({ params }: { params: { id: string } }) {
    const { data: userData, mutate: refetchStaff } = useStaffById(params.id);
    const { trigger, isMutating } = useUpdateStaff(params.id);
    const [activateStaffModalOpen, setActivateStaffModalOpen] = useState(false);
    const router = useRouter();
    const onSubmit = (
        data: INewStaffParams,
        setError: UseFormSetError<FieldValues>
    ) => {
        toast
            .promise(trigger(data), {
                loading: CommonMessages.Updating,
                success: CommonMessages.UpdatedSuccessful,
                error: (err) => err.message,
            })
            .then(() => {
                router.push(Slug.MANAGE_STAFF);
            });
    };

    const activateStaff = useActivateStaff();

    const onActivateStaff = () => {
        if (userData) {
            activateStaff(userData?.id || '', {
                isActive: !userData.isActive,
            }).then(() => {
                setActivateStaffModalOpen(false);
                refetchStaff();
                router.replace(Slug.MANAGE_STAFF);
            });
        }
    };

    return (
        <>
            <ManageStaffForm
                renderHeader={({ onUpdate }: any) => (
                    <div className={formHeaderStyle}>
                        <div className={css({ flex: 1 })}>
                            <ButtonBack label={userData?.fullName || ''} />
                        </div>
                        <div className={flex({ gap: '12px', pt: 2 })}>
                            <Button
                                type="button"
                                visual={
                                    userData?.isActive
                                        ? 'solid_inactive'
                                        : 'solid_active'
                                }
                                onClick={() => {
                                    if (userData)
                                        setActivateStaffModalOpen(true);
                                }}
                                className={css({ flex: 1 })}
                            >
                                {userData?.isActive ? 'Inactive' : 'Active'}
                            </Button>
                            <Button
                                onClick={onUpdate}
                                size="medium"
                                type="button"
                                visual={'solid_primary'}
                                disabled={isMutating || !userData?.isActive}
                            >
                                Update
                            </Button>
                        </div>
                    </div>
                )}
                defaultValues={{
                    ...userData,
                    avatar: userData?.user?.avatar,
                }}
                disabled={isMutating}
                onSubmit={onSubmit}
                isActive={!!userData?.isActive}
                mode="edit"
            />
            <ConfirmModal
                title={!userData?.isActive ? 'Active Staff' : 'Inactive Staff'}
                open={activateStaffModalOpen}
                confirmText="Ok"
                message={format(
                    userData?.isActive
                        ? CommonMessages.DeactivateAlert
                        : CommonMessages.ActivateAlert,
                    'staff'
                )}
                onCancel={() => {
                    setActivateStaffModalOpen(false);
                }}
                onConfirm={onActivateStaff}
            />
        </>
    );
}
