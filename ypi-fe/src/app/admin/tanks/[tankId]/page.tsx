'use client';

import _ from 'lodash';
import toast from 'react-hot-toast';
import TopBar from '@/components/manage-customers/TopBar';
import { flex } from '@/styled-system/patterns';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import Modal from '@/components/globals/Modal';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { Slug } from '@/utilities/types/enums/Slug';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';
import TankForm from '@/components/manage-customers/TankForm';
import {
    useActivateTank,
    useCleanDisinfectionByTank,
    useDeleteTank,
    useRemarkInspectorByTank,
    useTankById,
    useUpdateTank,
} from '@/hooks/tank';
import { ITank } from '@/utilities/types/entities/tank';
import TankHeader from '@/components/tank-detail/TankHeader';
import Tabs from '@/components/globals/Tabs';
import Button from '@/components/globals/Button';
import { expandedStyle } from '@/components/tank-detail/TankHeader/style';
import DisinfectionTaskList from '@/components/tank-detail/DisinfectionTaskList';
import LabReportPane from '@/components/tank-detail/LabReportPane';
import RemarkTaskList from '@/components/tank-detail/RemarkTaskList';
import { useMaterialAllow } from '@/hooks/material';
import { convertTaskToJob } from '@/utilities/helpers/task';
import ScrollView from '@/components/globals/ScrollView';
import { css } from '@/styled-system/css';
import { useActivateCustomer } from '@/hooks/customer';
import { ArrowContainer, Popover } from 'react-tiny-popover';
import PopoverTiny from '@/components/globals/PopoverTiny';

type Props = {
    params: { tankId: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

type PageTabKeyType = 'disinfection' | 'remark';

export default function TankDetailPage({ params, searchParams }: Props) {
    const router = useRouter();
    const { data, mutate } = useTankById(params.tankId);
    const [activateTanlModalOpen, setActivateTankModalOpen] = useState(false);
    const cleanDisinfectionFilter = useMemo(() => ({ pageIndex: 1 }), []);
    const {
        data: cleanDisinfectionData,
        isLoading,
        canNext,
        next,
    } = useCleanDisinfectionByTank(params.tankId, cleanDisinfectionFilter);
    const remarkInspectorFilter = useMemo(() => ({ pageIndex: 1 }), []);
    const {
        data: remarkInspectorData,
        isLoading: isRemarkInspectorLoading,
        canNext: canRemarkInspectorNext,
        next: remarkInspectorNext,
    } = useRemarkInspectorByTank(params.tankId, remarkInspectorFilter);

    const [editTankModalOpen, setEditTankModalOpen] = useState(false);
    const [deleteTankModalOpen, setDeleteTankModalOpen] = useState(false);
    const [currentTab, setCurrentTab] =
        useState<PageTabKeyType>('disinfection');
    const { data: materialAllows } = useMaterialAllow();

    const { trigger: updateTank, isMutating: isEditing } = useUpdateTank(
        params.tankId
    );
    const deleteTank = useDeleteTank();

    const onUpdateTank = (values: Partial<ITank>) => {
        toast.promise(
            updateTank<boolean>({ ...values, locationId: data?.location?.id }),
            {
                loading: CommonMessages.Updating,
                success: () => {
                    mutate();
                    setEditTankModalOpen(false);
                    return CommonMessages.UpdatedSuccessful;
                },
                error: (err) => err.message,
            }
        );
    };

    const activateTank = useActivateTank();

    const onDeleteTank = () => {
        deleteTank(params.tankId).then(() => {
            router.replace(
                `${Slug.MANAGE_CUSTOMERS}/${data?.customerId}/location/${data?.locationId}`
            );
        });
        setDeleteTankModalOpen(false);
    };

    const onActivateTank = () => {
        if (data) {
            activateTank(data?.id || '', { isActive: !data.isActive })
                .then(() => {
                    mutate();
                    setActivateTankModalOpen(false);
                })
                .catch((e) => {});
        }
    };

    const closeModel = () => setEditTankModalOpen(false);

    const [open, setOpen] = useState(false);

    const onClose = () => {
        setOpen(false);
    };

    if (!data) return <></>;
    const tankListCallbackUrl = searchParams?.callbackUrl;

    const disableAssign = !data?.isActive || !data?.canInactive;

    const messageDisableAssign = () => {
        if (!data.isActive) {
            return 'Tank is inactive. You cannot assign tasks to it.';
        }
        if (!data.canInactive) {
            return 'This tank has tasks assigned. You cannot assign tasks to it.';
        }

        return '';
    };
    return (
        <>
            <div
                className={flex({
                    flex: 1,
                    direction: 'column',
                })}
            >
                <TopBar
                    onBack={() => {
                        router.back();
                    }}
                    primaryButtonVisible={false}
                    secondaryButtonVisible
                    title="Tank details"
                    renderButtons={() => (
                        <div className={flex({ gap: '12px', pt: 2 })}>
                            <PopoverTiny
                                message={
                                    'This tank has tasks assigned. You cannot inactive'
                                }
                                trigger={['hover']}
                                positions={['bottom', 'top']}
                                disabled={data?.canInactive}
                            >
                                <Button
                                    type="button"
                                    visual={
                                        data?.isActive
                                            ? 'solid_inactive'
                                            : 'solid_active'
                                    }
                                    onClick={() => {
                                        if (data)
                                            setActivateTankModalOpen(true);
                                    }}
                                    disabled={!data?.canInactive}
                                    className={css({ flex: 1 })}
                                >
                                    {data?.isActive ? 'Inactive' : 'Active'}
                                </Button>
                            </PopoverTiny>

                            <PopoverTiny
                                message={messageDisableAssign()}
                                trigger={['hover']}
                                positions={['bottom', 'top']}
                                disabled={!disableAssign}
                            >
                                <Button
                                    onClick={() => {
                                        router.push(
                                            `${Slug.TANKS}/${params.tankId}/assign-task/?callbackUrl=${tankListCallbackUrl}`
                                        );
                                    }}
                                    visual="outline_primary"
                                    disabled={disableAssign}
                                >
                                    Assign task
                                </Button>
                            </PopoverTiny>
                        </div>
                    )}
                />
                <div
                    className={flex({
                        flex: 1,
                        direction: 'column',
                    })}
                >
                    <TankHeader
                        tank={data}
                        onEdit={() => {
                            setEditTankModalOpen(true);
                        }}
                        onDelete={() => setDeleteTankModalOpen(true)}
                    />
                    <div
                        className={flex({
                            flex: 1,
                            direction: 'row',
                            gap: 4,
                        })}
                    >
                        <div
                            className={flex({
                                flex: 1,
                                flexDirection: 'column',
                                pb: 1,
                            })}
                        >
                            <div className={flex({ gap: 3, mt: 3 })}>
                                <Tabs
                                    className={expandedStyle}
                                    tabs={[
                                        {
                                            key: 'disinfection',
                                            title: 'Date of Clean/Disinfection',
                                        },
                                        {
                                            key: 'remark',
                                            title: 'Remark of Inspector',
                                        },
                                    ]}
                                    selectedTabKey={currentTab}
                                    onChange={(key) =>
                                        setCurrentTab(key as PageTabKeyType)
                                    }
                                />
                                <Button
                                    href={`${Slug.TANKS}/${params.tankId}/history`}
                                    visual="solid_primary"
                                >
                                    History
                                </Button>
                            </div>
                            <ScrollView
                                className={flex({
                                    flex: 1,
                                    direction: 'column',
                                })}
                            >
                                {currentTab === 'disinfection' ? (
                                    <DisinfectionTaskList
                                        data={convertTaskToJob(
                                            cleanDisinfectionData?.records || []
                                        )}
                                        canLoadMore={canNext}
                                        onLoadMore={next}
                                        loading={isLoading}
                                    />
                                ) : (
                                    <RemarkTaskList
                                        data={
                                            remarkInspectorData?.records || []
                                        }
                                        canLoadMore={canRemarkInspectorNext}
                                        onLoadMore={remarkInspectorNext}
                                        loading={isRemarkInspectorLoading}
                                    />
                                )}
                            </ScrollView>
                        </div>
                        <LabReportPane
                            tankId={data?.id}
                            reports={data?.labReports || []}
                        />
                    </div>
                </div>
            </div>
            <ConfirmModal
                open={deleteTankModalOpen}
                onCancel={() => {
                    setDeleteTankModalOpen(false);
                }}
                onConfirm={onDeleteTank}
                title="Alert"
                message={format(CommonMessages.DeletingAlert, 'tank')}
            />
            <Modal
                isOpen={editTankModalOpen}
                onClose={() => {
                    if (!isEditing) {
                        closeModel();
                    }
                }}
                title="Update Tank"
                sizes="md"
            >
                <TankForm
                    isLoading={isEditing}
                    isUpdate
                    onSubmit={onUpdateTank}
                    materialAllows={materialAllows}
                    defaultValues={{
                        airdentity: data?.airdentity,
                        code: data?.code,
                        materialId: data?.material?.id,
                        shape: data?.shape,
                        type: data?.type,
                        width: data?.width,
                        height: data?.height,
                        length: data?.length,
                        effectiveCap: data?.effectiveCap,
                        floorLevel: data?.floorLevel,
                        waterSaved: data?.waterSaved,
                    }}
                />
            </Modal>
            <ConfirmModal
                title={!data?.isActive ? 'Active Tank' : 'Inactive Tank'}
                open={activateTanlModalOpen}
                confirmText="Ok"
                message={format(
                    data?.isActive
                        ? CommonMessages.DeactivateAlert
                        : CommonMessages.ActivateAlert,
                    'staff'
                )}
                onCancel={() => {
                    setActivateTankModalOpen(false);
                }}
                onConfirm={onActivateTank}
            />
        </>
    );
}
