'use client';

import TopBar from '@/components/manage-customers/TopBar';
import { flex } from '@/styled-system/patterns';
import { useRouter } from 'next/navigation';
import { UIEventHandler, useEffect, useRef, useState } from 'react';
import { useTankHistory } from '@/hooks/tank';
import { ITankHistoryListQueryParams } from '@/utilities/types/entities/tank';
import _, { range } from 'lodash';
import { SingleSelect } from '@/components/globals/Select';
import DisinfectionHistory from '@/components/tank-detail/DisinfectionHistory';
import ScrollView from '@/components/globals/ScrollView';
import dayjs from 'dayjs';
import { FORMAT } from '@/configs/date';
import Skeleton from '@/components/globals/Skeleton';
import { css } from '@/styled-system/css';
import TableEmpty from '@/components/globals/Table/TableEmpty';

type Props = {
    params: { tankId: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

export default function TankDetailHistoryPage({ params }: Props) {
    const router = useRouter();
    const containerRef = useRef<HTMLDivElement>(null);
    const [filter, setFilter] = useState<Partial<ITankHistoryListQueryParams>>({
        pageSize: 30,
        datetime: dayjs().format(FORMAT.DATE_TIME_FULL),
    });
    const { data, isLoading, canNext, next } = useTankHistory(
        params.tankId,
        filter
    );
    const isFirstLoad = !data?.records?.length && !!isLoading;
    const onScroll: UIEventHandler<HTMLDivElement> = (ev) => {
        const el = ev.currentTarget;
        if (canNext) {
            const viewHeight = el.getBoundingClientRect().height;
            const scrollHeight = el.scrollHeight;
            if (el.scrollTop + viewHeight >= scrollHeight - 20) {
                next();
            }
        }
    };
    useEffect(() => {
        if (isLoading || canNext || !containerRef.current) {
            return;
        }
        const containerEl = containerRef.current;
        const viewHeight = containerEl.getBoundingClientRect().height;
        const scrollHeight = containerEl.scrollHeight;

        if (viewHeight >= scrollHeight) {
            next();
        }
    }, [isLoading, canNext, next, data?.records?.length]);

    return (
        <>
            <div
                className={flex({
                    flex: 1,
                    gap: '48px',
                    direction: 'column',
                })}
            >
                <TopBar
                    onBack={() => {
                        router.back();
                    }}
                    title="Tank details"
                    renderButtons={() => {
                        return (
                            <SingleSelect
                                value={dayjs(filter.datetime)
                                    .toDate()
                                    .getFullYear()
                                    .toString()}
                                id="year"
                                sizes="sm"
                                variant="outline"
                                color="primary"
                                strong
                                onChange={(option) => {
                                    setFilter({ ...filter, year: option?.id });
                                }}
                                options={_.range(
                                    2020,
                                    new Date().getFullYear() + 1
                                ).map((year) => ({
                                    id: year.toString(),
                                    name: year.toString(),
                                }))}
                            />
                            // <SingleSelect />
                        );
                    }}
                />
                <div
                    className={css({
                        flex: 1,
                    })}
                    ref={containerRef}
                    onScroll={onScroll}
                >
                    <ScrollView
                        className={flex({
                            flex: 1,
                            gap: '48px',
                            direction: 'column',
                        })}
                    >
                        {data?.records?.map((assignTask, i) => (
                            <DisinfectionHistory
                                key={assignTask?.id}
                                index={i + 1}
                                assignTask={assignTask}
                            />
                        ))}

                        {isFirstLoad ? (
                            range(0, 5).map((i) => <Skeleton key={i} />)
                        ) : data?.records?.length === 0 ? (
                            <TableEmpty />
                        ) : null}
                    </ScrollView>
                </div>
            </div>
        </>
    );
}
