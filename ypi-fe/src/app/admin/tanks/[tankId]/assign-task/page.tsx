'use client';

import { AssignTankForm } from '@/components/assign-tank/AssignTankForm';
import AssignTankHeader from '@/components/assign-tank/AssignTankHeader';
import ScrollView from '@/components/globals/ScrollView';
import TopBar from '@/components/manage-customers/TopBar';
import { useLocationById } from '@/hooks/location';
import { useStaffAllow } from '@/hooks/staff';
import { useTankById } from '@/hooks/tank';
import { flex } from '@/styled-system/patterns';
import { useRouter } from 'next/navigation';

type TProps = {
    params: { tankId: string };
    searchParams: {
        mode?: 'create' | 'edit';
        callbackUrl?: string;
        assignTaskId?: string;
    };
};

export default function AssignTaskPage({ params, searchParams }: TProps) {
    const router = useRouter();
    const { mode, assignTaskId } = searchParams;
    // TODO: API useTankById hasnt integrated yet
    const { data } = useTankById(params.tankId, assignTaskId);
    const { data: location } = useLocationById(data?.location?.id);
    const { data: workers } = useStaffAllow();

    const workerOptions = workers?.map((item) => ({
        value: item.id,
        label: item.fullName,
    }));

    const onBack = () => {
        if (searchParams?.callbackUrl) {
            router.push(searchParams?.callbackUrl);
        }

        router.back();
    };

    return (
        <div
            className={flex({
                flex: 1,
                direction: 'column',
            })}
        >
            <TopBar
                onBack={onBack}
                title={`Job ID`}
                subTitle={data?.code}
                primaryButtonVisible={false}
            />
            <ScrollView
                className={flex({
                    flex: 1,
                    direction: 'column',
                    gap: 6,
                })}
            >
                <AssignTankHeader tank={data} location={location} />
                <AssignTankForm
                    tank={data}
                    workerOptions={workerOptions}
                    mode={mode ?? 'create'}
                />
            </ScrollView>
        </div>
    );
}
