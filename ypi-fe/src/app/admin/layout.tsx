import Sidebar from '@/components/globals/Sidebar';
import React from 'react';
import { flex } from '@/styled-system/patterns';
import SideBarItem from '@/utilities/types/side-bar-item';
import { getSideBarItemFromSlug } from '@/utilities/helpers/side-bar';
import { Slug } from '@/utilities/types/enums/Slug';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utilities/lib/auth';
import { EUserRole } from '@/utilities/types/entities/user';
import { RedirectType, redirect } from 'next/navigation';
import { AdminLayout, RightLayout } from '@/components/globals/Layout';
import { css } from '@/styled-system/css';

interface Props {
    children: React.ReactNode;
}

const generalReportSubItems: SideBarItem[] = [
    getSideBarItemFromSlug(
        Slug.GR_PRE_INSPECTION,
        [],
        css({ pl: 3, pr: 3, minWidth: '100% !important' })
    ),
    getSideBarItemFromSlug(
        Slug.GR_NON_COMPLIANCE,
        [],
        css({ pl: 3, pr: 3, minWidth: '100% !important' })
    ),
    getSideBarItemFromSlug(
        Slug.GR_COMPLETE,
        [],
        css({ pl: 3, pr: 3, minWidth: '100% !important' })
    ),
];

const sideBarItems: SideBarItem[] = [
    getSideBarItemFromSlug(Slug.MANAGE_CUSTOMERS),
    getSideBarItemFromSlug(Slug.GENERAL_REPORT, generalReportSubItems),
    getSideBarItemFromSlug(Slug.MANAGE_STAFF),
    getSideBarItemFromSlug(Slug.CALENDAR),
    getSideBarItemFromSlug(Slug.REMINDERS),
];

export default async function Layout({ children }: Props) {
    const auth = await getServerSession(authOptions);

    if (auth?.user?.role !== EUserRole.Admin) {
        redirect('/', RedirectType.replace);
    }
    return (
        <AdminLayout>
            <Sidebar items={sideBarItems} />
            <RightLayout>{children}</RightLayout>
        </AdminLayout>
    );
}
