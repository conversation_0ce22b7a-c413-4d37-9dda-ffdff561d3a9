'use client';

import { useMemo, useState } from 'react';
import Link from 'next/link';
import ScrollView from '@/components/globals/ScrollView';
import TopBar from '@/components/manage-customers/TopBar';
import { useCompleteId, useDownloadEmailContent } from '@/hooks/general-report';
import { flex } from '@/styled-system/patterns';
import { useRouter } from 'next/navigation';
import { css } from '@/styled-system/css';
import Button from '@/components/globals/Button';
import { emailIconStyle, footerWrapper, scrollViewContainer } from './style';
import Typography from '@/components/globals/Typography';
import CompleteHeader from '@/components/general-report/complete-detail/Header';
import ETypographyTag from '@/utilities/types/enums/Typography';
import LabReport from '@/components/general-report/complete-detail/LabReport';
import DateCleanAndDisinfectionList from '@/components/general-report/complete-detail/DateCleanAndDisinfectionList';
import CustomerJobSatisfied from '@/components/general-report/complete-detail/CustomerJobSatisfied';
import { Slug } from '@/utilities/types/enums/Slug';
import JobProgress, {
    EJobProgressStep,
} from '@/components/manage-customers/JobProgress';
import { MailIcon } from '@/components/globals/Icons';
import { SendEmailModal } from '@/components/general-report/complete-detail/SendEmailModal';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import { downloadFileBlob } from '@/utilities/globals/function/download';
import toast from 'react-hot-toast';
import CommonMessages from '@/utilities/messages/common';

type CompleteDetailProps = {
    params: { id: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

export default function CompleteDetailPage({
    params,
    searchParams,
}: CompleteDetailProps) {
    const router = useRouter();
    const { data, mutate } = useCompleteId(params?.id);
    const [open, setOpen] = useState(false);

    const staffInfo = useMemo(() => {
        if (!data || !data?.nonCompliance?.staffs?.length) {
            return {
                preInspection: { createdAt: '', staffNames: [] },
                nonCompliance: { createdAt: '', staffNames: [] },
            };
        }
        return {
            preInspection: {
                createdAt: data?.preInspection?.date || '',
                staffNames: [
                    ...new Set(
                        data?.preInspection?.staffs.map(
                            (item) => item?.fullName
                        )
                    ),
                ],
            },
            nonCompliance: {
                createdAt: data?.nonCompliance?.date || '',
                staffNames: [
                    ...new Set(
                        data?.nonCompliance?.staffs.map(
                            (item) => item?.fullName
                        )
                    ),
                ],
            },
        };
    }, [data]);

    const handleClickSendEmail = () => {
        setOpen(!open);
    };
    const { trigger: downloadEmail, isMutating } = useDownloadEmailContent(
        EGeneralReport.COMPLETE_DOWNLOAD_EMAIL
    );

    async function downloadPdf() {
        const response = await downloadEmail({
            assignTaskId: data?.id || '',
        });

        await downloadFileBlob({
            file: response,
            filename: 'CompleteEmailContent',
        });
    }
    if (!data) return null;
    return (
        <div className={flex({ flex: 1, direction: 'column' })}>
            <TopBar
                onBack={() => {
                    router.back();
                }}
                title={`Job ID`}
                subTitle={data?.code}
                primaryButtonVisible={false}
                renderButtons={() =>
                    searchParams?.tankId && (
                        <JobProgress
                            activeStep={EJobProgressStep['Complete']}
                        />
                    )
                }
            />
            <ScrollView className={scrollViewContainer}>
                <div className={css({ mb: 3 })}>
                    <CompleteHeader
                        tank={data?.tank}
                        location={data?.tank?.location}
                        nonCompliance={staffInfo?.nonCompliance}
                        preInspection={staffInfo?.preInspection}
                    />
                </div>
                <Typography typography="header_20" color="primary_100">
                    Date of Clean/Disinfection
                </Typography>
                <DateCleanAndDisinfectionList
                    data={data?.tasks}
                    completedDate={data?.completedDate}
                />
                <div className={flex({ gap: 3, mt: 3, mb: 3 })}>
                    <div
                        className={flex({
                            flex: 1,
                            direction: 'column',
                        })}
                    >
                        <Typography
                            typography="subtitle_20"
                            tag={ETypographyTag.h2}
                            color="primary_100"
                        >
                            Lab Report
                        </Typography>
                        <LabReport
                            reports={data?.labReports}
                            completeId={data?.id}
                            mutate={mutate}
                        />
                    </div>
                    <div
                        className={flex({
                            flex: 1,
                            direction: 'column',
                        })}
                    >
                        <Typography
                            typography="subtitle_20"
                            tag={ETypographyTag.h2}
                            color="primary_100"
                        >
                            Customer job satisfied
                        </Typography>
                        <CustomerJobSatisfied
                            customerName={
                                data?.tank?.location?.customer?.name || ''
                            }
                            signatureDate={data?.customerSignatureDate}
                            signatureImg={data?.customerSignature}
                        />
                    </div>
                </div>
            </ScrollView>
            <div className={footerWrapper}>
                <Button
                    size="medium"
                    type="button"
                    disabled={isMutating}
                    onClick={async () => {
                        toast.promise(downloadPdf(), {
                            loading: CommonMessages.Downloading,
                            success: () => {
                                return CommonMessages.DownloadSuccessful;
                            },
                            error: (err) => err.message,
                        });
                    }}
                >
                    <DownloadIcon
                        width={18}
                        height={18}
                        className={css({
                            color: 'white',
                        })}
                    />
                    <span>Download</span>
                </Button>
                <div>
                    <Button
                        onClick={handleClickSendEmail}
                        size="medium"
                        type="button"
                        visual="outline_primary"
                    >
                        <MailIcon
                            width={18}
                            height={18}
                            className={emailIconStyle}
                        />
                        <span>Send email</span>
                    </Button>
                    <SendEmailModal
                        isOpen={open}
                        onClose={handleClickSendEmail}
                        tank={data?.tank}
                        location={data?.tank?.location}
                        staffs={data?.staffs}
                        tasks={data?.tasks}
                        completedDate={data?.completedDate}
                        sampleTakenDate={data?.sampleTakenDate}
                        reportDate={data?.reportDate}
                        customerSignature={data?.customerSignature}
                        assignTaskId={data?.id}
                        reports={data?.labReports}
                    />
                </div>
                <Link href={`${Slug.GR_COMPLETE}/${params?.id}/view-pdf`}>
                    <Button>View pdf</Button>
                </Link>
            </div>
        </div>
    );
}
