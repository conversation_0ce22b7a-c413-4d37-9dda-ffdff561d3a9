'use client';

import { useMemo, useState } from 'react';
import FilterElements from '@/components/general-report/FilterElements';
import Typography from '@/components/globals/Typography';
import { getCompleteIdClient, useGeneralReport } from '@/hooks/general-report';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import {
    IComplete,
    ICompleteDetail,
} from '@/utilities/types/entities/general-report';
import CompleteList from '@/components/general-report/complete/CompleteList';
import { FORMAT } from '@/configs/date';
import ScrollView from '@/components/globals/ScrollView';
import { SendEmailModal } from '@/components/general-report/complete-detail/SendEmailModal';

export default function CompletePage() {
    const [search, setSearch] = useState('');
    const [date, setDate] = useState<Date | null>(new Date());
    const [completeData, setCompleteData] = useState<ICompleteDetail>();
    const [open, setOpen] = useState(false);

    const filter = useMemo(() => {
        return {
            pageIndex: 1,
            pageSize: 30,
            startTime: dayjs(date)
                .startOf('month')
                .format(FORMAT.DATE_TIME_FULL),
            endTime: dayjs(date).endOf('month').format(FORMAT.DATE_TIME_FULL),
            keyword: search,
        };
    }, [search, date]);

    const { data, isLoading, next, canNext } = useGeneralReport<IComplete>(
        EGeneralReport.COMPLETE,
        filter
    );

    const handleSendEmail = async (taskId: string) => {
        const data = await getCompleteIdClient(taskId);
        if (data?.status === 200) {
            setCompleteData(data?.data);
            setOpen(true);
        }
    };

    const handleClickSendEmail = () => {
        setOpen(!open);
    };

    return (
        <div className={flex({ flex: 1, direction: 'column' })}>
            <div
                className={flex({
                    justifyContent: 'space-between',
                    alignItems: 'center',
                })}
            >
                <div className={css({ flex: 1 })}>
                    <Typography
                        color="primary_100"
                        typography="header_24"
                        tag={ETypographyTag.h1}
                        className={css({ textTransform: 'uppercase' })}
                    >
                        Complete
                    </Typography>
                </div>
                <div>
                    <FilterElements
                        onFilterDate={(date) => {
                            setDate(date);
                        }}
                        onSearch={(s) => setSearch(s)}
                        defaultFilter={{ date }}
                    />
                </div>
            </div>
            <ScrollView
                className={flex({
                    pt: 6,
                    flex: 1,
                    direction: 'column',
                })}
            >
                <CompleteList
                    data={data?.records ?? []}
                    loading={isLoading}
                    canLoadMore={canNext}
                    onLoadMore={next}
                    handleSendEmail={handleSendEmail}
                />
            </ScrollView>
            <SendEmailModal
                isOpen={open}
                onClose={handleClickSendEmail}
                tank={completeData?.tank}
                location={completeData?.tank?.location}
                staffs={completeData?.staffs}
                tasks={completeData?.tasks}
                completedDate={completeData?.completedDate}
                sampleTakenDate={completeData?.sampleTakenDate}
                reportDate={completeData?.reportDate}
                customerSignature={completeData?.customerSignature}
                assignTaskId={completeData?.id as string}
                reports={completeData?.labReports}
            />
        </div>
    );
}
