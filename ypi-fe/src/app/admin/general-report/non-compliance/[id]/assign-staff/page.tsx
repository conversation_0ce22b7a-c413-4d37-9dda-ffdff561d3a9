'use client';

import AssignStaffForm from '@/components/general-report/assign-staff/AssignStaffForm';
import ScrollView from '@/components/globals/ScrollView';
import TopBar from '@/components/manage-customers/TopBar';
import { useNonComplianceId } from '@/hooks/general-report';
import { useStaffAllow } from '@/hooks/staff';
import { flex } from '@/styled-system/patterns';
import { EActionTypes } from '@/utilities/types/enums/Form';
import { ETaskStatus } from '@/utilities/types/enums/Task';
import { useRouter } from 'next/navigation';

type TProps = {
    params: { id: string };
    searchParams: { callbackUrl: string; mode: EActionTypes } & {
        [key: string]: string | string[] | undefined;
    };
};

export default function AssignTaskPage({ params, searchParams }: TProps) {
    const router = useRouter();
    const { data } = useNonComplianceId(params.id);
    const { data: workers } = useStaffAllow();

    const workerOptions = workers?.map((item) => ({
        value: item.id,
        label: item.fullName,
    }));

    if (!data) return <></>;

    const onBack = () => {
        router.back();
    };
    return (
        <div
            className={flex({
                flex: 1,
                direction: 'column',
            })}
        >
            <TopBar
                onBack={onBack}
                subTitle={data?.code}
                primaryButtonVisible={false}
            />
            <ScrollView
                className={flex({
                    flex: 1,
                    direction: 'column',
                    pl: 4,
                    gap: 6,
                })}
            >
                <AssignStaffForm
                    data={data}
                    workerOptions={workerOptions}
                    defaultValues={{
                        acceptRectify: 'yes',
                    }}
                    mode={searchParams?.mode || EActionTypes['CREATE']}
                    tasks={data?.tasks?.filter(
                        (task) =>
                            task?.status === ETaskStatus['UN_COMPLETED'] ||
                            task?.status === ETaskStatus['RECTIFY']
                    )}
                />
            </ScrollView>
        </div>
    );
}
