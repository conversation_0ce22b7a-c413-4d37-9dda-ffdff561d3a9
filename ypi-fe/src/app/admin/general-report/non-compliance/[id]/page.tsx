'use client';

import { useNonComplianceId, usePreInspectionId } from '@/hooks/general-report';
import CompletedNonComplianceDetailPage from '@/components/general-report/completed-noncompliance';
import PrepareNonComplianceDetailPage from '@/components/general-report/prepare-noncompliance';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';

type TProps = {
    params: { id: string };
    searchParams: { callbackUrl: string } & {
        [key: string]: string | string[] | undefined;
    };
};

export default function NonComplianceDetailPage({
    params,
    searchParams,
}: TProps) {
    const { data: nonComplianceData } = useNonComplianceId(params.id);
    if (!nonComplianceData) return null;
    return nonComplianceData?.status === EAssignTaskStatus['COMPLETED'] ? (
        <CompletedNonComplianceDetailPage
            searchParams={searchParams}
            params={params}
            data={nonComplianceData}
        />
    ) : (
        <PrepareNonComplianceDetailPage
            searchParams={searchParams}
            params={params}
            data={nonComplianceData}
        />
    );
}
