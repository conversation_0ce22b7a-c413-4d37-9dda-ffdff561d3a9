'use client';

import { useMemo, useState } from 'react';
import FilterElements from '@/components/general-report/FilterElements';
import Typography from '@/components/globals/Typography';
import {
    getNonComplianceIdClient,
    useGeneralReport,
} from '@/hooks/general-report';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import {
    INonCompliance,
    INonComplianceDetail,
} from '@/utilities/types/entities/general-report';
import NonComplianceList from '@/components/general-report/non-compliance/NonComplianceList';
import ScrollView from '@/components/globals/ScrollView';
import { FORMAT } from '@/configs/date';
import { SendEmailModal as SendEmailCompletedModal } from '@/components/general-report/non-compliance-detail/SendEmailModal';
import { EAssignTaskStatus, ETaskStatus } from '@/utilities/types/enums/Task';
import { SendEmailModal } from '@/components/general-report/pre-inspection-detail/SendEmailModal';

export default function NonCompliancePage() {
    const [search, setSearch] = useState('');
    const [date, setDate] = useState<Date | null>(new Date());
    const [nonComplianceData, setNonComplianceData] =
        useState<INonComplianceDetail>();
    const [open, setOpen] = useState(false);
    const [status, setStatus] = useState<ETaskStatus>();

    const filter = useMemo(() => {
        return {
            pageIndex: 1,
            pageSize: 30,
            startTime: dayjs(date)
                .startOf('month')
                .format(FORMAT.DATE_TIME_FULL),
            endTime: dayjs(date).endOf('month').format(FORMAT.DATE_TIME_FULL),
            keyword: search,
        };
    }, [search, date]);

    const { data, isLoading, next, canNext } = useGeneralReport<INonCompliance>(
        EGeneralReport.NON_COMPLIANCE,
        filter
    );

    const handleSendEmail = async (taskId: string, status: ETaskStatus) => {
        setStatus(status);
        const data = await getNonComplianceIdClient(taskId);
        if (data?.status === 200) {
            setNonComplianceData(data?.data);
            setOpen(true);
        }
    };

    const handleClickSendEmail = () => {
        setOpen(!open);
    };

    return (
        <div className={flex({ flex: 1, direction: 'column' })}>
            <div
                className={flex({
                    justifyContent: 'space-between',
                    alignItems: 'center',
                })}
            >
                <div className={css({ flex: 1 })}>
                    <Typography
                        color="primary_100"
                        typography="header_24"
                        tag={ETypographyTag.h1}
                        className={css({ textTransform: 'uppercase' })}
                    >
                        Non-Compliance
                    </Typography>
                </div>
                <div>
                    <FilterElements
                        onFilterDate={(date) => {
                            setDate(date);
                        }}
                        onSearch={(s) => setSearch(s)}
                        defaultFilter={{ date }}
                    />
                </div>
            </div>
            <ScrollView
                className={flex({
                    pt: 6,
                    flex: 1,
                    direction: 'column',
                })}
            >
                <NonComplianceList
                    data={data?.records ?? []}
                    loading={isLoading}
                    canLoadMore={canNext}
                    onLoadMore={next}
                    handleSendEmail={handleSendEmail}
                />
            </ScrollView>
            {status === ETaskStatus.COMPLETED ? (
                <SendEmailCompletedModal
                    isOpen={open}
                    onClose={handleClickSendEmail}
                    tank={nonComplianceData?.tank}
                    location={nonComplianceData?.tank?.location}
                    staffs={nonComplianceData?.staffs}
                    tasks={nonComplianceData?.tasks}
                    completedDate={nonComplianceData?.completedDate}
                    assignTaskId={nonComplianceData?.id as string}
                />
            ) : (
                <SendEmailModal
                    isOpen={open}
                    onClose={handleClickSendEmail}
                    tank={nonComplianceData?.tank}
                    location={nonComplianceData?.tank?.location}
                    staffs={nonComplianceData?.staffs}
                    tasks={nonComplianceData?.tasks}
                    preInspectionDate={nonComplianceData?.createdAt}
                    assignTaskId={nonComplianceData?.id as string}
                    status={nonComplianceData?.status as EAssignTaskStatus}
                />
            )}
        </div>
    );
}
