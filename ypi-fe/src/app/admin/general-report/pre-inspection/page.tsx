'use client';

import FilterElements from '@/components/general-report/FilterElements';
import Typography from '@/components/globals/Typography';
import {
    useGeneralReport,
    getPreInspectionIdClient,
} from '@/hooks/general-report';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { useMemo, useState } from 'react';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import {
    IPreInspection,
    IPreInspectionDetail,
} from '@/utilities/types/entities/general-report';
import PreInspectionList from '@/components/general-report/pre-inspection/PreInspectionList';
import ScrollView from '@/components/globals/ScrollView';
import { FORMAT } from '@/configs/date';
import { SendEmailModal } from '@/components/general-report/pre-inspection-detail/SendEmailModal';
import { EAssignTaskStatus } from '@/utilities/types/enums/Task';

export default function PreInspection() {
    const [search, setSearch] = useState('');
    const [date, setDate] = useState<Date | null>(new Date());
    const [preInspectionData, setPreInspectionData] =
        useState<IPreInspectionDetail>();
    const [open, setOpen] = useState(false);

    const filter = useMemo(() => {
        return {
            pageIndex: 1,
            pageSize: 30,
            startTime: dayjs(date)
                .startOf('month')
                .format(FORMAT.DATE_TIME_FULL),
            endTime: dayjs(date).endOf('month').format(FORMAT.DATE_TIME_FULL),
            keyword: search,
        };
    }, [search, date]);

    const { data, isLoading, next, canNext } = useGeneralReport<IPreInspection>(
        EGeneralReport.PRE_INSPECTION,
        filter
    );

    const handleSendEmail = async (taskId: string) => {
        const data = await getPreInspectionIdClient(taskId);
        if (data?.status === 200) {
            setPreInspectionData(data?.data);
            setOpen(true);
        }
    };

    const handleClickSendEmail = () => {
        setOpen(!open);
    };

    return (
        <div className={flex({ flex: 1, direction: 'column' })}>
            <div
                className={flex({
                    justifyContent: 'space-between',
                    alignItems: 'center',
                })}
            >
                <div className={css({ flex: 1 })}>
                    <Typography
                        color="primary_100"
                        typography="header_24"
                        tag={ETypographyTag.h1}
                        className={css({ textTransform: 'uppercase' })}
                    >
                        Pre-inspection
                    </Typography>
                </div>

                <div>
                    <FilterElements
                        onFilterDate={(date) => {
                            setDate(date);
                        }}
                        onSearch={(s) => setSearch(s)}
                        defaultFilter={{
                            date,
                        }}
                    />
                </div>
            </div>
            <ScrollView
                className={flex({
                    pt: 6,
                    flex: 1,
                    direction: 'column',
                })}
            >
                <PreInspectionList
                    data={data?.records ?? []}
                    loading={isLoading}
                    canLoadMore={canNext}
                    onLoadMore={next}
                    handleSendEmail={handleSendEmail}
                />
            </ScrollView>
            <SendEmailModal
                isOpen={open}
                onClose={handleClickSendEmail}
                tank={preInspectionData?.tank}
                location={preInspectionData?.tank?.location}
                staffs={preInspectionData?.staffs}
                tasks={preInspectionData?.tasks}
                preInspectionDate={preInspectionData?.createdAt}
                assignTaskId={preInspectionData?.id as string}
                status={preInspectionData?.status as EAssignTaskStatus}
            />
        </div>
    );
}
