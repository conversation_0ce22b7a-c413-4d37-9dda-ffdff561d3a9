'use client';

import Topbar from '@/components/globals/Topbar';
import { Slug } from '@/utilities/types/enums/Slug';
import pageConfig from '@/utilities/page-config';
import { useState } from 'react';
import { flex } from '@/styled-system/patterns';
import { useCustomerList } from '@/hooks/customer';
import { IPaginationParams } from '@/utilities/types/request';
import CustomerList from '@/components/manage-customers/CustomerList';
import Drawer from '@/components/globals/Drawer';
import { useBuildings } from '@/hooks/building';
import Filter from '@/components/manage-customers/Filter';
import { useRouter } from 'next/navigation';
import ScrollView from '@/components/globals/ScrollView';

export default function ManageCustomer() {
    const router = useRouter();
    const [filterOpen, setFilterOpen] = useState(false);
    const [buildingFilter, setBuildingFilter] = useState<
        Record<string, boolean>
    >({});

    const [filter, setFilter] = useState<Record<string, any>>({});

    const { data: buildingCategories } = useBuildings();
    const { data, isLoading, next, canNext } = useCustomerList(filter);

    const updateFilter = (newValue: Partial<IPaginationParams>) => {
        setFilter({
            ...filter,
            ...newValue,
        });
    };

    return (
        <div
            className={flex({
                flex: 1,
                direction: 'column',
            })}
        >
            <Drawer
                title="Filter"
                open={filterOpen}
                onClose={() => {
                    setFilterOpen(false);
                    const { agent, ...buildings } = buildingFilter;
                    setFilter({
                        agent,
                        'building[]': Object.keys(buildings).filter(
                            (key) => buildings[key]
                        ),
                    });
                }}
            >
                <Filter
                    buildings={buildingCategories ?? []}
                    valueKey="id"
                    value={buildingFilter}
                    onChange={(key, v) => {
                        setBuildingFilter({
                            ...buildingFilter,
                            [key]: v,
                        });
                    }}
                />
            </Drawer>
            <Topbar
                title={pageConfig[Slug.MANAGE_CUSTOMERS].title}
                filterButtonVisible
                onFilterClick={() => {
                    setFilterOpen(true);
                }}
                onSearch={(keyword) => updateFilter({ keyword })}
                onCreateClick={() => router.push(Slug.CREATE_CUSTOMER)}
            />
            <ScrollView
                className={flex({
                    pt: 6,
                    flex: 1,
                    direction: 'column',
                })}
            >
                <CustomerList
                    data={data?.records ?? []}
                    loading={isLoading}
                    canLoadMore={canNext}
                    onLoadMore={next}
                />
            </ScrollView>
        </div>
    );
}
