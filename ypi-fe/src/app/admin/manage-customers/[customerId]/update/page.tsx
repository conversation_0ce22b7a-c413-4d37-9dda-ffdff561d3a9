'use client';

import Typography from '@/components/globals/Typography';
import NewCustomerForm from '@/components/manage-customers/NewCustomerForm';
import { useCustomerById, useUpdateCustomer } from '@/hooks/customer';
import { css } from '@/styled-system/css';
import { INewCustomerParams } from '@/utilities/types/entities/customer';
import { Slug } from '@/utilities/types/enums/Slug';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

type Props = {
    params: { customerId: string };
};

export default function UpdateCustomPage({ params }: Props) {
    const updateCustomer = useUpdateCustomer();
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const { data: customer } = useCustomerById(params.customerId);

    const onSubmit = (
        data: Partial<INewCustomerParams>
        // setError: UseFormSetError<FieldValues>
    ) => {
        if (customer) {
            setLoading(true);
            updateCustomer(customer, data)
                .then(() => {
                    router.push(
                        `${Slug.MANAGE_CUSTOMERS}/${params.customerId}`
                    );
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    };

    return (
        <div
            className={css({
                pt: 4,
                ml: 6,
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
            })}
        >
            <Typography
                className={css({ textTransform: 'uppercase', mb: 2 })}
                color="primary_100"
                typography="header_24"
            >
                Update customer
            </Typography>

            {customer ? (
                <NewCustomerForm
                    disabled={loading}
                    onSubmit={onSubmit}
                    hasAgent={!!customer.agent}
                    defaultValues={{
                        buildingCategory: customer?.buildingCategory,
                        pubNumber: customer?.pubNumber,
                        email: customer.email,
                        customerName: customer.name,
                        officeTel: customer.officePhoneNumber,
                        officePhoneCode: customer.officePhoneCode,
                        contactPerson: customer.phoneNumber,
                        contactPersonPhoneCode: customer.phoneCode,
                        contactName: customer.agent?.name,
                        companyName: customer.agent?.company,
                        inChargeCategory: customer.agent?.inChargeCategory,
                        designation: customer.agent?.designation,
                        agentEmail: customer.agent?.email,
                        agentOfficeTel: customer.agent?.phoneNumber,
                        agentPhoneCode: customer.agent?.phoneCode,
                        postalCode: customer.agent?.postalCode,
                        blockNo: customer.agent?.blockNo,
                        street: customer.agent?.street,
                        buildingName: customer.agent?.building,
                        nameBuilding: customer?.nameBuilding
                    }}
                    submitText="Update"
                />
            ) : null}
        </div>
    );
}
