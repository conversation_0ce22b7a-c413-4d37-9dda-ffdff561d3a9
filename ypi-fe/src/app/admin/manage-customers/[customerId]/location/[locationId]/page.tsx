'use client';

import { useCreateTank, useTankList } from '@/hooks/tank';
import {
    useDeleteLocation,
    useLocationById,
    useUpdateLocation,
} from '@/hooks/location';
import CommonMessages from '@/utilities/messages/common';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { INewProjectLocationParams } from '@/utilities/types/entities/project-location';
import { ITank } from '@/utilities/types/entities/tank';
import LocationHeader from '@/components/manage-customers/LocationHeader';
import LocationTanksMetadata from '@/components/manage-customers/LocationTanksMetadata';
import Modal from '@/components/globals/Modal';
import NewLocationForm from '@/components/manage-customers/NewLocationForm';
import { Slug } from '@/utilities/types/enums/Slug';
import TankForm from '@/components/manage-customers/TankForm';
import TankList from '@/components/manage-customers/TankList';
import TopBar from '@/components/manage-customers/TopBar';
import { flex } from '@/styled-system/patterns';
import { format } from '@/utilities/globals/function/string';
import toast from 'react-hot-toast';
import { useMaterialAllow } from '@/hooks/material';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';
import { debounce } from 'lodash';

type Props = {
    params: { customerId: string; locationId: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

export default function LocationDetailPage({ params }: Props) {
    const router = useRouter();
    const { data, mutate } = useLocationById(params.locationId);
    const [newTankModalOpen, setNewTankModalOpen] = useState(false);
    const [editLocationModalOpen, setEditLocationModalOpen] = useState(false);
    const [deleteLocationModalOpen, setDeleteLocationModalOpen] =
        useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { data: materialAllows } = useMaterialAllow();
    const { trigger: triggerCreateTank } = useCreateTank();
    const updateLocation = useUpdateLocation();
    const deleteLocation = useDeleteLocation();
    const [filter, setFilter] = useState<Record<string, any>>({
        keyword: '',
        page: 1,
    });

    const {
        data: tanks,
        isLoading: tankListLoading,
        refresh,
        next,
        canNext,
    } = useTankList(params.locationId, filter);

    const pathname = usePathname();
    const onUpdateLocation = (values: Partial<INewProjectLocationParams>) => {
        if (data) {
            setIsSubmitting(true);
            updateLocation(data, values)
                .then(() => {
                    mutate();
                    refresh();
                })
                .finally(() => {
                    setIsSubmitting(false);
                    setEditLocationModalOpen(false);
                });
        }
    };

    const onDeleteLocation = () => {
        if (data) {
            deleteLocation(data).then(() => {
                router.replace(`${Slug.MANAGE_CUSTOMERS}/${params.customerId}`);
            });
            setDeleteLocationModalOpen(false);
        }
    };

    const onCreateTank = (data: Partial<ITank>) => {
        toast.promise(
            triggerCreateTank({ ...data, locationId: params.locationId }),
            {
                loading: CommonMessages.Creating,
                error: (err) => err.message,
                success: ({ data }: { data: ITank }) => {
                    router.push(
                        `${Slug.TANKS}/${data.id}?callbackUrl=${pathname}`
                    );
                    return CommonMessages.CreatedSuccessful;
                },
            }
        );
    };

    const closeModel = () => setEditLocationModalOpen(false);

    return (
        <>
            <div
                className={flex({
                    flex: 1,
                    direction: 'column',
                })}
            >
                <TopBar
                    onPrimaryClick={() => {
                        setNewTankModalOpen(true);
                    }}
                    onBack={() => {
                        router.back();
                    }}
                    title={
                        data
                            ? `${data.postalCode} - ${data.blockNo} ${data.street}`
                            : ''
                    }
                    primaryText="New tank"
                />
                <div
                    className={flex({
                        flex: 1,
                        direction: 'column',
                    })}
                >
                    <LocationHeader
                        location={data}
                        onEdit={() => {
                            setEditLocationModalOpen(true);
                        }}
                        onDelete={() => setDeleteLocationModalOpen(true)}
                    />
                    <LocationTanksMetadata
                        onChange={debounce((value) => {
                            setFilter({ ...filter, page: 1, keyword: value });
                        }, 800)}
                        statisticType={tanks?.statisticType}
                    />
                    <TankList
                        loading={tankListLoading}
                        customerId={params.customerId}
                        data={tanks?.records ?? []}
                        canLoadMore={canNext}
                        onLoadMore={next}
                    />
                </div>
            </div>
            <ConfirmModal
                open={deleteLocationModalOpen}
                onCancel={() => {
                    setDeleteLocationModalOpen(false);
                }}
                onConfirm={onDeleteLocation}
                title="Alert"
                message={format(CommonMessages.DeletingAlert, 'location')}
            />
            <Modal
                isOpen={newTankModalOpen}
                onClose={() => {
                    setNewTankModalOpen(false);
                }}
                sizes="md"
                fullWidth
                title="Create new tank"
            >
                <TankForm
                    onSubmit={onCreateTank}
                    isLoading={false}
                    materialAllows={materialAllows}
                />
            </Modal>
            <Modal
                isOpen={editLocationModalOpen}
                onClose={() => {
                    if (!isSubmitting) {
                        closeModel();
                    }
                }}
                title="Update location"
                sizes="sm"
            >
                <NewLocationForm
                    isUpdate
                    isLoading={isSubmitting}
                    onSubmit={onUpdateLocation}
                    defaultValues={{
                        blockNo: data?.blockNo,
                        building: data?.building,
                        postalCode: data?.postalCode,
                        street: data?.street,
                        location: {
                            lat: Number(`${data?.lat || 0}`),
                            lng: Number(`${data?.long || 0}`),
                        },
                    }}
                />
            </Modal>
        </>
    );
}
