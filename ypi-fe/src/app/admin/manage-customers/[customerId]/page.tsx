'use client';

import TopBar from '@/components/manage-customers/TopBar';
import { box, flex } from '@/styled-system/patterns';
import { useRouter } from 'next/navigation';
import { contentContainer, infoContainer, projectListContainer } from './style';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import ProjectLocationList from '@/components/manage-customers/ProjectLocationList';
import Tabs from '@/components/globals/Tabs';
import { useState } from 'react';
import CustomerInfo from '@/components/manage-customers/CustomerInfo';
import Button from '@/components/globals/Button';
import ScrollView from '@/components/globals/ScrollView';
import AgentInfo from '@/components/manage-customers/AgentInfo';
import { useActivateCustomer, useCustomerById } from '@/hooks/customer';
import Modal from '@/components/globals/Modal';
import NewLocationForm from '@/components/manage-customers/NewLocationForm';
import { INewProjectLocationParams } from '@/utilities/types/entities/project-location';
import { useCreateLocation, useLocationList } from '@/hooks/location';
import SearchInput from '@/components/globals/SearchInput';
import { Slug } from '@/utilities/types/enums/Slug';
import ConfirmModal from '@/components/globals/ConfirmModal';
import { format } from '@/utilities/globals/function/string';
import CommonMessages from '@/utilities/messages/common';
import { debounce } from 'lodash';

type Props = {
    params: { customerId: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

const defaultTabs = [
    {
        key: 'detail',
        title: 'Detail',
    },
];
const tabsWithAgent = [
    {
        key: 'detail',
        title: 'Detail',
    },
    {
        key: 'agent',
        title: 'Agent',
    },
];

export default function CustomerDetailPage({ params }: Props) {
    const router = useRouter();
    const { data, mutate: refetchCustomer } = useCustomerById(
        params.customerId
    );
    const [filter, setFilter] = useState<Record<string, any>>({
        keyword: '',
        userId: params.customerId,
    });
    const {
        data: locations,
        isLoading,
        canNext,
        next,
        refresh,
    } = useLocationList(params.customerId, filter);
    const createLocation = useCreateLocation(params.customerId);
    const [createModelOpen, setCreateModelOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [currentTab, setCurrentTab] = useState<'detail' | 'agent'>('detail');
    const [activateCustomerModalOpen, setActivateCustomerModalOpen] =
        useState(false);

    const activateCustomer = useActivateCustomer();

    const onActivateCustomer = () => {
        if (data) {
            activateCustomer(data?.id || '', { isActive: !data.isActive }).then(
                () => {
                    refetchCustomer();
                    setActivateCustomerModalOpen(false);
                    router.replace(Slug.MANAGE_CUSTOMERS);
                }
            );
        }
    };

    const onSubmit = (data: Partial<INewProjectLocationParams>) => {
        setIsSubmitting(true);
        createLocation(data)
            .then(() => {
                closeModel();
                refresh();
            })
            .finally(() => {
                setIsSubmitting(false);
            });
    };

    const closeModel = () => setCreateModelOpen(false);

    return (
        <>
            <div className={projectListContainer}>
                <TopBar
                    onPrimaryClick={() => {
                        setCreateModelOpen(true);
                    }}
                    onSecondaryClick={() => {}}
                    onBack={() => {
                        router.back();
                    }}
                    title={data?.name}
                    primaryText="New location"
                    // secondaryText="General report"
                    // secondaryButtonVisible
                />
                <div className={contentContainer}>
                    <div className={infoContainer}>
                        <Tabs
                            selectedTabKey={currentTab}
                            fullWidth
                            onChange={(key) =>
                                setCurrentTab(
                                    key === 'agent' ? 'agent' : 'detail'
                                )
                            }
                            tabs={data?.agent ? tabsWithAgent : defaultTabs}
                        />
                        <ScrollView>
                            {currentTab === 'detail' ? (
                                <CustomerInfo customer={data} />
                            ) : null}
                            {currentTab === 'agent' ? (
                                <AgentInfo customer={data} />
                            ) : null}
                        </ScrollView>
                        <div className={flex({ gap: '12px', pt: 2 })}>
                            <Button
                                visual={
                                    data?.isActive
                                        ? 'solid_inactive'
                                        : 'solid_active'
                                }
                                onClick={() => {
                                    if (data)
                                        setActivateCustomerModalOpen(true);
                                }}
                                className={css({ flex: 1 })}
                            >
                                {data?.isActive ? 'Inactive' : 'Active'}
                            </Button>
                            <Button
                                href={`${Slug.MANAGE_CUSTOMERS}/${params.customerId}/update`}
                                visual="outline_primary"
                                className={css({ flex: 1 })}
                            >
                                Edit
                            </Button>
                        </div>
                    </div>
                    <div
                        className={flex({
                            position: 'relative',
                            width: 'calc(100% - 264px)',
                            overflowX: 'hidden',
                            flex: 1,
                            flexDirection: 'column',
                            gap: '24px',
                            pointerEvents: data?.isActive ? 'auto' : 'none',
                        })}
                    >
                        {!data?.isActive && (
                            <div
                                className={box({
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    backgroundColor:
                                        'rgba(255, 255, 255, 0.5)' /* Semi-transparent black overlay */,
                                    zIndex: 10 /* Ensure overlay is above other content */,
                                })}
                            />
                        )}
                        <div
                            className={flex({
                                alignItems: 'center',
                                justifyContent: 'space-between',
                            })}
                        >
                            <Typography
                                typography="subtitle_24"
                                color="primary_100"
                            >
                                Project Location
                            </Typography>
                            <SearchInput
                                keyword={filter.keyword}
                                onChange={debounce(
                                    (keyword) =>
                                        setFilter({
                                            ...filter,
                                            keyword,
                                        }),
                                    800
                                )}
                            />
                        </div>
                        <div
                            className={flex({ width: '100%', flex: 1, mt: 3 })}
                        >
                            <ProjectLocationList
                                loading={isLoading}
                                customerId={params.customerId}
                                data={locations?.records ?? []}
                                onLoadMore={next}
                                canLoadMore={canNext}
                            />
                        </div>
                    </div>
                </div>
            </div>
            <ConfirmModal
                title={
                    !data?.isActive ? 'Active Customer' : 'Inactive Customer'
                }
                open={activateCustomerModalOpen}
                confirmText="Ok"
                message={format(
                    data?.isActive
                        ? CommonMessages.DeactivateAlert
                        : CommonMessages.ActivateAlert,
                    'customer'
                )}
                onCancel={() => {
                    setActivateCustomerModalOpen(false);
                }}
                onConfirm={onActivateCustomer}
            />
            <Modal
                isOpen={createModelOpen}
                onClose={() => {
                    if (!isSubmitting) {
                        closeModel();
                    }
                }}
                title="Create new location"
                sizes="md"
            >
                <NewLocationForm isLoading={isSubmitting} onSubmit={onSubmit} />
            </Modal>
        </>
    );
}
