import { flex } from '@/styled-system/patterns';

export const contentContainer = flex({
    width: '100%',
    flex: 1,
    gap: '32px',
    pb: 4,
});
export const infoContainer = flex({
    width: 296,
    direction: 'column',
    p: 3,
    pt: 2,
    borderRadius: 10,
    borderWidth: '1.5px',
    borderStyle: 'solid',
    borderColor: 'primary.100',
});
export const projectListContainer = flex({
    flex: 1,
    direction: 'column',
    gap: '48px',
});
// export const projectLocationListTitle = flex({ flex: 1, direction: 'column' });
