import Toaster from '@/components/Toaster';
import { Provider } from '@/provider';
import { authOptions } from '@/utilities/lib/auth';
import { getServerSession } from 'next-auth';
import { Montserrat } from 'next/font/google';
import React from 'react';
import './globals.css';

const inter = Montserrat({ subsets: ['latin'] });

export const metadata = {
    title: 'YPI Admin',
};

export default async function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const session = await getServerSession(authOptions);

    return (
        <html lang="en">
            <body className={inter.className}>
                <Provider session={session}>{children}</Provider>
                <Toaster />
            </body>
        </html>
    );
}
