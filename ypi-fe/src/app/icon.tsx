import { ImageResponse } from 'next/og';
import Favicon from '@/components/globals/Icons/Favicon';

// Route segment config
export const runtime = 'edge';

// Image metadata
export const size = {
    width: 120,
    height: 120,
};
export const contentType = 'image/png';

// Image generation
export default function Icon() {
    return new ImageResponse(
        (
            // ImageResponse JSX element
            <div
                style={{
                    background: '#E5E7EB',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                }}
            >
                <Favicon width={120} height={120} />
            </div>
        ),
        // ImageResponse options
        {
            // For convenience, we can re-use the exported icons size metadata
            // config to also set the ImageResponse's width and height.
            ...size,
        }
    );
}
