version: 2.1

jobs:
    build_dev:
        docker:
            - image: cimg/node:20.4.0-browsers
        resource_class: large
        steps:
            - run:
                  shell: /bin/sh
                  command: |
                      sudo apt update
                      sudo apt install git -y
                      git --version
            - run:
                  name: Install rsync
                  command: |
                      sudo apt-get install rsync
            - checkout
            - restore_cache:
                  keys:
                      - npm-packages-{{ checksum "package-lock.json" }}
            - run:
                  name: Checkout development branch
                  command: |
                      git checkout development
                      git pull origin development
            - run:
                  name: Install dependencies
                  command: |
                      npm ci
            - save_cache:
                  paths:
                      - node_modules
                  key: npm-packages-{{ checksum "package-lock.json" }}
            - run:
                  name: Create .env file
                  command: |
                      echo "APP_API_HOST=${APP_API_HOST}" >> .env
                      echo "APP_HOSTNAME=${APP_HOSTNAME}" >> .env
                      echo "NEXTAUTH_SECRET=${NEXTAUTH_SECRET}" >> .env
            - run:
                  name: Build
                  command: |
                      export NODE_OPTIONS=--max-old-space-size=8192
                      npm run build
            - run:
                  name: Change port in server.js
                  command: |
                      sed -i 's/const currentPort = parseInt(process.env.PORT, 10) || 3000/const currentPort = 3030/g' ./.next/standalone/server.js
            - add_ssh_keys
            - run:
                  name: Add the server to known hosts
                  command: |
                      ssh-keyscan -H ${SERVER_DEV} >> ~/.ssh/known_hosts
            - run:
                  name: Upload files to stand alone files to server
                  command: |
                      rsync -avce ssh --delete ./.next/standalone/. ubuntu@${SERVER_DEV}:/var/www/ypi-fe
            - run:
                  name: Upload static files to server
                  command: |
                      rsync -avce ssh --delete ./.next/static/. ubuntu@${SERVER_DEV}:/var/www/ypi-fe/.next/static
            - run:
                  name: Run migration and restart server
                  command: |
                      ssh ubuntu@${SERVER_DEV} "source ~/.nvm/nvm.sh && cd /var/www/ypi-fe && ls -la && source ~/.bashrc && pm2 restart ypi-fe"

    build_prod:
        docker:
            - image: cimg/deploy:2024.08-node
        steps:
            - setup_remote_docker
            - checkout
            - run:
                  name: Build Docker image
                  command: |
                      docker build --build-arg GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY} -t ypi-fe -f Dockerfile .
            - run:
                  name: Save Docker image to tar file
                  command: |
                      docker save ypi-fe -o ypi-fe.tar
            - persist_to_workspace:
                  root: .
                  paths:
                      - ypi-fe.tar

    deploy_prod:
        docker:
            - image: cimg/base:stable
        steps:
            - setup_remote_docker
            - attach_workspace:
                  at: .
            - run:
                  name: Load image to docker
                  command: |
                      docker load -i ypi-fe.tar
            - run:
                  name: Login to docker hub
                  command: |
                      docker login -u ${DOCKER_USER} -p ${DOCKER_PASS}
            - run:
                  name: Push to docker hub
                  command: |
                      docker tag ypi-fe:latest ${DOCKER_USER}/ypi-fe:latest
                      docker push ${DOCKER_USER}/ypi-fe:latest
            - add_ssh_keys
            - run:
                  name: Add the server to known hosts
                  command: |
                      ssh-keyscan -H ${SERVER_PROD} >> ~/.ssh/known_hosts
            - run:
                  name: Run migration and restart server
                  command: |
                      ssh ubuntu@${SERVER_PROD} "sudo docker login -u ${DOCKER_USER} -p ${DOCKER_PASS} && cd ypi && sudo docker compose pull && sudo docker compose up -d"

workflows:
    build-and-deploy-dev:
        jobs:
            - build_dev:
                  filters:
                      branches:
                          only: development

    build-and-deploy-prod:
        jobs:
            - build_prod:
                  filters:
                      branches:
                          only: production
            - deploy_prod:
                  requires:
                      - build_prod
                  filters:
                      branches:
                          only: production
